# Me? Reincarnated?

An isekai text adventure game where you are reincarnated as a creature in a fantasy world and evolve through your choices and actions.

## Features

### Core Gameplay
- **Enhanced Character Creation**: Immersive backstory scenarios explaining your death and reincarnation into a fantasy world
- **Expanded Creature Selection**: Choose from 6 unique starting creatures, each with distinct abilities and evolution paths
- **AI-Powered Storytelling**: Uses Google's Gemini API with advanced consistency system for high-quality narrative generation
- **Turn-Based Combat**: Strategic combat with attack, defend, skill, and item options
- **Experience & Leveling**: Gain experience through combat, exploration, and skill usage
- **Evolution System**: Grow from a weak creature to powerful being through multiple evolution paths
- **World Exploration**: 7 unique locations with distinct encounters, NPCs, and secrets

### Innovative Skill System
- **Skill Fusion**: Combine related abilities into powerful merged skills
  - Heat + Cold Resistance → Temperature Regulation
  - Web Spin + Poison Bite → Venomous Web Trap
  - Leadership + Cunning → Tactical Genius
- **Cross-Creature Compatibility**: Some fusions work across different creature types
- **Automatic Discovery**: Learn fusions by acquiring compatible abilities
- **<PERSON>ward Skill System**: Earn special skills through achievements and milestones
  - Story Progression: First Evolution, Survivor, Apex Predator
  - Quest Completion: Monster Hunter, Explorer, Champion
  - Relationship Growth: Trusted Ally, Pack Leader, Diplomat
  - Achievement-Based: Glutton, Hoarder, Scholar, Fusion Master
- **Legendary Fusion Skills**: Combine reward skills for ultimate abilities
  - Ultimate Survivor (Survivor + Veteran Survivor + Apex Predator)
  - War Chief (Champion + Pack Leader + Trusted Ally)
  - Master Explorer (Explorer + Treasure Hunter + Scholar)

### Advanced Systems
- **Response Consistency Engine**: Advanced AI response processing for consistent, high-quality narrative
  - Intelligent post-processing removes artifacts and ensures proper formatting
  - Narrative continuity tracking maintains story flow across interactions
  - Character state analysis provides mood-aware responses
  - Dynamic prompt engineering adapts to different action types
- **Two-Tiered Memory**: Short-term memory for recent events, long-term memory for important world state
- **Persistent Save System**: Save and load your progress with automatic checkpoints
- **Relationship Tracking**: Build relationships with NPCs that affect the story
- **Inventory & Equipment**: Manage items, weapons, armor, and consumables
- **Modern GUI**: Clean, dark-themed interface built with CustomTkinter

## Installation

1. **Clone or download** this repository to your local machine

2. **Install Python 3.8+** if you haven't already

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the game**:
   ```bash
   python main.py
   ```

## Starting Creatures

Choose from six unique starting forms, each with different abilities and evolution paths:

### **Slime** 🟢
- **Stats**: HP:50 MP:30 ATK:5 DEF:8 SPD:3
- **Strengths**: High adaptability and defense, can absorb abilities from defeated enemies
- **Evolution Paths**: Elemental Slime, King Slime, Mimic Slime
- **Gameplay Style**: Defensive tank with absorption mechanics and high adaptability

### **Spider** 🕷️
- **Stats**: HP:30 MP:40 ATK:8 DEF:4 SPD:9
- **Strengths**: High speed and magical potential, excels at stealth and web-based tactics
- **Evolution Paths**: Arachne, Widow Spider, Phase Spider
- **Gameplay Style**: Tactical controller with trap-based combat and hit-and-run tactics

### **Goblin** 👹
- **Stats**: HP:40 MP:20 ATK:7 DEF:5 SPD:6
- **Strengths**: Balanced stats with tool use, natural leadership and pack tactics
- **Evolution Paths**: Hobgoblin, Goblin Shaman, Goblin King
- **Gameplay Style**: Versatile all-rounder with crafting, social, and tactical abilities

### **Wisp** ✨
- **Stats**: HP:25 MP:60 ATK:6 DEF:2 SPD:8
- **Strengths**: Pure magical energy with light manipulation and high MP
- **Evolution Paths**: Elemental Wisp, Guardian Spirit, Arcane Wisp
- **Gameplay Style**: Glass cannon mage with high magical damage and utility spells

### **Rat** 🐀
- **Stats**: HP:35 MP:25 ATK:6 DEF:3 SPD:10
- **Strengths**: Highest speed, keen senses, and excellent survival instincts
- **Evolution Paths**: Dire Rat, Plague Rat, Shadow Rat
- **Gameplay Style**: Fast scout with stealth abilities and environmental awareness

### **Mushroom** 🍄
- **Stats**: HP:45 MP:35 ATK:4 DEF:7 SPD:2
- **Strengths**: Natural healing, spore-based abilities, and nature magic
- **Evolution Paths**: Mycelium Network, Toxic Mushroom, Healing Mushroom
- **Gameplay Style**: Support healer with area control and nature magic

## Backstory Scenarios

Experience your death and reincarnation through one of five immersive scenarios:

### **Overwork Death** 💼
Your dedication to work ultimately led to your demise from exhaustion. Suggested for analytical, hardworking characters.

### **Heroic Sacrifice** 🦸
You died saving someone from danger, earning you a chance at a new heroic life. Perfect for brave, selfless characters.

### **Accident** 🚗
An unexpected accident cut your life short, but every ending is a new beginning. Ideal for curious, adaptable characters.

### **Natural Causes** 🏥
You lived a full life before passing peacefully, carrying wisdom into your new existence. Best for patient, experienced characters.

### **Gaming Accident** 🎮
A classic isekai scenario - you died while gaming and now get to experience the real thing. Great for strategic, competitive characters.

Each scenario influences suggested traits and occupations, creating a cohesive character background that flows naturally into your creature selection.

## Game Progression

1. **Backstory Phase**: Experience your death and reincarnation through immersive narrative scenarios
2. **Character Creation**: Choose your name, traits, occupation, and starting creature form
3. **Awakening**: First moments in the fantasy world as your chosen creature
4. **Early Game**: Learn basic survival, discover abilities, meet first NPCs
5. **Mid Game**: Evolution choices, skill fusion mastery, faction relationships
6. **Late Game**: Kingdom management, complex storylines, multiple endings

## Controls

- **Text Input**: Type your actions and press Enter or click Submit
- **Menu Buttons**: Use the right panel for game management (Save, Load, New Game)
- **Status Panel**: View character stats, abilities, and current status

## Development

### Running Tests

```bash
pytest tests/ -v
```

### Project Structure

```
Me Reincarnated/
├── main.py                 # Entry point
├── config.py              # Game configuration
├── requirements.txt       # Dependencies
├── api/
│   └── gemini_client.py   # Google Gemini API integration with response consistency engine
├── game/
│   ├── game_engine.py     # Core game logic
│   ├── character.py       # Character management
│   ├── lore_system.py     # Backstory scenarios and world lore
│   ├── skill_system.py    # Skill fusion and learning systems
│   ├── reward_skill_system.py # Achievement-based reward skills
│   ├── evolution_system.py # Creature evolution mechanics
│   ├── memory_system.py   # Two-tiered memory system
│   └── save_system.py     # Save/load functionality
├── ui/
│   └── main_window.py     # Main GUI window
├── tests/                 # Unit tests including response consistency tests
├── docs/                  # Documentation
│   └── RESPONSE_CONSISTENCY.md # Detailed consistency system documentation
├── data/                  # Game data files
│   └── creatures_database.py # Detailed creature information and fusion specialties
├── saves/                 # Save game files
└── assets/               # Game assets (future)
```

## API Configuration

The game uses Google's Gemini API for text generation with an advanced response consistency system. The API key is configured in `config.py`.

### Response Quality Settings

The consistency engine can be configured in `config.py`:

```python
# Response Consistency Settings
RESPONSE_MIN_LENGTH = 50          # Minimum response length
RESPONSE_MAX_LENGTH = 800         # Maximum response length
ENABLE_RESPONSE_VALIDATION = True # Enable post-processing
ENABLE_CONTEXT_CONTINUITY = True  # Enable narrative continuity
CONSISTENCY_TEMPERATURE = 0.6     # Lower temperature for consistency
```

**Note**: The current API key is for development purposes. For production use, you should:
1. Get your own API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Replace the key in `config.py` or use environment variables

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed with `pip install -r requirements.txt`

2. **API Errors**: Check your internet connection and API key validity

3. **GUI Issues**: Ensure you're running Python 3.8+ and have tkinter support

4. **Save/Load Problems**: Check that the `saves/` directory exists and is writable

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify all dependencies are correctly installed
3. Make sure you have a stable internet connection for API calls

## Recent Updates

### Version 0.5.0 - Reward Skill System & Achievement Tracking (Current)
- **Reward Skill System**: Automatic skill rewards based on achievements and milestones
  - 16 unique reward skills across 4 categories (Story, Quest, Relationship, Achievement)
  - Real-time progress tracking for all reward criteria
  - Integration with existing skill fusion system
- **Legendary Fusion Skills**: 4 ultimate skills created by combining reward skills
  - Ultimate Survivor: Legendary endurance and dominance
  - War Chief: Supreme combat leadership abilities
  - Master Explorer: Unparalleled discovery and learning
  - Legendary Collector: Master of acquisition and fusion
- **Enhanced Achievement Tracking**: Comprehensive progress monitoring
  - Combat victories, unique enemies defeated, evolution milestones
  - Quest completion, location discovery, relationship building
  - Item collection, food consumption, skill learning progress
- **Persistent Reward Progress**: Save/load system includes reward tracking
- **Fusion Integration**: Reward skills participate in existing fusion mechanics

### Version 0.4.0 - Circumstantial Skills & Beta Milestone
- **Circumstantial Skill System**: 50+ new environmental and situational skills
  - Eating glowing mushrooms unlocks Bioluminescence and Toxin Resistance
  - Drinking from magical springs grants Purification and Mana Regeneration
  - Touching ancient artifacts provides Artifact Sense and Ancient Knowledge
  - Weather exposure builds elemental resistances and affinities
  - Creature-specific environmental interactions (spore release, light manipulation, etc.)
- **Advanced Skill Fusion**: 10 new fusion combinations for circumstantial skills
  - Magical Metabolism (Magical Digestion + Mana Absorption)
  - Elemental Affinity (Fire + Ice + Lightning Affinity)
  - Mystic Scholar (Ancient Languages + Forbidden Knowledge + Scholarly Mind)
  - And many more unique combinations
- **Progressive Difficulty Scaling**: Skill learning requirements increase with character progression
- **Expanded Skill Database**: 70+ total learnable skills across all tiers and categories
- **Enhanced Beta Features**: Reduced learning requirements, improved skill discovery hints

### Version 0.3.1 - Response Consistency Engine
- **Enhanced AI Quality**: Advanced response processing system ensures consistent, high-quality narrative
- **Narrative Continuity**: Intelligent tracking of recent events for better story flow
- **Character State Awareness**: Responses now consider your creature's physical and mental condition
- **Dynamic Prompts**: Action-specific prompt templates for combat, exploration, social, and skill usage
- **Configurable Settings**: Fine-tune response quality with new configuration options

### Version 0.3.0 - Enhanced Character Creation & Expanded Creatures
- **Immersive Backstories**: Five detailed death/reincarnation scenarios
- **Expanded Creature Selection**: Six unique starting creatures with distinct evolution paths
- **Advanced Skill Fusion**: Combine abilities into powerful merged skills
- **Comprehensive Testing**: Full test coverage for all major systems

## Future Features

- Visual character portraits using Google's Imagen API
- Expanded evolution trees with branching paths
- Kingdom building mechanics
- Multiplayer interactions
- Achievement system
- Sound effects and music

## License

This project is for educational and personal use. Please respect the terms of service for Google's APIs when using this software.

## Version

Current Version: 0.5.0 (Beta - Reward Skill System & Achievement Tracking)

---

*Embark on your new life and discover what you can become!*
