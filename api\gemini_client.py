"""
Google Gemini API client for text generation
"""
import google.generativeai as genai
from typing import Optional, Dict, Any, List
import config

class GeminiClient:
    def __init__(self):
        """Initialize the Gemini client with API key"""
        genai.configure(api_key=config.GEMINI_API_KEY)
        self.model = genai.GenerativeModel(config.GEMINI_MODEL)

    async def generate_text(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using Gemini API with enhanced consistency

        Args:
            prompt: The main prompt for generation
            context: Optional context to include

        Returns:
            Generated text response with consistency improvements
        """
        try:
            # Combine context and prompt if context is provided
            full_prompt = self._build_consistent_prompt(prompt, context)

            # Use consistency temperature for better response quality
            temperature = getattr(config, 'CONSISTENCY_TEMPERATURE', config.GEMINI_TEMPERATURE)

            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=config.GEMINI_MAX_TOKENS,
                )
            )

            # Post-process response for consistency if enabled
            if getattr(config, 'ENABLE_RESPONSE_VALIDATION', True):
                processed_response = self._post_process_response(response.text)
                return processed_response
            else:
                return response.text

        except Exception as e:
            print(f"Error generating text: {e}")
            return "I'm having trouble thinking right now. Please try again."

    async def generate_character_creation_response(self, user_input: str, stage: str, backstory_context: str = "") -> str:
        """Generate responses for character creation process"""
        base_context = backstory_context if backstory_context else ""

        prompts = {
            "name": f"""You are a mystical entity guiding someone through reincarnation in a fantasy world.
            {base_context}
            The user wants to be called '{user_input}'. Respond warmly and mysteriously, acknowledging their chosen name
            and referencing their past life circumstances when appropriate. Keep it under 100 words.""",

            "traits": f"""The user described their personality as: {user_input}
            {base_context}
            You are guiding their reincarnation. Acknowledge their traits, noting how they connect to their past life,
            and ask about their former occupation. Be mystical and encouraging. Keep it under 100 words.""",

            "occupation": f"""The user's former occupation was: {user_input}
            {base_context}
            You are guiding their reincarnation. Acknowledge their past work and how it shaped them, then transition
            to discussing their new form. Reference their death circumstances and personality. Be mystical and wise. Keep it under 100 words."""
        }

        prompt = prompts.get(stage, "Continue the mystical reincarnation process.")
        return await self.generate_text(prompt)

    async def generate_game_response(self, user_action: str, game_state: Dict[str, Any],
                             short_term_memory: list, world_context: str, action_context: str = "") -> str:
        """Generate game responses based on user actions and current state"""

        # Build context from game state
        character = game_state.get('character', {})
        location = game_state.get('current_location', 'unknown area')

        # Enhanced context building
        context = self._build_enhanced_context(character, location, short_term_memory, world_context)

        # Add action selection context if provided
        if action_context:
            context += f"\n\nACTION CONTEXT: {action_context}"

        # Dynamic prompt based on action type and character state
        prompt = self._build_dynamic_prompt(user_action, character, location)

        return await self.generate_text(prompt, context)

    def _build_enhanced_context(self, character: Dict[str, Any], location: str,
                               short_term_memory: list, world_context: str) -> str:
        """Build enhanced context with personality and progression awareness"""

        # Character personality context
        personality_traits = character.get('traits', [])
        personality_desc = f"Personality: {', '.join(personality_traits)}" if personality_traits else ""

        # Character progression context
        level = character.get('level', 1)
        creature_type = character.get('creature_type', 'Unknown')
        abilities = character.get('abilities', [])

        progression_desc = f"Level {level} {creature_type}"
        if abilities:
            key_abilities = abilities[:3]  # Show first 3 abilities
            progression_desc += f" with abilities: {', '.join(key_abilities)}"

        # Enhanced recent events with narrative continuity
        recent_events = self._build_narrative_continuity(short_term_memory)

        # Character state and mood
        character_state = self._analyze_character_state(character, short_term_memory)

        return f"""
        CHARACTER: {character.get('name', 'Unknown')} - {progression_desc}
        {personality_desc}
        CHARACTER STATE: {character_state}
        LOCATION: {location}
        RECENT NARRATIVE: {recent_events}
        WORLD CONTEXT: {world_context}

        CONSISTENCY NOTES:
        - Maintain character personality throughout responses
        - Reference recent events naturally when relevant
        - Keep power level consistent with character progression
        - Ensure location details match established descriptions
        """

    def _build_narrative_continuity(self, short_term_memory: list) -> str:
        """Build narrative continuity from recent events"""
        if not short_term_memory:
            return "Just awakened in this new world, still adjusting to their reincarnated form."

        # Get last few events and create a flowing narrative
        recent_events = short_term_memory[-5:] if len(short_term_memory) >= 5 else short_term_memory

        # Analyze event types for better continuity
        event_summary = []
        for event in recent_events:
            event_lower = event.lower()
            if any(word in event_lower for word in ['combat', 'fight', 'attack', 'battle']):
                event_summary.append("engaged in combat")
            elif any(word in event_lower for word in ['explore', 'discover', 'find']):
                event_summary.append("made discoveries")
            elif any(word in event_lower for word in ['talk', 'meet', 'speak']):
                event_summary.append("had social interactions")
            elif any(word in event_lower for word in ['learn', 'skill', 'ability']):
                event_summary.append("developed new abilities")
            else:
                event_summary.append("took various actions")

        # Create flowing narrative
        if len(event_summary) == 1:
            return f"Recently {event_summary[0]}."
        elif len(event_summary) == 2:
            return f"Recently {event_summary[0]} and {event_summary[1]}."
        else:
            return f"Recently {', '.join(event_summary[:-1])}, and {event_summary[-1]}."

    def _analyze_character_state(self, character: Dict[str, Any], short_term_memory: list) -> str:
        """Analyze character's current emotional/physical state"""
        # Base state from character stats
        hp_ratio = character.get('hp', 100) / character.get('max_hp', 100)
        mp_ratio = character.get('mp', 100) / character.get('max_mp', 100)

        # Physical condition
        if hp_ratio > 0.8:
            physical_state = "healthy and energetic"
        elif hp_ratio > 0.5:
            physical_state = "somewhat worn but determined"
        elif hp_ratio > 0.2:
            physical_state = "injured but still fighting"
        else:
            physical_state = "badly wounded and struggling"

        # Mental/magical condition
        if mp_ratio > 0.8:
            mental_state = "mentally sharp and magically charged"
        elif mp_ratio > 0.5:
            mental_state = "focused but conserving energy"
        elif mp_ratio > 0.2:
            mental_state = "mentally fatigued"
        else:
            mental_state = "mentally exhausted"

        # Recent activity influence
        recent_text = ' '.join(short_term_memory[-3:]).lower() if short_term_memory else ""
        if 'victory' in recent_text or 'success' in recent_text:
            mood = "confident and accomplished"
        elif 'defeat' in recent_text or 'fail' in recent_text:
            mood = "frustrated but resilient"
        elif 'discover' in recent_text or 'learn' in recent_text:
            mood = "curious and excited"
        else:
            mood = "alert and ready"

        return f"{physical_state}, {mental_state}, feeling {mood}"

    def _build_dynamic_prompt(self, user_action: str, character: Dict[str, Any], location: str) -> str:
        """Build dynamic prompts based on action type and context"""

        base_personality = "You are the game master for an isekai text adventure where the player is a reincarnated creature."

        # Use character and location info for context (avoiding unused parameter warnings)
        creature_type = character.get('creature_type', 'Unknown')
        location_context = f"in {location}" if location else ""

        # Determine action type
        action_lower = user_action.lower()

        if any(word in action_lower for word in ['attack', 'fight', 'combat', 'battle']):
            action_type = "combat"
        elif any(word in action_lower for word in ['explore', 'look', 'search', 'investigate']):
            action_type = "exploration"
        elif any(word in action_lower for word in ['talk', 'speak', 'greet', 'ask']):
            action_type = "social"
        elif any(word in action_lower for word in ['use', 'cast', 'activate']):
            action_type = "skill"
        else:
            action_type = "general"

        # Enhanced dynamic prompts with better consistency
        prompts = {
            "combat": f"""
            {base_personality}

            SCENARIO: The {creature_type} player is engaging in combat {location_context}.
            PLAYER ACTION: {user_action}

            Generate a combat response that includes:
            1. Immediate, visceral description of what happens
            2. Enemy reactions and tactical situation changes
            3. Environmental factors that affect the fight
            4. Clear next action options (attack, defend, use ability, retreat)

            TONE: Exciting but tactical. Emphasize {creature_type}-specific advantages.
            STRUCTURE: Action result → Enemy response → Current situation → Options
            """,

            "exploration": f"""
            {base_personality}

            SCENARIO: The player is exploring and investigating {location_context}.
            PLAYER ACTION: {user_action}

            Generate an exploration response that includes:
            1. Rich sensory details of discoveries (sight, sound, smell, touch)
            2. Hidden elements revealed through careful observation
            3. Potential items, secrets, or encounters uncovered
            4. New paths or investigation opportunities

            TONE: Mysterious and rewarding. Make discoveries feel meaningful.
            STRUCTURE: Discovery description → Hidden details → Findings → Next opportunities
            """,

            "social": f"""
            {base_personality}

            SCENARIO: The {creature_type} player is attempting social interaction {location_context}.
            PLAYER ACTION: {user_action}

            Generate a social response that includes:
            1. NPC reactions considering the player's creature type
            2. Meaningful dialogue that reveals character and world lore
            3. Relationship dynamics and social consequences
            4. Conversation paths and social opportunities

            TONE: Character-driven with distinct NPC personalities.
            STRUCTURE: NPC reaction → Dialogue exchange → Relationship change → Options
            """,

            "skill": f"""
            {base_personality}

            SCENARIO: The {creature_type} player is using abilities or items {location_context}.
            PLAYER ACTION: {user_action}

            Generate a skill usage response that includes:
            1. Dramatic, detailed description of the ability's effect
            2. Environmental and character reactions to the power
            3. Skill mastery hints or fusion opportunities
            4. New possibilities unlocked by this ability

            TONE: Empowering and unique to {creature_type} abilities.
            STRUCTURE: Ability activation → Effect description → Reactions → New opportunities
            """,

            "general": f"""
            {base_personality}

            SCENARIO: General action in the world {location_context}.
            PLAYER ACTION: {user_action}

            Generate a world response that includes:
            1. Clear consequences of the player's action
            2. Vivid description of what the player experiences
            3. Any changes to the environment or situation
            4. Logical next steps or opportunities

            TONE: Engaging isekai adventure with light humor when appropriate.
            STRUCTURE: Action result → Experience description → Situation change → Next options
            """
        }

        return prompts.get(action_type, prompts["general"])

    async def generate_npc_dialogue(self, npc_name: str, npc_personality: str,
                                   player_reputation: int, conversation_context: str) -> str:
        """Generate personality-based NPC dialogue"""

        # Determine relationship tone based on reputation
        if player_reputation > 50:
            tone = "friendly and respectful"
        elif player_reputation > 0:
            tone = "neutral but cautious"
        elif player_reputation > -50:
            tone = "suspicious and wary"
        else:
            tone = "hostile and dismissive"

        prompt = f"""
        Generate dialogue for {npc_name}, an NPC with personality: {npc_personality}

        Context: {conversation_context}
        Player reputation: {player_reputation} (tone should be {tone})

        Create 2-3 lines of dialogue that:
        1. Reflects the NPC's unique personality
        2. Responds appropriately to the player's reputation
        3. Advances the conversation or provides useful information
        4. Feels natural and engaging

        Keep it concise but memorable. Include personality quirks.
        """

        return await self.generate_text(prompt)

    async def generate_dynamic_encounter(self, location: str, character_level: int,
                                       recent_actions: List[str]) -> str:
        """Generate dynamic encounters based on player behavior"""

        # Analyze recent actions for encounter type
        action_analysis = self._analyze_player_behavior(recent_actions)

        prompt = f"""
        Generate a dynamic encounter for a level {character_level} character in {location}.

        Player behavior pattern: {action_analysis}
        Recent actions: {', '.join(recent_actions[-3:]) if recent_actions else 'None'}

        Create an encounter that:
        1. Matches the player's current power level
        2. Responds to their recent behavior patterns
        3. Offers meaningful choices and consequences
        4. Fits the location's theme and atmosphere

        Describe the encounter setup and initial choices available.
        """

        return await self.generate_text(prompt)

    def _analyze_player_behavior(self, recent_actions: List[str]) -> str:
        """Analyze recent player actions to determine behavior pattern"""
        if not recent_actions:
            return "cautious newcomer"

        action_text = ' '.join(recent_actions).lower()

        if action_text.count('attack') + action_text.count('fight') > 2:
            return "aggressive combatant"
        elif action_text.count('explore') + action_text.count('search') > 2:
            return "curious explorer"
        elif action_text.count('talk') + action_text.count('help') > 1:
            return "diplomatic socializer"
        elif action_text.count('sneak') + action_text.count('hide') > 1:
            return "stealthy infiltrator"
        else:
            return "balanced adventurer"

    def _build_consistent_prompt(self, prompt: str, context: Optional[str] = None) -> str:
        """Build a consistent prompt with formatting guidelines"""

        # Base consistency guidelines
        consistency_rules = """
        RESPONSE FORMATTING GUIDELINES:
        - Write in present tense, second person ("You see...", "You feel...")
        - Use clear paragraph breaks for different topics or actions
        - Maintain consistent tone throughout (adventurous but not overly dramatic)
        - End with clear options or next steps when appropriate
        - Keep descriptions vivid but concise
        - Avoid repetitive phrases or redundant information
        - Ensure smooth transitions between ideas
        - Use proper punctuation and grammar
        """

        # Combine context and prompt with consistency rules
        full_prompt = consistency_rules + "\n\n"

        if context:
            full_prompt += f"CONTEXT: {context}\n\n"

        full_prompt += f"TASK: {prompt}\n\n"
        full_prompt += "Generate a response following the formatting guidelines above."

        return full_prompt

    def _post_process_response(self, response: str) -> str:
        """Post-process response for consistency and quality"""
        if not response:
            return "Something seems to have gone wrong. Please try again."

        # Clean up common issues
        processed = response.strip()

        # Remove redundant spacing
        import re
        processed = re.sub(r'\n\s*\n\s*\n', '\n\n', processed)  # Max 2 consecutive newlines
        processed = re.sub(r' +', ' ', processed)  # Remove extra spaces

        # Ensure proper sentence endings
        processed = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', processed)

        # Remove common AI artifacts and formatting issues
        artifacts_to_remove = [
            "Here's what happens:",
            "Here is what happens:",
            "Response:",
            "Output:",
            "Generated response:",
            "SCENARIO:",
            "PLAYER ACTION:",
            "TONE:",
            "STRUCTURE:",
            "Generate a",
            "Generate an",
        ]

        for artifact in artifacts_to_remove:
            processed = processed.replace(artifact, "").strip()

        # Remove numbered list formatting if it appears at the start
        processed = re.sub(r'^1\.\s*', '', processed)

        # Ensure response doesn't start with lowercase (unless intentional)
        if processed and processed[0].islower() and not processed.startswith(('you', 'your')):
            processed = processed[0].upper() + processed[1:]

        # Length validation
        min_length = getattr(config, 'RESPONSE_MIN_LENGTH', 50)
        max_length = getattr(config, 'RESPONSE_MAX_LENGTH', 800)

        if len(processed) < min_length:
            processed += " The world around you seems to pause, waiting for your next move."
        elif len(processed) > max_length:
            # Truncate at the last complete sentence within limit
            sentences = processed.split('. ')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence + '. ') <= max_length:
                    truncated += sentence + '. '
                else:
                    break
            processed = truncated.strip()

        # Add paragraph breaks for better readability if response is long
        if len(processed) > 200 and '\n\n' not in processed:
            # Try to add breaks at natural points
            sentences = processed.split('. ')
            if len(sentences) > 3:
                mid_point = len(sentences) // 2
                processed = '. '.join(sentences[:mid_point]) + '.\n\n' + '. '.join(sentences[mid_point:])

        # Final cleanup - ensure proper ending punctuation
        if processed and not processed[-1] in '.!?':
            processed += '.'

        return processed
