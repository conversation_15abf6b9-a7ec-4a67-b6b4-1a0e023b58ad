"""
Google Imagen API client for character portrait generation
"""
from google import genai
from google.genai import types
from typing import Optional, Dict, Any, List
from PIL import Image
from io import BytesIO
import config

class ImagenClient:
    def __init__(self):
        """Initialize the Imagen client with API key"""
        # Initialize the new Google GenAI client
        self.client = genai.Client(api_key=config.GEMINI_API_KEY)

        # Fallback to Imagen 3 if available (paid tier)
        self.imagen3_available = getattr(config, 'IMAGEN3_ENABLED', False)

    async def generate_character_portrait(self, character_data: Dict[str, Any],
                                        evolution_stage: str = "base") -> Optional[bytes]:
        """
        Generate a character portrait based on character data

        Args:
            character_data: Dictionary containing character information
            evolution_stage: Current evolution stage (base, evolved, etc.)

        Returns:
            Image bytes if successful, None if failed
        """
        try:
            # Build character-specific prompt
            prompt = self._build_character_prompt(character_data, evolution_stage)

            # Use Imagen 3 for image generation
            response = self.client.models.generate_images(
                model='imagen-3.0-generate-002',
                prompt=prompt,
                config=types.GenerateImagesConfig(
                    number_of_images=1,
                    aspect_ratio="1:1",  # Square portraits
                    person_generation="ALLOW_ADULT"
                )
            )

            # Extract image from response
            if response.generated_images:
                return response.generated_images[0].image.image_bytes

            return None

        except Exception as e:
            print(f"Error generating character portrait: {e}")
            return None

    async def generate_imagen3_portrait(self, character_data: Dict[str, Any],
                                      evolution_stage: str = "base") -> Optional[List[bytes]]:
        """
        Generate character portraits using Imagen 3 (paid tier)

        Args:
            character_data: Dictionary containing character information
            evolution_stage: Current evolution stage

        Returns:
            List of image bytes if successful, None if failed
        """
        if not self.imagen3_available:
            return None

        try:
            # Build character-specific prompt for Imagen 3
            prompt = self._build_imagen3_prompt(character_data, evolution_stage)

            # Use the new Google GenAI client for Imagen 3
            response = self.client.models.generate_images(
                model='imagen-3.0-generate-002',
                prompt=prompt,
                config=types.GenerateImagesConfig(
                    number_of_images=1,
                    aspect_ratio="1:1",  # Square portraits
                    person_generation="ALLOW_ADULT"
                )
            )

            images = []
            for generated_image in response.generated_images:
                images.append(generated_image.image.image_bytes)

            return images

        except Exception as e:
            print(f"Error generating Imagen 3 portrait: {e}")
            return None

    async def generate_image_from_prompt(self, prompt: str) -> Optional[bytes]:
        """
        Generate an image from a custom prompt

        Args:
            prompt: Custom text prompt for image generation

        Returns:
            Image bytes if successful, None if failed
        """
        try:
            # Use Imagen 3 for image generation
            response = self.client.models.generate_images(
                model='imagen-3.0-generate-002',
                prompt=prompt,
                config=types.GenerateImagesConfig(
                    number_of_images=1,
                    aspect_ratio="1:1"
                )
            )

            # Extract image from response
            if response.generated_images:
                return response.generated_images[0].image.image_bytes

            return None

        except Exception as e:
            print(f"Error generating image from prompt: {e}")
            return None

    def _build_character_prompt(self, character_data: Dict[str, Any],
                              evolution_stage: str) -> str:
        """Build a detailed prompt for character portrait generation"""

        # Extract character information
        name = character_data.get('name', 'Unknown')
        creature_type = character_data.get('creature_type', 'Unknown')
        traits = character_data.get('traits', [])
        occupation = character_data.get('occupation', '')
        level = character_data.get('level', 1)

        # Get creature-specific appearance details
        appearance_details = self._get_creature_appearance(creature_type, evolution_stage)

        # Build trait descriptions
        trait_descriptions = self._build_trait_descriptions(traits)

        # Construct the prompt
        prompt = f"""Generate a detailed character portrait of {name}, a {creature_type}

Character Details:
- Type: {creature_type} ({evolution_stage} form)
- Level: {level}
- Occupation: {occupation}
- Traits: {', '.join(traits)}

Appearance: {appearance_details}

Personality Traits: {trait_descriptions}

Style: Fantasy art, detailed character portrait, anime-inspired, high quality digital art,
fantasy creature design, expressive eyes, dynamic pose, magical atmosphere.

The portrait should be a close-up or medium shot showing the character's personality
and unique features. Include subtle magical effects or aura that reflects their abilities.
Make it suitable for a fantasy RPG character portrait.
"""

        return prompt

    def _build_imagen3_prompt(self, character_data: Dict[str, Any],
                            evolution_stage: str) -> str:
        """Build a more concise prompt optimized for Imagen 3"""

        creature_type = character_data.get('creature_type', 'Unknown')
        traits = character_data.get('traits', [])

        # Get creature-specific appearance
        appearance = self._get_creature_appearance(creature_type, evolution_stage)

        # Build concise prompt (Imagen 3 has 480 token limit)
        prompt = f"""Fantasy character portrait: {creature_type} {evolution_stage}, {appearance}.
        Traits: {', '.join(traits[:3])}. High quality digital art, fantasy RPG style,
        detailed character design, magical atmosphere, portrait composition."""

        return prompt

    def _get_creature_appearance(self, creature_type: str, evolution_stage: str) -> str:
        """Get appearance description based on creature type and evolution stage"""

        appearances = {
            "Slime": {
                "base": "translucent gelatinous blob with a cute core, semi-transparent body with magical sparkles",
                "evolved": "larger, more defined form with elemental properties, crystalline core, flowing magical energy"
            },
            "Spider": {
                "base": "small arachnid with intelligent eyes, sleek black body, delicate web patterns",
                "evolved": "elegant spider-humanoid hybrid, multiple arms, intricate web designs, mystical markings"
            },
            "Goblin": {
                "base": "small green humanoid with pointed ears, mischievous expression, simple clothing",
                "evolved": "taller, more refined goblin with ornate clothing, intelligent eyes, tribal markings"
            },
            "Wisp": {
                "base": "floating orb of pure light energy, ethereal glow, wispy magical trails",
                "evolved": "complex light formation with geometric patterns, intense magical aura, prismatic effects"
            },
            "Rat": {
                "base": "agile rodent with keen eyes, sleek fur, alert posture",
                "evolved": "larger, more intelligent rat with mystical markings, enhanced features, magical accessories"
            },
            "Mushroom": {
                "base": "sentient fungus with a cap head, earthy colors, spore effects around body",
                "evolved": "larger mushroom being with multiple caps, bioluminescent patterns, nature magic aura"
            }
        }

        creature_appearances = appearances.get(creature_type, {
            "base": f"mystical {creature_type.lower()} creature with magical properties",
            "evolved": f"evolved {creature_type.lower()} with enhanced magical abilities"
        })

        return creature_appearances.get(evolution_stage, creature_appearances["base"])

    def _build_trait_descriptions(self, traits: List[str]) -> str:
        """Convert character traits into visual descriptions"""

        trait_visuals = {
            "Brave": "confident posture, determined expression",
            "Curious": "inquisitive eyes, alert stance",
            "Cautious": "watchful gaze, defensive posture",
            "Aggressive": "fierce expression, intimidating stance",
            "Peaceful": "calm demeanor, serene expression",
            "Intelligent": "wise eyes, thoughtful expression",
            "Strong": "muscular build, powerful stance",
            "Agile": "lithe form, graceful posture",
            "Magical": "mystical aura, glowing effects",
            "Cunning": "sly expression, calculating gaze"
        }

        descriptions = []
        for trait in traits:
            if trait in trait_visuals:
                descriptions.append(trait_visuals[trait])

        return ", ".join(descriptions) if descriptions else "balanced and harmonious appearance"

    def save_portrait(self, image_bytes: bytes, character_name: str,
                     evolution_stage: str = "base") -> str:
        """
        Save portrait image to disk

        Args:
            image_bytes: Raw image data
            character_name: Character name for filename
            evolution_stage: Evolution stage for filename

        Returns:
            File path where image was saved
        """
        try:
            # Create portraits directory if it doesn't exist
            portraits_dir = config.ASSETS_DIR / "portraits"
            portraits_dir.mkdir(exist_ok=True)

            # Generate filename
            safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_name}_{evolution_stage}.png"
            filepath = portraits_dir / filename

            # Convert bytes to PIL Image and save
            image = Image.open(BytesIO(image_bytes))
            image.save(filepath, "PNG")

            return str(filepath)

        except Exception as e:
            print(f"Error saving portrait: {e}")
            return ""

    def load_portrait(self, character_name: str, evolution_stage: str = "base") -> Optional[Image.Image]:
        """
        Load existing portrait from disk

        Args:
            character_name: Character name
            evolution_stage: Evolution stage

        Returns:
            PIL Image if found, None otherwise
        """
        try:
            portraits_dir = config.ASSETS_DIR / "portraits"
            safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_name}_{evolution_stage}.png"
            filepath = portraits_dir / filename

            if filepath.exists():
                return Image.open(filepath)

            return None

        except Exception as e:
            print(f"Error loading portrait: {e}")
            return None
