"""
Pytest configuration for Me? Reincarnated? tests
"""
import pytest
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@pytest.fixture
def sample_character_data():
    """Sample character data for testing"""
    return {
        "name": "TestHero",
        "traits": ["brave", "curious"],
        "occupation": "programmer",
        "creature_type": "Slime",
        "level": 1,
        "experience": 0,
        "evolution_stage": 0,
        "stats": {
            "hp": 50,
            "max_hp": 50,
            "mp": 30,
            "max_mp": 30,
            "attack": 5,
            "defense": 8,
            "speed": 3
        },
        "abilities": ["Absorb", "Acid Resistance"],
        "skills": {},
        "inventory": [],
        "equipment": {},
        "relationships": {}
    }

@pytest.fixture
def sample_memory_data():
    """Sample memory data for testing"""
    return {
        "short_term_memory": ["Event 1", "Event 2", "Event 3"],
        "long_term_memory": {
            "important_events": [],
            "discovered_locations": [],
            "met_npcs": {},
            "completed_quests": [],
            "world_changes": [],
            "learned_lore": []
        },
        "current_context": {
            "location": "Test Location",
            "active_npcs": [],
            "current_quest": None,
            "immediate_threats": [],
            "available_actions": []
        }
    }
