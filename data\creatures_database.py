"""
Expanded creature database with detailed information for each starting creature type
"""
from typing import Dict, List, Any

# Extended creature database with lore, fusion combinations, and evolution details
CREATURES_DATABASE = {
    # Original Creatures (Enhanced)
    "Slime": {
        "name": "<PERSON><PERSON>",
        "description": "A gelatinous blob with incredible adaptability and absorption abilities",
        "detailed_description": "Slimes are among the most adaptable creatures in Aethermoor. Their malleable bodies can absorb nutrients, toxins, and even magical essences from their environment. While initially weak, their potential for growth is virtually limitless.",
        "base_stats": {"hp": 50, "mp": 30, "attack": 5, "defense": 8, "speed": 3},
        "stat_growth": {"hp": 1.2, "mp": 1.1, "attack": 1.0, "defense": 1.3, "speed": 0.9},
        "abilities": ["Absorb", "Acid Resistance"],
        "starting_skills": ["Basic Absorption", "Toxin Resistance"],
        "unique_fusions": ["Perfect Copy", "Elemental Adaptation", "Mimic Form"],
        "evolution_paths": ["Elemental Slime", "King Slime", "Mimic Slime"],
        "lore": "Born from the primordial ooze of creation, slimes represent pure potential. Many underestimate them, but wise adventurers know that a mature slime can become one of the most formidable creatures in existence.",
        "gameplay_style": "Defensive tank with absorption mechanics and high adaptability",
        "recommended_for": "Players who enjoy gradual growth and defensive strategies"
    },
    
    "Spider": {
        "name": "Spider",
        "description": "A cunning arachnid with web-spinning abilities and venomous attacks",
        "detailed_description": "Spiders are master tacticians of the creature world. Their ability to create complex web traps and deliver precise venomous strikes makes them formidable despite their small size. They excel at controlling the battlefield.",
        "base_stats": {"hp": 30, "mp": 40, "attack": 8, "defense": 4, "speed": 9},
        "stat_growth": {"hp": 0.9, "mp": 1.2, "attack": 1.1, "defense": 0.8, "speed": 1.3},
        "abilities": ["Web Spin", "Poison Bite"],
        "starting_skills": ["Basic Web Creation", "Venom Production"],
        "unique_fusions": ["Venomous Web Trap", "Dimensional Web", "Web Mastery"],
        "evolution_paths": ["Arachne", "Widow Spider", "Phase Spider"],
        "lore": "Ancient texts speak of the first spider, who learned the secrets of fate by weaving the threads of destiny. Modern spiders inherit this wisdom, making them natural strategists and trap-makers.",
        "gameplay_style": "Tactical controller with trap-based combat and hit-and-run tactics",
        "recommended_for": "Players who enjoy strategic planning and battlefield control"
    },
    
    "Goblin": {
        "name": "Goblin",
        "description": "A small humanoid with natural cunning and tool-using abilities",
        "detailed_description": "Goblins are among the most intelligent starting creatures, with an innate understanding of tools, tactics, and social dynamics. Their small stature is offset by their cleverness and ability to work with others.",
        "base_stats": {"hp": 40, "mp": 20, "attack": 7, "defense": 5, "speed": 6},
        "stat_growth": {"hp": 1.0, "mp": 0.9, "attack": 1.1, "defense": 1.0, "speed": 1.1},
        "abilities": ["Tool Use", "Pack Tactics"],
        "starting_skills": ["Basic Crafting", "Social Awareness"],
        "unique_fusions": ["Tactical Genius", "Master Craftsman", "Leadership"],
        "evolution_paths": ["Hobgoblin", "Goblin Shaman", "Goblin King"],
        "lore": "Despite their reputation as mere pests, goblins possess a sophisticated culture and remarkable adaptability. They were among the first creatures to develop complex societies and technological innovations.",
        "gameplay_style": "Versatile all-rounder with crafting, social, and tactical abilities",
        "recommended_for": "Players who enjoy balanced gameplay with social and crafting elements"
    },
    
    # New Creatures
    "Wisp": {
        "name": "Wisp",
        "description": "A floating orb of pure magical energy with light and illusion powers",
        "detailed_description": "Wisps are beings of concentrated magical essence, capable of manipulating light, energy, and the very fabric of magic itself. They are fragile but possess immense magical potential.",
        "base_stats": {"hp": 25, "mp": 60, "attack": 6, "defense": 2, "speed": 8},
        "stat_growth": {"hp": 0.8, "mp": 1.4, "attack": 0.9, "defense": 0.7, "speed": 1.2},
        "abilities": ["Light Manipulation", "Magic Sense"],
        "starting_skills": ["Basic Light Magic", "Mana Perception"],
        "unique_fusions": ["Radiant Burst", "Mana Mastery", "Ethereal Form"],
        "evolution_paths": ["Elemental Wisp", "Guardian Spirit", "Arcane Wisp"],
        "lore": "Wisps are fragments of the world's magical consciousness given form. They serve as bridges between the physical and magical realms, often guiding lost souls to safety.",
        "gameplay_style": "Glass cannon mage with high magical damage and utility spells",
        "recommended_for": "Players who enjoy pure magic gameplay and high-risk, high-reward strategies"
    },
    
    "Rat": {
        "name": "Rat",
        "description": "A small, agile rodent with keen senses and survival instincts",
        "detailed_description": "Rats are the ultimate survivors, capable of thriving in any environment. Their keen senses, quick reflexes, and natural cunning make them excellent scouts and infiltrators.",
        "base_stats": {"hp": 35, "mp": 25, "attack": 6, "defense": 3, "speed": 10},
        "stat_growth": {"hp": 0.9, "mp": 1.0, "attack": 1.0, "defense": 0.8, "speed": 1.4},
        "abilities": ["Keen Senses", "Scavenge"],
        "starting_skills": ["Enhanced Hearing", "Survival Instinct"],
        "unique_fusions": ["Shadow Step", "Plague Carrier", "Pack Leader"],
        "evolution_paths": ["Dire Rat", "Plague Rat", "Shadow Rat"],
        "lore": "Underestimated by most, rats have survived every catastrophe in Aethermoor's history. Their adaptability and intelligence have made them secret keepers of ancient knowledge.",
        "gameplay_style": "Fast scout with stealth abilities and environmental awareness",
        "recommended_for": "Players who enjoy stealth gameplay and exploration"
    },
    
    "Mushroom": {
        "name": "Mushroom",
        "description": "A sentient fungus with spore-based abilities and natural healing powers",
        "detailed_description": "Mushroom creatures are living ecosystems, capable of producing various spores for different effects. They excel at support abilities and have strong connections to nature.",
        "base_stats": {"hp": 45, "mp": 35, "attack": 4, "defense": 7, "speed": 2},
        "stat_growth": {"hp": 1.1, "mp": 1.2, "attack": 0.8, "defense": 1.2, "speed": 0.7},
        "abilities": ["Spore Release", "Natural Healing"],
        "starting_skills": ["Basic Healing", "Spore Production"],
        "unique_fusions": ["Toxic Cloud", "Regeneration", "Nature's Blessing"],
        "evolution_paths": ["Mycelium Network", "Toxic Mushroom", "Healing Mushroom"],
        "lore": "Mushrooms are the memory keepers of the forest, connected through vast underground networks. They remember every creature that has lived and died in their domain.",
        "gameplay_style": "Support healer with area control and nature magic",
        "recommended_for": "Players who enjoy support roles and nature-based magic"
    }
}

# Creature-specific skill fusion combinations
CREATURE_FUSION_SPECIALTIES = {
    "Slime": {
        "absorption_mastery": ["Absorb", "Acid Resistance", "Toxin Immunity"],
        "elemental_adaptation": ["Heat Resistance", "Cold Resistance", "Electric Resistance"],
        "perfect_mimic": ["Absorb", "Mimic", "Shape Change"]
    },
    "Spider": {
        "web_mastery": ["Web Spin", "Sticky Trap", "Web Strength"],
        "venom_expertise": ["Poison Bite", "Toxin Production", "Paralytic Venom"],
        "dimensional_weaving": ["Web Spin", "Phase", "Space Magic"]
    },
    "Goblin": {
        "tactical_leadership": ["Pack Tactics", "Leadership", "Battle Strategy"],
        "master_crafting": ["Tool Use", "Crafting", "Engineering"],
        "social_mastery": ["Social Awareness", "Persuasion", "Diplomacy"]
    },
    "Wisp": {
        "light_mastery": ["Light Manipulation", "Radiant Burst", "Blinding Flash"],
        "mana_control": ["Magic Sense", "Mana Manipulation", "Spell Efficiency"],
        "ethereal_form": ["Phase", "Incorporeal", "Energy Being"]
    },
    "Rat": {
        "stealth_mastery": ["Keen Senses", "Stealth", "Silent Movement"],
        "survival_expert": ["Scavenge", "Survival Instinct", "Adaptation"],
        "pack_coordination": ["Pack Tactics", "Communication", "Group Strategy"]
    },
    "Mushroom": {
        "spore_mastery": ["Spore Release", "Toxic Spores", "Healing Spores"],
        "nature_connection": ["Natural Healing", "Plant Growth", "Earth Magic"],
        "ecosystem_control": ["Decomposition", "Nutrient Cycle", "Life Force"]
    }
}

def get_creature_info(creature_name: str) -> Dict[str, Any]:
    """Get detailed information about a creature"""
    return CREATURES_DATABASE.get(creature_name, {})

def get_all_creature_names() -> List[str]:
    """Get names of all available creatures"""
    return list(CREATURES_DATABASE.keys())

def get_creature_fusion_specialties(creature_name: str) -> Dict[str, List[str]]:
    """Get fusion specialties for a specific creature"""
    return CREATURE_FUSION_SPECIALTIES.get(creature_name, {})
