"""
Simple demonstration of the Enhanced Action Selection System in gameplay
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.game_engine import GameEngine

async def demo_action_selection_gameplay():
    """Demonstrate action selection during actual gameplay"""
    print("🎮 Enhanced Action Selection - Gameplay Demo")
    print("=" * 50)
    
    try:
        # Create game engine
        game_engine = GameEngine()
        
        print("Creating a test character and starting gameplay...")
        
        # Start new game
        await game_engine.start_new_game()
        
        # Complete character creation quickly
        await game_engine.process_character_creation("1")  # Backstory
        await game_engine.process_character_creation("DemoPlayer")  # Name
        await game_engine.process_character_creation("brave, curious")  # Traits
        await game_engine.process_character_creation("Explorer")  # Occupation
        response = await game_engine.process_character_creation("1")  # Slime
        
        print(f"\n✅ Character created: {game_engine.character.name} the {game_engine.character.creature_type}")
        print(f"Abilities: {game_engine.character.abilities}")
        print(f"Location: {game_engine.current_location}")
        
        # Now we should be in gameplay mode with action options
        print("\n" + "=" * 50)
        print("GAMEPLAY WITH ENHANCED ACTION SELECTION")
        print("=" * 50)
        
        print("\n🎯 Initial Action Options:")
        print("-" * 30)
        if game_engine.current_action_options:
            for i, action in enumerate(game_engine.current_action_options, 1):
                risk_indicator = ""
                if action.risk_level == "medium":
                    risk_indicator = " ⚠️"
                elif action.risk_level == "high":
                    risk_indicator = " ⚠️⚠️"
                print(f"  {i}. {action.name}{risk_indicator}")
                print(f"     {action.description}")
        else:
            print("  No action options available yet")
        
        # Demo numbered selection
        print("\n🎯 Demo 1: Numbered Action Selection")
        print("-" * 30)
        if game_engine.current_action_options:
            print("Player selects option 1...")
            response = await game_engine.process_game_action("1")
            print(f"Response: {response[:150]}...")
            
            print(f"\nNew action options generated: {len(game_engine.current_action_options)}")
            for i, action in enumerate(game_engine.current_action_options, 1):
                print(f"  {i}. {action.name}")
        
        # Demo custom action
        print("\n🎯 Demo 2: Custom Action Input")
        print("-" * 30)
        print("Player uses custom action: 'look for interesting plants'...")
        response = await game_engine.process_game_action("look for interesting plants")
        print(f"Response: {response[:150]}...")
        
        # Demo anti-repetition
        print("\n🎯 Demo 3: Anti-Repetition System")
        print("-" * 30)
        print("Testing repetition detection...")
        
        # Record some actions to test repetition
        for i in range(3):
            game_engine.memory.record_player_action("explore", game_engine.current_location)
        
        is_repetitive = game_engine.memory.is_action_repetitive("explore", game_engine.current_location)
        print(f"'explore' action is now repetitive: {is_repetitive}")
        
        # Show action context
        context = game_engine.memory.get_action_suggestions_context(game_engine.current_location)
        print(f"Recent actions: {context['recent_actions']}")
        print(f"Overused actions: {context['overused_actions']}")
        
        # Demo cooldown system
        print("\n🎯 Demo 4: Action Cooldown System")
        print("-" * 30)
        game_engine.memory.set_action_cooldown("rest", 2)
        print("Set 'rest' action on 2-turn cooldown")
        print(f"'rest' on cooldown: {game_engine.memory.is_action_on_cooldown('rest')}")
        
        game_engine.memory.update_cooldowns()
        print("After 1 turn:")
        print(f"'rest' on cooldown: {game_engine.memory.is_action_on_cooldown('rest')}")
        
        game_engine.memory.update_cooldowns()
        print("After 2 turns:")
        print(f"'rest' on cooldown: {game_engine.memory.is_action_on_cooldown('rest')}")
        
        # Demo action parsing
        print("\n🎯 Demo 5: Action Parsing System")
        print("-" * 30)
        if game_engine.current_action_options:
            test_inputs = ["1", "2", "explore", "use absorb", "dance wildly"]
            
            for test_input in test_inputs:
                selected_action, input_type = game_engine.action_system.parse_player_input(
                    test_input, game_engine.current_action_options
                )
                if selected_action:
                    print(f"  '{test_input}' -> {selected_action.name} ({input_type})")
                else:
                    print(f"  '{test_input}' -> Custom action ({input_type})")
        
        # Demo memory integration
        print("\n🎯 Demo 6: Memory System Integration")
        print("-" * 30)
        print(f"Action history length: {len(game_engine.memory.action_history)}")
        print(f"Response patterns tracked: {len(game_engine.memory.response_patterns)}")
        print(f"Locations with action counts: {list(game_engine.memory.location_action_counts.keys())}")
        
        # Demo save/load
        print("\n🎯 Demo 7: Save/Load Integration")
        print("-" * 30)
        original_actions = len(game_engine.current_action_options)
        print(f"Current action options: {original_actions}")
        
        # Save game state
        game_state = game_engine.get_game_state()
        saved_actions = len(game_state.get("current_action_options", []))
        print(f"Action options in save data: {saved_actions}")
        
        print("\n✅ Enhanced Action Selection System Demo Complete!")
        
        print("\n📊 SYSTEM STATISTICS:")
        print(f"  • Total actions generated: {len(game_engine.current_action_options)}")
        print(f"  • Action categories available: {len(set(a.category for a in game_engine.current_action_options))}")
        print(f"  • Memory events tracked: {len(game_engine.memory.action_history)}")
        print(f"  • Base actions available: {len(game_engine.action_system.base_actions)}")
        print(f"  • Contextual generators: {len(game_engine.action_system.contextual_generators)}")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demo_action_selection_gameplay())
