#!/usr/bin/env python3
"""
Demo script to showcase the new circumstantial skills system
"""

from game.skill_learning_system import SkillLearningSystem
from game.character import Character


def demo_circumstantial_skills():
    """Demonstrate the new circumstantial skills system"""
    print("🌟 Isekai Text Adventure - Circumstantial Skills Demo 🌟")
    print("=" * 60)

    # Initialize the skill learning system
    learning_system = SkillLearningSystem()

    # Create a test character
    character = Character("Aria", ["curious", "brave"], "scholar")
    character.set_creature_type("Slime")
    character.level = 1
    character.abilities = []
    character.combat_victories = 0
    character.stats = {"hp": 50, "max_hp": 50, "mp": 30, "max_mp": 30}

    print(f"Character: {character.name} the {character.creature_type}")
    print(f"Traits: {', '.join(character.traits)}")
    print(f"Occupation: {character.occupation}")
    print()

    # Demo environmental interactions
    print("🍄 Environmental Interactions:")
    print("-" * 30)

    # Eating glowing mushrooms
    print("Action: Eating glowing mushrooms...")
    learned_skills = learning_system.analyze_action_for_learning(
        "I carefully eat the glowing mushroom", character, "Mystical Forest"
    )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Drinking from magical spring
    print("Action: Drinking from magical spring...")
    learned_skills = learning_system.analyze_action_for_learning(
        "I drink from the magical spring", character, "Sacred Grove"
    )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Touching ancient artifact
    print("Action: Touching ancient artifact...")
    for i in range(2):  # ADVANCED tier needs 2 repetitions with tutorial boost
        learned_skills = learning_system.analyze_action_for_learning(
            "I carefully touch the ancient artifact", character, "Ancient Ruins"
        )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Weather exposure
    print("🌩️ Weather Exposure:")
    print("-" * 20)

    print("Action: Standing in lightning storm...")
    learned_skills = learning_system.analyze_action_for_learning(
        "I stand bravely in the lightning storm", character, "Mountain Peak"
    )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Creature-specific interactions
    print("🦄 Creature-Specific Interactions:")
    print("-" * 35)

    # Change to Mushroom creature for spore abilities
    character.creature_type = "Mushroom"
    print(f"Character evolved to: {character.creature_type}")

    print("Action: Releasing spores...")
    for i in range(2):  # ADVANCED tier needs 2 repetitions with tutorial boost
        learned_skills = learning_system.analyze_action_for_learning(
            "I release spores into the air", character, "Forest Clearing"
        )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Change to Wisp creature for light abilities
    character.creature_type = "Wisp"
    print(f"Character evolved to: {character.creature_type}")

    print("Action: Manipulating light...")
    for i in range(2):  # ADVANCED tier needs 2 repetitions with tutorial boost
        learned_skills = learning_system.analyze_action_for_learning(
            "I glow brightly to illuminate the area", character, "Dark Cave"
        )
    if learned_skills:
        print(f"✨ Learned: {', '.join(learned_skills)}")
    else:
        print("📝 Action recorded, skill learning in progress...")
    print()

    # Show action counters
    print("📊 Action Progress:")
    print("-" * 18)
    for action, count in learning_system.action_counters.items():
        if count > 0:
            print(f"  {action}: {count} times")
    print()

    # Show skills learned by category
    print("🎓 Skills Learned by Category:")
    print("-" * 30)
    for category, count in learning_system.skills_learned_by_category.items():
        if count > 0:
            print(f"  {category.title()}: {count} skills")
    print()

    # Demo skill fusion possibilities
    print("🔮 Skill Fusion Possibilities:")
    print("-" * 30)

    # Simulate having some skills for fusion
    test_abilities = ["Magical Digestion", "Mana Absorption"]
    possible_fusions = learning_system.skill_system.check_fusion_possibilities(test_abilities)
    if possible_fusions:
        print(f"With skills {test_abilities}:")
        for fusion in possible_fusions:
            print(f"  → Can fuse into: {fusion}")

    test_abilities = ["Fire Affinity", "Ice Affinity", "Lightning Affinity"]
    possible_fusions = learning_system.skill_system.check_fusion_possibilities(test_abilities)
    if possible_fusions:
        print(f"With skills {test_abilities}:")
        for fusion in possible_fusions:
            print(f"  → Can fuse into: {fusion}")

    print()
    print("🎉 Demo complete! The circumstantial skills system is working!")
    print("=" * 60)


if __name__ == "__main__":
    demo_circumstantial_skills()
