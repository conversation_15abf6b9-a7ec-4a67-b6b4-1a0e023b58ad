"""
Demonstration of the Enhanced Action Selection System
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.game_engine import GameEngine

async def demo_enhanced_action_selection():
    """Demonstrate the enhanced action selection system"""
    print("🎮 Enhanced Action Selection System Demo")
    print("=" * 50)
    
    try:
        # Create game engine
        game_engine = GameEngine()
        
        print("Starting new game and creating character...")
        
        # Start new game
        await game_engine.start_new_game()
        
        # Simulate character creation process for a Slime
        print("\n1. Creating character...")
        await game_engine.process_character_creation("1")  # Backstory
        await game_engine.process_character_creation("ActionDemo")  # Name
        await game_engine.process_character_creation("curious, adaptive, brave")  # Traits
        await game_engine.process_character_creation("Game Developer")  # Occupation
        response = await game_engine.process_character_creation("1")  # Slime
        
        print(f"Character created: {game_engine.character.name}")
        print(f"Creature type: {game_engine.character.creature_type}")
        print(f"Abilities: {game_engine.character.abilities}")
        
        print("\n" + "=" * 50)
        print("ENHANCED ACTION SELECTION FEATURES DEMO")
        print("=" * 50)
        
        # Demo 1: Structured Action Presentation
        print("\n🎯 DEMO 1: Structured Action Presentation")
        print("-" * 40)
        print("After character creation, the game automatically presents contextual action options:")
        print(response)
        
        # Demo 2: Hybrid Input System - Numbered Selection
        print("\n🎯 DEMO 2: Hybrid Input System - Numbered Selection")
        print("-" * 40)
        print("Player selects option 1 (numbered input)...")
        response = await game_engine.process_game_action("1")
        print(f"Response: {response[:200]}...")
        
        # Demo 3: Hybrid Input System - Free-form Text
        print("\n🎯 DEMO 3: Hybrid Input System - Free-form Text")
        print("-" * 40)
        print("Player uses custom action: 'examine the mysterious glowing mushrooms'...")
        response = await game_engine.process_game_action("examine the mysterious glowing mushrooms")
        print(f"Response: {response[:200]}...")
        
        # Demo 4: Anti-Repetition Mechanisms
        print("\n🎯 DEMO 4: Anti-Repetition Mechanisms")
        print("-" * 40)
        print("Demonstrating action repetition tracking...")
        
        # Perform the same action multiple times
        for i in range(3):
            print(f"  Attempt {i+1}: Exploring the area...")
            response = await game_engine.process_game_action("explore the area")
            
            # Check if action is becoming repetitive
            is_repetitive = game_engine.memory.is_action_repetitive("explore the area", game_engine.current_location)
            print(f"  Action is repetitive: {is_repetitive}")
        
        # Demo 5: Action Cooldowns
        print("\n🎯 DEMO 5: Action Cooldowns")
        print("-" * 40)
        print("Testing action cooldowns...")
        
        # Use an action with cooldown
        response = await game_engine.process_game_action("rest and recover")
        print("Used 'rest and recover' action")
        
        # Check if it's on cooldown
        on_cooldown = game_engine.memory.is_action_on_cooldown("rest")
        print(f"'Rest' action on cooldown: {on_cooldown}")
        
        # Demo 6: Contextual Action Generation
        print("\n🎯 DEMO 6: Contextual Action Generation")
        print("-" * 40)
        print("Current action options are generated based on:")
        print(f"  - Location: {game_engine.current_location}")
        print(f"  - Character abilities: {game_engine.character.abilities}")
        print(f"  - Character level: {game_engine.character.level}")
        print(f"  - Time of day: {game_engine.world_state['time']}")
        print(f"  - Weather: {game_engine.world_state['weather']}")
        
        print("\nCurrent available actions:")
        for i, action in enumerate(game_engine.current_action_options, 1):
            risk_indicator = ""
            if action.risk_level == "medium":
                risk_indicator = " ⚠️"
            elif action.risk_level == "high":
                risk_indicator = " ⚠️⚠️"
            print(f"  {i}. {action.name}{risk_indicator} ({action.category.value})")
        
        # Demo 7: Information Control
        print("\n🎯 DEMO 7: Information Control")
        print("-" * 40)
        print("Action options don't reveal hidden information:")
        print("  ✅ Actions are presented from character's perspective")
        print("  ✅ Secret passages aren't revealed until discovered")
        print("  ✅ NPC thoughts and motivations remain hidden")
        print("  ✅ Future events aren't spoiled in action descriptions")
        
        # Demo 8: Memory Integration
        print("\n🎯 DEMO 8: Memory System Integration")
        print("-" * 40)
        print("Action history tracking:")
        recent_actions = [a["action"] for a in list(game_engine.memory.action_history)[-5:]]
        print(f"  Recent actions: {recent_actions}")
        
        location_counts = game_engine.memory.location_action_counts.get(game_engine.current_location, {})
        print(f"  Location action counts: {location_counts}")
        
        print(f"  Response patterns tracked: {len(game_engine.memory.response_patterns)}")
        
        # Demo 9: Save/Load Compatibility
        print("\n🎯 DEMO 9: Save/Load System Integration")
        print("-" * 40)
        print("Testing save/load with action selection state...")
        
        # Save game
        success = game_engine.save_game("action_demo_save")
        print(f"Game saved: {success}")
        
        # Check that action options are saved
        game_state = game_engine.get_game_state()
        saved_actions = game_state.get("current_action_options", [])
        print(f"Action options saved: {len(saved_actions)} actions")
        
        print("\n✅ Enhanced Action Selection System Demo Complete!")
        print("\nKey Features Demonstrated:")
        print("  🎯 Structured action presentation with contextual relevance")
        print("  🔄 Hybrid input system (numbered + free-form text)")
        print("  🚫 Anti-repetition mechanisms with cooldowns")
        print("  🔒 Information control maintaining narrative immersion")
        print("  🧠 Memory system integration for pattern tracking")
        print("  💾 Save/load compatibility with action state")
        print("  🎮 Seamless integration with existing game systems")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()

def show_system_overview():
    """Show an overview of the enhanced action selection system"""
    print("\n📋 ENHANCED ACTION SELECTION SYSTEM OVERVIEW")
    print("=" * 60)
    
    print("\n🏗️ ARCHITECTURE:")
    print("  • ActionSelectionSystem - Core action generation and parsing")
    print("  • ActionOption - Structured action data with metadata")
    print("  • MemorySystem - Enhanced with anti-repetition tracking")
    print("  • GameEngine - Integrated action processing pipeline")
    print("  • GeminiClient - Enhanced with action context support")
    
    print("\n🎯 KEY FEATURES:")
    print("  • Contextual Action Generation - Based on location, abilities, state")
    print("  • Hybrid Input Support - Numbered selections + custom text")
    print("  • Anti-Repetition System - Cooldowns, pattern detection, variety")
    print("  • Information Control - No spoilers or hidden info leaks")
    print("  • Memory Integration - Persistent action history tracking")
    print("  • Save/Load Support - Complete state preservation")
    
    print("\n🔧 TECHNICAL IMPLEMENTATION:")
    print("  • 5 contextual action generators (location, character, encounter, skill, environmental)")
    print("  • Action scoring and diversity selection algorithms")
    print("  • Repetition detection with configurable thresholds")
    print("  • Response pattern analysis for AI variety")
    print("  • Comprehensive unit test coverage (13 test cases)")
    
    print("\n🎮 PLAYER EXPERIENCE:")
    print("  • Clear, numbered action options after each AI response")
    print("  • Freedom to choose suggested actions or create custom ones")
    print("  • Reduced repetitive gameplay through smart filtering")
    print("  • Contextually relevant suggestions that feel natural")
    print("  • Seamless integration with existing game mechanics")

if __name__ == "__main__":
    show_system_overview()
    print("\n" + "=" * 60)
    asyncio.run(demo_enhanced_action_selection())
