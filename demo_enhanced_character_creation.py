"""
Demo script to showcase the enhanced character creation system
"""
import asyncio
from game.game_engine import GameEngine
from game.lore_system import LoreSystem
from data.creatures_database import CREATURES_DATABASE, get_creature_info

async def demo_character_creation():
    """Demonstrate the enhanced character creation flow"""
    print("=== Enhanced Character Creation System Demo ===\n")
    
    # Initialize systems
    engine = GameEngine()
    lore_system = LoreSystem()
    
    print("1. LORE SYSTEM DEMONSTRATION")
    print("-" * 40)
    
    # Show available backstories
    print("Available backstory scenarios:")
    for i, scenario in enumerate(lore_system.backstory_scenarios, 1):
        print(f"{i}. {scenario.name}: {scenario.description}")
    
    print("\nExample backstory (Gaming Accident):")
    gaming_backstory = lore_system.get_backstory_by_name("Gaming Accident")
    if gaming_backstory:
        print(f"Death Scene: {gaming_backstory.death_scene[:100]}...")
        print(f"Suggested Traits: {', '.join(gaming_backstory.suggested_traits)}")
        print(f"Suggested Occupations: {', '.join(gaming_backstory.suggested_occupations)}")
    
    print("\n" + "="*60 + "\n")
    
    print("2. EXPANDED CREATURE DATABASE")
    print("-" * 40)
    
    print("Available starting creatures:")
    for creature_name, creature_data in CREATURES_DATABASE.items():
        stats = creature_data["base_stats"]
        print(f"\n{creature_name}:")
        print(f"  Description: {creature_data['description']}")
        print(f"  Stats: HP:{stats['hp']} MP:{stats['mp']} ATK:{stats['attack']} DEF:{stats['defense']} SPD:{stats['speed']}")
        print(f"  Starting Abilities: {', '.join(creature_data['abilities'])}")
        print(f"  Evolution Paths: {', '.join(creature_data['evolution_paths'])}")
        print(f"  Gameplay Style: {creature_data['gameplay_style']}")
    
    print("\n" + "="*60 + "\n")
    
    print("3. CHARACTER CREATION FLOW SIMULATION")
    print("-" * 40)
    
    # Start new game
    response = await engine.start_new_game()
    print("Game Start Response:")
    print(response[:200] + "..." if len(response) > 200 else response)
    
    print("\nSimulating backstory selection (choosing option 1)...")
    response = await engine.process_character_creation("1")
    print("Backstory Response:")
    print(response[:200] + "..." if len(response) > 200 else response)
    
    print("\nSimulating name input...")
    response = await engine.process_character_creation("Aiden")
    print("Name Response:")
    print(response[:150] + "..." if len(response) > 150 else response)
    
    print("\nSimulating traits input...")
    response = await engine.process_character_creation("strategic, persistent, analytical")
    print("Traits Response:")
    print(response[:150] + "..." if len(response) > 150 else response)
    
    print("\nSimulating occupation input...")
    response = await engine.process_character_creation("programmer")
    print("Occupation Response:")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    print("\nSimulating creature selection (choosing Wisp)...")
    response = await engine.process_character_creation("4")  # Wisp should be option 4
    print("Final Response:")
    print(response[:200] + "..." if len(response) > 200 else response)
    
    print("\n" + "="*60 + "\n")
    
    print("4. CHARACTER STATS VERIFICATION")
    print("-" * 40)
    
    character = engine.character
    print(f"Character Name: {character.name}")
    print(f"Creature Type: {character.creature_type}")
    print(f"Traits: {', '.join(character.traits)}")
    print(f"Occupation: {character.occupation}")
    print(f"Stats: {character.stats}")
    print(f"Starting Abilities: {', '.join(character.abilities)}")
    
    print("\n" + "="*60 + "\n")
    
    print("5. SKILL FUSION SYSTEM INTEGRATION")
    print("-" * 40)
    
    # Check available fusions for the character
    possible_fusions = engine.skill_system.check_fusion_possibilities(character.abilities)
    print(f"Possible fusions with current abilities: {possible_fusions}")
    
    # Show creature-specific fusions
    print(f"\nCreature-specific fusions for {character.creature_type}:")
    for fusion_name, recipe in engine.skill_system.fusion_recipes.items():
        if recipe.get("creature_requirement") == character.creature_type:
            print(f"  {fusion_name}: {' + '.join(recipe['components'])}")
    
    print("\n" + "="*60 + "\n")
    
    print("6. EVOLUTION SYSTEM INTEGRATION")
    print("-" * 40)
    
    # Check evolution paths
    evolution_paths = engine.evolution_system.get_all_evolution_paths(character.creature_type)
    print(f"Evolution paths for {character.creature_type}:")
    for path in evolution_paths:
        print(f"  {path.name}: {path.description}")
        print(f"    Requirements: {[req.description for req in path.requirements]}")
    
    print("\nDemo completed successfully!")

if __name__ == "__main__":
    asyncio.run(demo_character_creation())
