"""
Demonstration of the NPC Relationship System and Dialogue Trees
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.npc_system import NPCSystem, RelationshipLevel
from game.dialogue_system import DialogueSystem
from game.memory_system import MemorySystem
from game.character import Character

async def demo_npc_dialogue_system():
    """Demonstrate the NPC and dialogue systems"""
    print("🎭 NPC Relationship System & Dialogue Trees Demo")
    print("=" * 60)

    # Initialize systems
    npc_system = NPCSystem()
    dialogue_system = DialogueSystem()
    memory = MemorySystem()
    character = Character("DemoPlayer", ["curious", "brave"], "Explorer")
    character.level = 3
    character.abilities = ["Absorb", "Acid Resistance"]

    print("\n🏗️ SYSTEM OVERVIEW")
    print("-" * 30)
    print(f"NPCs initialized: {len(npc_system.npcs)}")
    print(f"Dialogue trees: {len(dialogue_system.dialogue_trees)}")
    print(f"Character: {character.name} (Level {character.level})")

    # Demo 1: NPC Database
    print("\n🎯 DEMO 1: NPC Database & Locations")
    print("-" * 40)

    locations = ["Mysterious Forest", "Crystal Caves", "Moonlit Meadow", "Abandoned Ruins"]
    for location in locations:
        npcs = npc_system.get_npcs_in_location(location)
        if npcs:
            print(f"\n📍 {location}:")
            for npc in npcs:
                print(f"  • {npc.name} ({npc.personality.value} {npc.role.value})")
                print(f"    {npc.description}")

    # Demo 2: NPC Personality & Traits
    print("\n🎯 DEMO 2: NPC Personalities & Traits")
    print("-" * 40)

    featured_npcs = ["Elder Sprite", "Lost Traveler", "Dwarf Miner", "Crystal Sage"]
    for npc_name in featured_npcs:
        npc = npc_system.get_npc(npc_name)
        if npc:
            print(f"\n👤 {npc.name}:")
            print(f"  Personality: {npc.personality.value}")
            print(f"  Role: {npc.role.value}")
            print(f"  Traits: {', '.join(npc.personality_traits)}")
            print(f"  Interests: {', '.join(npc.interests[:3])}...")
            print(f"  Likes: {', '.join(npc.likes[:2])}...")
            print(f"  Services: {', '.join(npc.services)}")

    # Demo 3: Dialogue Tree Structure
    print("\n🎯 DEMO 3: Dialogue Tree Structure")
    print("-" * 40)

    elder_tree = dialogue_system.dialogue_trees["Elder Sprite"]
    print(f"Elder Sprite dialogue tree:")
    print(f"  Root node: {elder_tree.root_node_id}")
    print(f"  Total nodes: {len(elder_tree.nodes)}")
    print(f"  Node types: {list(set(node.node_type.value for node in elder_tree.nodes.values()))}")

    # Show greeting node structure
    greeting_node = elder_tree.nodes["greeting"]
    print(f"\n  Greeting node:")
    print(f"    Text: {greeting_node.npc_text[:80]}...")
    print(f"    Choices: {len(greeting_node.choices)}")
    for i, choice in enumerate(greeting_node.choices):
        print(f"      {i+1}. {choice.text}")

    # Demo 4: Starting Conversations
    print("\n🎯 DEMO 4: Starting Conversations")
    print("-" * 40)

    print("Starting conversation with Elder Sprite...")
    npc_response, choices = dialogue_system.start_conversation(
        "player", "Elder Sprite", character, memory
    )

    print(f"\n**Elder Sprite**: {npc_response}")
    print("\nAvailable responses:")
    for choice in choices:
        print(f"  {choice}")

    # Demo 5: Processing Dialogue Choices
    print("\n🎯 DEMO 5: Processing Dialogue Choices")
    print("-" * 40)

    print("Player selects option 1...")
    npc_response, new_choices, consequences = dialogue_system.process_dialogue_choice(
        "player", 0, character, memory
    )

    print(f"\n**Elder Sprite**: {npc_response}")
    if consequences:
        print(f"Consequences: {consequences}")

    if new_choices:
        print("\nNew choices:")
        for choice in new_choices:
            print(f"  {choice}")

    # Demo 6: Relationship System
    print("\n🎯 DEMO 6: Relationship System")
    print("-" * 40)

    # Test relationship levels
    test_relationships = [-75, -30, -10, 10, 30, 60, 90]
    print("Relationship level examples:")
    for rel_value in test_relationships:
        level = npc_system.get_relationship_level(rel_value)
        print(f"  {rel_value:3d} → {level.value}")

    # Test relationship changes
    print("\nRelationship change examples:")
    test_actions = [
        ("help protect the forest", {"helped_npc": True}),
        ("destroy trees", {"harmed_npc": True}),
        ("show respect for nature", {}),
        ("act impatient", {})
    ]

    for action, context in test_actions:
        change = npc_system.calculate_relationship_change("Elder Sprite", action, context)
        print(f"  '{action}' → {change:+d} relationship")

    # Demo 7: Memory Integration
    print("\n🎯 DEMO 7: Memory System Integration")
    print("-" * 40)

    # Meet an NPC
    memory.meet_npc("Elder Sprite", "A wise and ancient forest spirit", 15)

    # Update relationship
    memory.update_npc_relationship("Elder Sprite", "Had a pleasant conversation", 5)

    # Show NPC context
    npc_context = memory.get_npc_context("Elder Sprite")
    print(f"NPC context: {npc_context}")

    # Show memory data
    npc_data = memory.long_term_memory["met_npcs"]["Elder Sprite"]
    print(f"Relationship value: {npc_data['relationship']}")
    print(f"Interactions: {len(npc_data['interactions'])}")
    print(f"Last interaction: {npc_data['interactions'][-1] if npc_data['interactions'] else 'None'}")

    # Demo 8: Conditional Dialogue
    print("\n🎯 DEMO 8: Conditional Dialogue")
    print("-" * 40)

    print("Testing dialogue conditions...")

    # Test with low relationship
    print(f"Current relationship with Elder Sprite: {npc_data['relationship']}")

    # Start new conversation to see if conditions affect available choices
    dialogue_system.active_conversations.clear()  # Reset conversation
    npc_response, choices = dialogue_system.start_conversation(
        "player", "Elder Sprite", character, memory
    )

    print(f"Available choices with relationship {npc_data['relationship']}: {len(choices)}")

    # Increase relationship and test again
    memory.update_npc_relationship("Elder Sprite", "Showed great respect", 10)
    dialogue_system.active_conversations.clear()  # Reset conversation
    npc_response, choices = dialogue_system.start_conversation(
        "player", "Elder Sprite", character, memory
    )

    updated_relationship = memory.long_term_memory["met_npcs"]["Elder Sprite"]["relationship"]
    print(f"Available choices with relationship {updated_relationship}: {len(choices)}")

    # Demo 9: Multiple NPCs
    print("\n🎯 DEMO 9: Multiple NPC Interactions")
    print("-" * 40)

    # Meet different NPCs
    npcs_to_meet = ["Lost Traveler", "Dwarf Miner", "Fairy Queen"]

    for npc_name in npcs_to_meet:
        npc = npc_system.get_npc(npc_name)
        if npc:
            # Simulate meeting
            memory.meet_npc(npc_name, f"Met {npc_name} in {npc.location}", 0)

            # Start conversation
            dialogue_system.active_conversations.clear()
            npc_response, choices = dialogue_system.start_conversation(
                "player", npc_name, character, memory
            )

            print(f"\n**{npc_name}**: {npc_response[:60]}...")
            print(f"  Available choices: {len(choices)}")

    # Demo 10: Save/Load Compatibility
    print("\n🎯 DEMO 10: Save/Load Compatibility")
    print("-" * 40)

    # Test memory serialization
    memory_dict = memory.to_dict()
    print(f"Memory data includes NPCs: {'met_npcs' in memory_dict}")
    met_npcs = memory_dict.get('long_term_memory', {}).get('met_npcs', {})
    print(f"NPCs met: {len(met_npcs)}")

    # Test restoration
    new_memory = MemorySystem.from_dict(memory_dict)
    restored_npc_data = new_memory.get_npc_context("Elder Sprite")
    print(f"Restored NPC context: {restored_npc_data}")

    print("\n✅ NPC Relationship System & Dialogue Trees Demo Complete!")

    # Summary statistics
    print("\n📊 SYSTEM STATISTICS:")
    print(f"  • Total NPCs: {len(npc_system.npcs)}")
    print(f"  • Dialogue trees: {len(dialogue_system.dialogue_trees)}")
    print(f"  • Total dialogue nodes: {sum(len(tree.nodes) for tree in dialogue_system.dialogue_trees.values())}")
    print(f"  • NPCs met in demo: {len(memory.long_term_memory['met_npcs'])}")
    print(f"  • Relationship levels: {len(RelationshipLevel)}")
    print(f"  • Active conversations: {len(dialogue_system.active_conversations)}")

def show_npc_system_overview():
    """Show an overview of the NPC and dialogue systems"""
    print("\n📋 NPC RELATIONSHIP SYSTEM & DIALOGUE TREES OVERVIEW")
    print("=" * 70)

    print("\n🏗️ ARCHITECTURE:")
    print("  • NPCSystem - Manages NPC data, relationships, and availability")
    print("  • DialogueSystem - Handles conversation trees and choice processing")
    print("  • NPCData - Structured NPC information with personality and traits")
    print("  • DialogueTree - Branching conversation structures with conditions")
    print("  • MemorySystem - Enhanced with persistent NPC relationship tracking")

    print("\n🎭 NPC FEATURES:")
    print("  • 6 NPCs across 4 locations with unique personalities and roles")
    print("  • Personality-driven dialogue with consistent character voices")
    print("  • Relationship system with 7 levels from Enemy to Devoted")
    print("  • Dynamic relationship changes based on player actions")
    print("  • Availability conditions based on level, quests, and relationships")
    print("  • Services and knowledge areas specific to each NPC's role")

    print("\n💬 DIALOGUE FEATURES:")
    print("  • Branching dialogue trees with multiple conversation paths")
    print("  • Conditional choices based on relationships, skills, and progress")
    print("  • Consequence system for learning skills and gaining experience")
    print("  • One-time dialogue nodes for special interactions")
    print("  • Auto-progression for seamless conversation flow")
    print("  • Integration with quest system (ready for future implementation)")

    print("\n🔧 TECHNICAL IMPLEMENTATION:")
    print("  • Modular design with clear separation of NPC data and dialogue logic")
    print("  • Comprehensive condition system for dynamic content")
    print("  • Memory integration for persistent relationship tracking")
    print("  • Action selection integration for contextual NPC interactions")
    print("  • Save/load compatibility with full state preservation")
    print("  • Extensive unit test coverage (18 test cases)")

    print("\n🎮 PLAYER EXPERIENCE:")
    print("  • Rich character interactions with meaningful relationship building")
    print("  • Contextual dialogue that responds to player choices and progress")
    print("  • Clear feedback on relationship changes and consequences")
    print("  • Seamless integration with existing action selection system")
    print("  • Foundation ready for quest system and advanced social mechanics")

if __name__ == "__main__":
    show_npc_system_overview()
    print("\n" + "=" * 70)
    asyncio.run(demo_npc_dialogue_system())
