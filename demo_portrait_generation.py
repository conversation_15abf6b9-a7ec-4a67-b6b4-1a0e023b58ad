"""
Demo script to test portrait generation functionality
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.portrait_system import PortraitSystem
from game.character import Character
import config

async def demo_portrait_generation():
    """Demo the portrait generation system"""
    print("🎨 Portrait Generation Demo")
    print("=" * 50)
    
    # Check if portrait generation is enabled
    if not config.ENABLE_PORTRAIT_GENERATION:
        print("❌ Portrait generation is disabled in config.py")
        print("Set ENABLE_PORTRAIT_GENERATION = True to enable it")
        return
    
    # Initialize portrait system
    portrait_system = PortraitSystem()
    
    # Create a test character
    character = Character()
    character.name = "Zephyr"
    character.set_creature_type("Slime")
    character.traits = ["Brave", "Curious", "Magical"]
    character.occupation = "Adventurer"
    character.level = 3
    
    print(f"📝 Created test character: {character.name}")
    print(f"   Type: {character.creature_type}")
    print(f"   Traits: {', '.join(character.traits)}")
    print(f"   Level: {character.level}")
    print()
    
    # Get character data
    character_data = character.to_dict()
    
    # Test 1: Generate base character portrait
    print("🖼️  Test 1: Generating base character portrait...")
    try:
        base_portrait = await portrait_system.get_character_portrait(
            character_data, "base", force_regenerate=True
        )
        
        if base_portrait:
            print("✅ Base portrait generated successfully!")
            print(f"   Size: {base_portrait.size}")
            print(f"   Mode: {base_portrait.mode}")
        else:
            print("❌ Failed to generate base portrait")
    except Exception as e:
        print(f"❌ Error generating base portrait: {e}")
    
    print()
    
    # Test 2: Generate evolution portraits
    print("🔄 Test 2: Generating evolution portraits...")
    try:
        evolution_paths = ["Elemental Slime", "King Slime"]
        evolution_portraits = await portrait_system.generate_evolution_portraits(
            character_data, evolution_paths
        )
        
        for evolution_name, portrait in evolution_portraits.items():
            if portrait:
                print(f"✅ {evolution_name} portrait generated successfully!")
            else:
                print(f"❌ Failed to generate {evolution_name} portrait")
    except Exception as e:
        print(f"❌ Error generating evolution portraits: {e}")
    
    print()
    
    # Test 3: Generate backstory illustration
    print("📖 Test 3: Generating backstory illustration...")
    try:
        backstory_illustration = await portrait_system.generate_backstory_illustration(
            "The Overworked Salaryman"
        )
        
        if backstory_illustration:
            print("✅ Backstory illustration generated successfully!")
            print(f"   Size: {backstory_illustration.size}")
        else:
            print("❌ Failed to generate backstory illustration")
    except Exception as e:
        print(f"❌ Error generating backstory illustration: {e}")
    
    print()
    
    # Test 4: Cache information
    print("💾 Test 4: Cache information...")
    cache_info = portrait_system.get_cache_info()
    print(f"   Cached portraits: {cache_info['cached_portraits']}")
    print(f"   Queue length: {cache_info['queue_length']}")
    print(f"   Is generating: {cache_info['is_generating']}")
    print(f"   Cache enabled: {cache_info['cache_enabled']}")
    print(f"   Generation enabled: {cache_info['generation_enabled']}")
    
    print()
    
    # Test 5: Test different creature types
    print("🐾 Test 5: Testing different creature types...")
    creature_types = ["Spider", "Goblin", "Wisp", "Rat", "Mushroom"]
    
    for creature_type in creature_types:
        print(f"   Testing {creature_type}...")
        test_character_data = character_data.copy()
        test_character_data['creature_type'] = creature_type
        test_character_data['name'] = f"Test{creature_type}"
        
        # Queue for background generation
        portrait_system.queue_portrait_generation(
            test_character_data, "base", priority=2
        )
    
    print(f"   Queued {len(creature_types)} portraits for background generation")
    
    # Process queue manually for demo
    if portrait_system.generation_queue:
        print("   Processing generation queue...")
        try:
            await portrait_system.process_queue_manually()
            print("   ✅ Queue processed successfully!")
        except Exception as e:
            print(f"   ❌ Error processing queue: {e}")
    
    print()
    print("🎉 Portrait generation demo completed!")
    print()
    print("📁 Generated portraits are saved in:")
    print(f"   {config.ASSETS_DIR / 'portraits'}")
    print()
    print("💡 Tips:")
    print("   - Portraits are cached to avoid regeneration")
    print("   - Use force_regenerate=True to create new versions")
    print("   - Evolution portraits are generated automatically when characters evolve")
    print("   - Background generation queue prevents API rate limiting")

def main():
    """Main function to run the demo"""
    try:
        asyncio.run(demo_portrait_generation())
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
