"""
Demonstration of the Quest System with Branching Storylines
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.quest_system import QuestSystem, QuestStatus, QuestType
from game.memory_system import MemorySystem
from game.character import Character

async def demo_quest_system():
    """Demonstrate the quest system functionality"""
    print("📜 Quest System with Branching Storylines Demo")
    print("=" * 60)
    
    # Initialize systems
    quest_system = QuestSystem()
    memory = MemorySystem()
    character = Character("QuestHero", ["brave", "curious"], "Adventurer")
    character.level = 3
    character.abilities = ["Basic Combat", "Exploration"]
    
    print("\n🏗️ SYSTEM OVERVIEW")
    print("-" * 30)
    print(f"Quests in database: {len(quest_system.quest_database)}")
    print(f"Character: {character.name} (Level {character.level})")
    print(f"Abilities: {', '.join(character.abilities)}")
    
    # Demo 1: Quest Database Overview
    print("\n🎯 DEMO 1: Quest Database Overview")
    print("-" * 40)
    
    quest_types = {}
    for quest in quest_system.quest_database.values():
        quest_type = quest.quest_type.value
        if quest_type not in quest_types:
            quest_types[quest_type] = []
        quest_types[quest_type].append(quest.name)
    
    for quest_type, quests in quest_types.items():
        print(f"\n📋 {quest_type.upper()} QUESTS:")
        for quest_name in quests:
            quest = next(q for q in quest_system.quest_database.values() if q.name == quest_name)
            print(f"  • {quest_name} (Level {quest.level_requirement})")
            print(f"    Given by: {quest.giver_npc} in {quest.location}")
            print(f"    Objectives: {len(quest.objectives)}")
            if quest.branches:
                print(f"    Branches: {len(quest.branches)}")
    
    # Demo 2: Quest Availability
    print("\n🎯 DEMO 2: Quest Availability System")
    print("-" * 40)
    
    available_quests = quest_system.get_available_quests(character, memory)
    print(f"Available quests for level {character.level} character: {len(available_quests)}")
    
    for quest in available_quests:
        print(f"\n✅ {quest.name}")
        print(f"   Type: {quest.quest_type.value}")
        print(f"   Location: {quest.location}")
        print(f"   Description: {quest.description}")
        
        if quest.prerequisite_skills:
            print(f"   Required skills: {', '.join(quest.prerequisite_skills)}")
        if quest.prerequisite_relationships:
            print(f"   Required relationships: {quest.prerequisite_relationships}")
    
    # Demo 3: Starting a Quest
    print("\n🎯 DEMO 3: Starting a Quest")
    print("-" * 40)
    
    # Start the forest mapping quest
    quest_id = "map_mysterious_forest"
    quest = quest_system.get_quest(quest_id)
    print(f"Starting quest: {quest.name}")
    
    success = quest_system.start_quest(quest_id, character, memory)
    print(f"Quest started successfully: {success}")
    
    if success:
        print(f"Quest status: {quest.status.value}")
        print(f"Active quests: {len(quest_system.active_quests)}")
        
        # Show objectives
        print("\n📋 Quest Objectives:")
        for i, objective in enumerate(quest.objectives, 1):
            print(f"  {i}. {objective.description} ({objective.get_progress_text()})")
    
    # Demo 4: Quest Progress Tracking
    print("\n🎯 DEMO 4: Quest Progress Tracking")
    print("-" * 40)
    
    print("Simulating quest progress...")
    
    # Update first objective
    quest_system.update_quest_progress(quest_id, "explore_forest_areas", 1)
    print("✅ Explored first forest area")
    
    quest_system.update_quest_progress(quest_id, "explore_forest_areas", 1)
    print("✅ Explored second forest area")
    
    # Update second objective
    quest_system.update_quest_progress(quest_id, "find_landmarks", 1)
    print("✅ Found first landmark")
    
    # Show updated progress
    print("\n📊 Updated Progress:")
    for i, objective in enumerate(quest.objectives, 1):
        status = "✅" if objective.is_complete() else "⏳"
        print(f"  {status} {objective.description} ({objective.get_progress_text()})")
    
    # Demo 5: Quest Summary
    print("\n🎯 DEMO 5: Quest Summary System")
    print("-" * 40)
    
    summaries = quest_system.get_active_quests_summary()
    for quest_name, summary in summaries.items():
        print(f"📜 **{quest_name}**")
        print(summary)
    
    # Demo 6: Quest Branching
    print("\n🎯 DEMO 6: Quest Branching System")
    print("-" * 40)
    
    print("Available branches for current quest:")
    for branch in quest.branches:
        print(f"  🌿 {branch.name}")
        print(f"     Condition: {branch.condition}")
        print(f"     Description: {branch.description}")
        print(f"     Objectives: {len(branch.objectives)}")
        if branch.rewards.experience > 0:
            print(f"     Bonus XP: {branch.rewards.experience}")
    
    # Trigger a branch
    print(f"\nTriggering branch condition: 'found_forest_exit'")
    branch_triggered = quest_system.trigger_quest_branch(quest_id, "found_forest_exit")
    print(f"Branch triggered: {branch_triggered}")
    
    if branch_triggered:
        print(f"Current branch: {quest.current_branch}")
        
        # Show branch objectives
        active_objectives = quest.get_active_objectives()
        print("\n📋 Branch Objectives:")
        for obj in active_objectives:
            print(f"  • {obj.description} ({obj.get_progress_text()})")
    
    # Demo 7: Quest Completion
    print("\n🎯 DEMO 7: Quest Completion System")
    print("-" * 40)
    
    # Complete remaining main objectives
    quest_system.update_quest_progress(quest_id, "explore_forest_areas", 1)  # Complete exploration
    quest_system.update_quest_progress(quest_id, "find_landmarks", 1)  # Complete landmarks
    
    print("Completed all main objectives!")
    
    # Complete branch objective
    if quest.current_branch:
        quest_system.update_quest_progress(quest_id, "escort_marcus", 1)
        print("Completed branch objective!")
    
    # Check if quest is complete
    is_complete = quest_system.check_quest_completion(quest_id)
    print(f"Quest ready for completion: {is_complete}")
    
    if is_complete:
        # Complete the quest
        rewards = quest_system.complete_quest(quest_id, character, memory)
        
        print("\n🎉 Quest Completed!")
        print(f"Experience gained: {rewards.experience}")
        print(f"Skills learned: {', '.join(rewards.skills)}")
        print(f"Items received: {', '.join(rewards.items)}")
        
        if rewards.relationship_changes:
            print("Relationship changes:")
            for npc, change in rewards.relationship_changes.items():
                print(f"  • {npc}: +{change}")
        
        if rewards.world_changes:
            print("World changes:")
            for change in rewards.world_changes:
                print(f"  • {change}")
        
        print(f"\nQuest status: {quest.status.value}")
        print(f"Active quests remaining: {len(quest_system.active_quests)}")
        print(f"Completed quests: {len(quest_system.completed_quests)}")
    
    # Demo 8: Quest Prerequisites
    print("\n🎯 DEMO 8: Quest Prerequisites System")
    print("-" * 40)
    
    # Try to start the delivery quest (requires completed forest quest)
    delivery_quest_id = "deliver_message_to_family"
    delivery_quest = quest_system.get_quest(delivery_quest_id)
    
    print(f"Attempting to start: {delivery_quest.name}")
    print(f"Prerequisites: {delivery_quest.prerequisite_quests}")
    
    # Check if available now
    is_available = delivery_quest.is_available(character, memory)
    print(f"Quest available: {is_available}")
    
    if is_available:
        success = quest_system.start_quest(delivery_quest_id, character, memory)
        print(f"Successfully started delivery quest: {success}")
    
    # Demo 9: Multiple Quest Management
    print("\n🎯 DEMO 9: Multiple Quest Management")
    print("-" * 40)
    
    # Start another quest
    cave_quest_id = "clear_blocked_tunnel"
    cave_quest = quest_system.get_quest(cave_quest_id)
    
    # Increase character level to meet requirements
    character.level = 4
    
    if cave_quest.is_available(character, memory):
        quest_system.start_quest(cave_quest_id, character, memory)
        print(f"Started additional quest: {cave_quest.name}")
    
    # Show all active quests
    print(f"\nActive quests: {len(quest_system.active_quests)}")
    summaries = quest_system.get_active_quests_summary()
    for quest_name in summaries.keys():
        print(f"  📜 {quest_name}")
    
    # Demo 10: Quest System Statistics
    print("\n🎯 DEMO 10: Quest System Statistics")
    print("-" * 40)
    
    total_quests = len(quest_system.quest_database)
    active_quests = len(quest_system.active_quests)
    completed_quests = len(quest_system.completed_quests)
    
    print(f"📊 Quest System Statistics:")
    print(f"  • Total quests in database: {total_quests}")
    print(f"  • Currently active: {active_quests}")
    print(f"  • Completed: {completed_quests}")
    print(f"  • Available to start: {len(quest_system.get_available_quests(character, memory))}")
    
    # Count quest types
    type_counts = {}
    for quest in quest_system.quest_database.values():
        quest_type = quest.quest_type.value
        type_counts[quest_type] = type_counts.get(quest_type, 0) + 1
    
    print(f"\n📋 Quest Types:")
    for quest_type, count in type_counts.items():
        print(f"  • {quest_type}: {count} quests")
    
    # Count objectives
    total_objectives = sum(len(quest.objectives) for quest in quest_system.quest_database.values())
    total_branches = sum(len(quest.branches) for quest in quest_system.quest_database.values())
    
    print(f"\n🎯 Content Statistics:")
    print(f"  • Total objectives: {total_objectives}")
    print(f"  • Total quest branches: {total_branches}")
    print(f"  • Average objectives per quest: {total_objectives / total_quests:.1f}")
    
    print("\n✅ Quest System Demo Complete!")

def show_quest_system_overview():
    """Show an overview of the quest system"""
    print("\n📋 QUEST SYSTEM WITH BRANCHING STORYLINES OVERVIEW")
    print("=" * 70)
    
    print("\n🏗️ ARCHITECTURE:")
    print("  • QuestSystem - Central quest management and progression tracking")
    print("  • Quest - Individual quest data with objectives and branching")
    print("  • QuestObjective - Trackable goals with progress monitoring")
    print("  • QuestBranch - Alternative quest paths with unique outcomes")
    print("  • QuestReward - Comprehensive reward system with multiple types")
    
    print("\n📜 QUEST FEATURES:")
    print("  • 5 unique quests across multiple locations and NPCs")
    print("  • 8 quest types: Main Story, Side Quest, Daily, Exploration, etc.")
    print("  • 7 objective types: Kill, Collect, Talk To, Visit, Deliver, etc.")
    print("  • Branching storylines with conditional paths and outcomes")
    print("  • Prerequisites system based on level, skills, and relationships")
    print("  • Progress tracking with hidden objectives and optional goals")
    
    print("\n🌿 BRANCHING SYSTEM:")
    print("  • Dynamic quest paths that change based on player choices")
    print("  • Conditional branches triggered by player actions or world state")
    print("  • Multiple endings for quests with different rewards")
    print("  • Branch-specific objectives and consequences")
    print("  • Integration with NPC relationships and world changes")
    
    print("\n🔧 TECHNICAL IMPLEMENTATION:")
    print("  • Comprehensive objective tracking with multiple progress types")
    print("  • Reward system supporting XP, items, skills, and relationships")
    print("  • Memory integration for persistent quest state")
    print("  • Action selection integration for quest-related actions")
    print("  • Dialogue system integration for quest offering and updates")
    print("  • Extensive unit test coverage (19 test cases)")
    
    print("\n🎮 PLAYER EXPERIENCE:")
    print("  • Clear quest objectives with progress tracking")
    print("  • Meaningful choices that affect quest outcomes")
    print("  • Rich rewards that impact character progression")
    print("  • Seamless integration with NPC interactions")
    print("  • Foundation for complex narrative experiences")

if __name__ == "__main__":
    show_quest_system_overview()
    print("\n" + "=" * 70)
    asyncio.run(demo_quest_system())
