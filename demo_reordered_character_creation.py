"""
Demo script to showcase the reordered character creation flow
"""
import asyncio
from game.game_engine import GameEngine

async def demo_reordered_character_creation():
    """Demonstrate the new character creation flow: name → traits → occupation → creature → backstory"""
    print("=== REORDERED CHARACTER CREATION FLOW DEMO ===\n")
    
    # Initialize game engine
    engine = GameEngine()
    
    print("🎯 NEW FLOW: Name → Traits → Occupation → Creature → Backstory")
    print("=" * 70)
    print("This new flow allows the backstory to be shaped by previous choices,")
    print("creating a more personalized and meaningful origin story.\n")
    
    print("🎮 ENHANCED CHARACTER CREATION SIMULATION")
    print("-" * 50)
    
    # Start new game (now begins with name)
    response = await engine.start_new_game()
    print("📝 STEP 1: Name Selection")
    print("Current Stage:", engine.creation_stage)
    print("Response:")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    # Enter name
    print("\n🎭 Entering name '<PERSON><PERSON>'...")
    response = await engine.process_character_creation("Zara")
    print("\n📝 STEP 2: Trait Selection")
    print("Current Stage:", engine.creation_stage)
    print("Character Name:", engine.character.name)
    print("Response:")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    # Enter traits
    print("\n🎭 Entering traits 'strategic, curious, analytical'...")
    response = await engine.process_character_creation("strategic, curious, analytical")
    print("\n📝 STEP 3: Occupation Selection")
    print("Current Stage:", engine.creation_stage)
    print("Character Traits:", engine.character.traits)
    print("Response:")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    # Enter occupation
    print("\n💼 Entering occupation 'game designer'...")
    response = await engine.process_character_creation("game designer")
    print("\n📝 STEP 4: Creature Selection")
    print("Current Stage:", engine.creation_stage)
    print("Character Occupation:", engine.character.occupation)
    print("Response:")
    print(response[:400] + "..." if len(response) > 400 else response)
    
    # Select creature
    print("\n🕷️ Selecting Spider (option 2)...")
    response = await engine.process_character_creation("2")
    print("\n📝 STEP 5: Backstory Selection (INTELLIGENT RECOMMENDATIONS)")
    print("Current Stage:", engine.creation_stage)
    print("Character Creature:", engine.character.creature_type)
    print("Response:")
    print(response[:500] + "..." if len(response) > 500 else response)
    
    # Show recommendation analysis
    print("\n🔍 RECOMMENDATION ANALYSIS")
    print("-" * 30)
    recommended_backstories = engine._get_recommended_backstories()
    print(f"Recommended Backstories: {recommended_backstories}")
    print("Recommendation Logic:")
    print(f"  - Character Traits: {engine.character.traits}")
    print(f"  - Character Occupation: {engine.character.occupation}")
    print(f"  - Character Creature: {engine.character.creature_type}")
    print("  - System analyzes synergy between choices and backstory themes")
    
    # Select backstory (Gaming Accident should be recommended)
    print("\n🎯 Selecting 'Gaming Accident' backstory (should be recommended)...")
    response = await engine.process_character_creation("5")  # Gaming Accident
    print("\n📝 STEP 6: CHARACTER CREATION COMPLETE")
    print("Final Stage:", engine.creation_stage)
    print("Game State:", engine.state)
    print("Response:")
    print(response[:600] + "..." if len(response) > 600 else response)
    
    print("\n" + "="*70 + "\n")
    
    print("📊 FINAL CHARACTER ANALYSIS")
    print("-" * 50)
    
    character = engine.character
    print(f"✅ Character: {character.name}")
    print(f"✅ Traits: {', '.join(character.traits)}")
    print(f"✅ Occupation: {character.occupation}")
    print(f"✅ Creature Type: {character.creature_type}")
    print(f"✅ Backstory: {character.backstory_name}")
    
    print(f"\n🎯 Synergy Analysis:")
    for bonus_type, bonus_data in character.backstory_synergy_bonuses.items():
        if bonus_type == "trait_synergy":
            print(f"  Trait Synergy Score: {bonus_data['synergy_score']:.2f}")
            print(f"  Matching Traits: {bonus_data['matching_traits']}")
        elif bonus_type == "occupation_synergy":
            print(f"  Occupation Match: Perfect={bonus_data['is_perfect_match']}, Partial={bonus_data['is_partial_match']}")
        elif bonus_type == "stat_bonuses":
            print(f"  Stat Bonuses Applied: {bonus_data}")
    
    print(f"\n📈 Final Stats:")
    for stat, value in character.stats.items():
        print(f"  {stat}: {value}")
    
    print("\n" + "="*70 + "\n")
    
    print("🌟 BENEFITS OF REORDERED FLOW")
    print("-" * 50)
    print("1. 🎭 Player establishes identity first (name, traits, occupation)")
    print("2. 🎯 Creature choice reflects player preferences")
    print("3. 📖 Backstory recommendations based on ALL previous choices")
    print("4. 🔗 More meaningful connections between character elements")
    print("5. 🎨 Personalized origin story that feels crafted for the character")
    print("6. ⭐ Intelligent recommendations guide but don't force choices")
    print("7. 🎪 Enhanced narrative coherence and player investment")
    
    print("\n" + "="*70 + "\n")
    
    print("🔄 COMPARISON: OLD vs NEW FLOW")
    print("-" * 50)
    print("OLD FLOW: Backstory → Name → Traits → Occupation → Creature")
    print("  ❌ Backstory chosen without knowing character details")
    print("  ❌ Suggestions feel generic and disconnected")
    print("  ❌ Player may feel constrained by early backstory choice")
    print("")
    print("NEW FLOW: Name → Traits → Occupation → Creature → Backstory")
    print("  ✅ Character identity established before backstory")
    print("  ✅ Backstory recommendations feel personalized")
    print("  ✅ All choices inform the final origin story")
    print("  ✅ Better narrative coherence and player agency")
    
    print("\nDemo completed successfully! 🎉")

async def demo_multiple_character_types():
    """Demonstrate how different character choices lead to different backstory recommendations"""
    print("\n" + "="*70)
    print("🎭 MULTIPLE CHARACTER TYPE DEMONSTRATION")
    print("="*70)
    
    character_profiles = [
        {
            "name": "Alex",
            "traits": ["strategic", "competitive", "analytical"],
            "occupation": "programmer",
            "creature": "Spider",
            "expected_backstory": "Gaming Accident"
        },
        {
            "name": "Maya",
            "traits": ["brave", "protective", "selfless"],
            "occupation": "firefighter", 
            "creature": "Goblin",
            "expected_backstory": "Heroic Sacrifice"
        },
        {
            "name": "Chen",
            "traits": ["methodical", "persistent", "detail-oriented"],
            "occupation": "accountant",
            "creature": "Mushroom",
            "expected_backstory": "Overwork Death"
        }
    ]
    
    for i, profile in enumerate(character_profiles, 1):
        print(f"\n🎯 CHARACTER PROFILE {i}: {profile['name']}")
        print("-" * 30)
        
        engine = GameEngine()
        
        # Simulate character creation
        await engine.start_new_game()
        await engine.process_character_creation(profile["name"])
        await engine.process_character_creation(", ".join(profile["traits"]))
        await engine.process_character_creation(profile["occupation"])
        await engine.process_character_creation(profile["creature"])
        
        # Get recommendations
        recommended_backstories = engine._get_recommended_backstories()
        
        print(f"Character: {profile['name']}")
        print(f"Traits: {', '.join(profile['traits'])}")
        print(f"Occupation: {profile['occupation']}")
        print(f"Creature: {profile['creature']}")
        print(f"Recommended Backstories: {recommended_backstories}")
        print(f"Expected Backstory: {profile['expected_backstory']}")
        print(f"✅ Correct Recommendation: {profile['expected_backstory'] in recommended_backstories}")

if __name__ == "__main__":
    asyncio.run(demo_reordered_character_creation())
    asyncio.run(demo_multiple_character_types())
