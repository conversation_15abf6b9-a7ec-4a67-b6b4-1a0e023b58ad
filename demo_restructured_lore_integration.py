"""
Demo script to showcase the restructured lore integration system
"""
import asyncio
from game.game_engine import GameEngine
from game.lore_system import LoreSystem
from game.character import Character

async def demo_restructured_lore_integration():
    """Demonstrate the enhanced lore integration throughout character creation"""
    print("=== RESTRUCTURED LORE INTEGRATION DEMO ===\n")
    
    # Initialize systems
    engine = GameEngine()
    lore_system = LoreSystem()
    
    print("🌟 ENHANCED BACKSTORY SYSTEM")
    print("-" * 50)
    
    # Show enhanced backstory scenarios with new fields
    print("Available backstory scenarios with enhanced integration:")
    for i, scenario in enumerate(lore_system.backstory_scenarios, 1):
        print(f"\n{i}. {scenario.name}")
        print(f"   Description: {scenario.description}")
        print(f"   Recommended Creatures: {', '.join(scenario.recommended_creatures)}")
        print(f"   Stat Bonuses: {scenario.stat_bonuses}")
        print(f"   Personality Keywords: {', '.join(scenario.personality_keywords)}")
    
    print("\n" + "="*70 + "\n")
    
    print("🎭 SYNERGY VALIDATION SYSTEM")
    print("-" * 50)
    
    # Demonstrate synergy validation
    gaming_backstory = lore_system.get_backstory_by_name("Gaming Accident")
    if gaming_backstory:
        print(f"Testing synergy for '{gaming_backstory.name}' backstory:")
        
        # Test trait synergy
        print("\n📊 Trait Synergy Testing:")
        test_traits = ["strategic", "persistent", "curious"]  # Mix of matching and non-matching
        trait_synergy = lore_system.validate_trait_synergy(gaming_backstory, test_traits)
        print(f"   Test Traits: {test_traits}")
        print(f"   Synergy Score: {trait_synergy['synergy_score']:.2f}")
        print(f"   Matching Traits: {trait_synergy['matching_traits']}")
        print(f"   Bonus Description: {trait_synergy['bonus_description']}")
        
        # Test occupation synergy
        print("\n💼 Occupation Synergy Testing:")
        test_occupations = ["programmer", "teacher", "chef"]
        for occupation in test_occupations:
            occ_synergy = lore_system.validate_occupation_synergy(gaming_backstory, occupation)
            print(f"   '{occupation}': Perfect Match={occ_synergy['is_perfect_match']}, "
                  f"Partial Match={occ_synergy['is_partial_match']}")
            print(f"      {occ_synergy['bonus_description']}")
    
    print("\n" + "="*70 + "\n")
    
    print("🎮 ENHANCED CHARACTER CREATION FLOW")
    print("-" * 50)
    
    # Simulate enhanced character creation
    print("Starting enhanced character creation simulation...")
    
    # Start new game
    response = await engine.start_new_game()
    print("\n📖 Game Start (showing backstory options):")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    # Select backstory (Gaming Accident)
    print("\n🎯 Selecting 'Gaming Accident' backstory...")
    response = await engine.process_character_creation("5")  # Gaming Accident should be option 5
    print("Backstory Selection Response:")
    print(response[:250] + "..." if len(response) > 250 else response)
    
    # Enter name
    print("\n📝 Entering name 'Alex'...")
    response = await engine.process_character_creation("Alex")
    print("Name Response (with backstory context):")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    # Enter traits (mix of suggested and custom)
    print("\n🎭 Entering traits 'strategic, persistent, creative'...")
    response = await engine.process_character_creation("strategic, persistent, creative")
    print("Traits Response (with synergy feedback):")
    print(response[:350] + "..." if len(response) > 350 else response)
    
    # Enter occupation
    print("\n💼 Entering occupation 'game designer'...")
    response = await engine.process_character_creation("game designer")
    print("Occupation Response (with creature recommendations):")
    print(response[:400] + "..." if len(response) > 400 else response)
    
    # Select creature (recommended one)
    print("\n🕷️ Selecting Spider (recommended creature)...")
    response = await engine.process_character_creation("2")  # Spider should be option 2
    print("Final Creation Response:")
    print(response[:300] + "..." if len(response) > 300 else response)
    
    print("\n" + "="*70 + "\n")
    
    print("📊 CHARACTER ANALYSIS")
    print("-" * 50)
    
    # Analyze final character
    character = engine.character
    print(f"Final Character: {character.name}")
    print(f"Backstory: {character.backstory_name}")
    print(f"Creature Type: {character.creature_type}")
    print(f"Traits: {', '.join(character.traits)}")
    print(f"Occupation: {character.occupation}")
    
    print(f"\n🎯 Synergy Bonuses Applied:")
    for bonus_type, bonus_data in character.backstory_synergy_bonuses.items():
        print(f"   {bonus_type}: {bonus_data}")
    
    print(f"\n📈 Final Stats (with backstory bonuses):")
    for stat, value in character.stats.items():
        print(f"   {stat}: {value}")
    
    print("\n" + "="*70 + "\n")
    
    print("🔮 AI CONTEXT GENERATION")
    print("-" * 50)
    
    # Show AI context generation
    if engine.selected_backstory:
        ai_context = lore_system.get_backstory_context_for_ai(engine.selected_backstory)
        print("Generated AI Context for future responses:")
        print(ai_context)
    
    print("\n" + "="*70 + "\n")
    
    print("✅ INTEGRATION BENEFITS SUMMARY")
    print("-" * 50)
    print("1. 🎭 Backstory influences ALL creation stages")
    print("2. 💡 Intelligent suggestions based on death circumstances")
    print("3. ⭐ Creature recommendations aligned with backstory themes")
    print("4. 📊 Synergy validation with meaningful bonuses")
    print("5. 🎯 Stat bonuses that reflect past life experiences")
    print("6. 🤖 Enhanced AI responses with backstory context")
    print("7. 💾 Persistent backstory data for future gameplay")
    print("8. 🔄 Cross-stage validation and feedback")
    
    print("\nDemo completed successfully! 🎉")

if __name__ == "__main__":
    asyncio.run(demo_restructured_lore_integration())
