#!/usr/bin/env python3
"""
Demo script for the Reward Skill System in Me? Reincarnated?
Showcases automatic skill rewards based on achievements and milestones.
"""

from game.reward_skill_system import RewardSkillSystem
from game.character import Character
from game.skill_system import SkillFusionSystem


def demo_reward_skills():
    """Demonstrate the new reward skill system"""
    print("🏆 Isekai Text Adventure - Reward Skill System Demo 🏆")
    print("=" * 60)

    # Initialize systems
    reward_system = RewardSkillSystem()
    skill_system = SkillFusionSystem()

    # Create a test character
    character = Character("Aria", ["brave", "curious"], "adventurer")
    character.set_creature_type("Slime")
    character.level = 1
    character.abilities = []
    character.combat_victories = 0

    print(f"Character: {character.name} the {character.creature_type}")
    print(f"Traits: {', '.join(character.traits)}")
    print(f"Occupation: {character.occupation}")
    print()

    # Demo Story Progression Skills
    print("📖 Story Progression Rewards:")
    print("-" * 30)

    # Simulate first evolution
    print("Action: Character evolves for the first time...")
    newly_earned = reward_system.update_progress("evolution_count", 1, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")
            skill_info = reward_system.get_reward_skill(skill)
            print(f"   Effect: {skill_info.description}")
    print()

    # Simulate survival milestone
    print("Action: Surviving 10 days in the wilderness...")
    newly_earned = reward_system.update_progress("days_survived", 10, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")
            skill_info = reward_system.get_reward_skill(skill)
            print(f"   Effect: {skill_info.description}")
    print()

    # Demo Quest Completion Skills
    print("⚔️ Quest Completion Rewards:")
    print("-" * 30)

    # Simulate defeating different enemies
    enemies = ["Forest Wolf", "Cave Spider", "Goblin Scout", "Wild Boar", "Shadow Rat"]
    print("Action: Defeating various creatures...")
    for i, enemy in enumerate(enemies, 1):
        newly_earned = reward_system.update_progress("unique_enemies_defeated", enemy, "add_to_set")
        print(f"  Defeated {enemy} ({i}/5)")
        if newly_earned:
            for skill in newly_earned:
                character.earn_reward_skill(skill)
                print(f"🏆 Achievement unlocked! You earned: {skill}")
                skill_info = reward_system.get_reward_skill(skill)
                print(f"   Effect: {skill_info.description}")
    print()

    # Simulate location discovery
    print("Action: Discovering new locations...")
    locations = ["Ancient Ruins", "Crystal Cave", "Mystic Grove"]
    for i, location in enumerate(locations, 1):
        newly_earned = reward_system.update_progress("discovered_locations", location, "add_to_set")
        print(f"  Discovered {location} ({i}/3)")
        if newly_earned:
            for skill in newly_earned:
                character.earn_reward_skill(skill)
                print(f"🏆 Achievement unlocked! You earned: {skill}")
                skill_info = reward_system.get_reward_skill(skill)
                print(f"   Effect: {skill_info.description}")
    print()

    # Demo Relationship Growth Skills
    print("🤝 Relationship Growth Rewards:")
    print("-" * 30)

    # Simulate building strong relationship
    print("Action: Building trust with village elder...")
    newly_earned = reward_system.update_progress("max_npc_relationship", 80, "max")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")
            skill_info = reward_system.get_reward_skill(skill)
            print(f"   Effect: {skill_info.description}")
    print()

    # Demo Achievement-Based Skills
    print("🎯 Achievement-Based Rewards:")
    print("-" * 30)

    # Simulate collecting many items
    print("Action: Collecting 100 items...")
    newly_earned = reward_system.update_progress("total_items_collected", 100, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")
            skill_info = reward_system.get_reward_skill(skill)
            print(f"   Effect: {skill_info.description}")
    print()

    # Simulate learning many skills
    print("Action: Learning 20 different skills...")
    newly_earned = reward_system.update_progress("total_skills_learned", 20, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")
            skill_info = reward_system.get_reward_skill(skill)
            print(f"   Effect: {skill_info.description}")
    print()

    # Demo Legendary Fusion Skills
    print("⭐ Legendary Fusion Skills:")
    print("-" * 30)

    # Simulate earning more survival skills for legendary fusion
    print("Action: Reaching level 20 (Apex Predator)...")
    newly_earned = reward_system.update_progress("character_level", 20, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")

    print("Action: Surviving 30 days (Veteran Survivor)...")
    newly_earned = reward_system.update_progress("days_survived", 30, "set")
    if newly_earned:
        for skill in newly_earned:
            character.earn_reward_skill(skill)
            print(f"🏆 Achievement unlocked! You earned: {skill}")

    # Check for legendary fusion possibility
    print("\nChecking for legendary fusion possibilities...")
    fusion_combos = reward_system.get_fusion_combinations_with_rewards()
    
    # Check if Ultimate Survivor fusion is possible
    ultimate_survivor_components = fusion_combos["Ultimate Survivor"]["components"]
    if all(skill in character.abilities for skill in ultimate_survivor_components):
        print(f"🌟 LEGENDARY FUSION AVAILABLE!")
        print(f"   Components: {' + '.join(ultimate_survivor_components)}")
        print(f"   Result: Ultimate Survivor")
        
        # Get the legendary skill
        legendary_skills = reward_system.get_legendary_reward_skills()
        ultimate_skill = legendary_skills["Ultimate Survivor"]
        print(f"   Effect: {ultimate_skill.description}")
    print()

    # Show progress summary
    print("📊 Progress Summary:")
    print("-" * 20)
    summary = reward_system.get_progress_summary()
    
    earned_count = 0
    in_progress_count = 0
    
    for criteria_name, progress in summary.items():
        if progress["status"] == "earned":
            earned_count += 1
        else:
            in_progress_count += 1
    
    print(f"Total Reward Skills Earned: {earned_count}")
    print(f"Rewards In Progress: {in_progress_count}")
    print()
    
    # Show some specific progress
    print("Sample Progress Details:")
    for criteria_name, progress in list(summary.items())[:5]:
        if progress["status"] == "earned":
            print(f"✅ {criteria_name}: EARNED")
        else:
            current = progress["current"]
            target = progress["target"]
            percent = progress["progress_percent"]
            print(f"⏳ {criteria_name}: {current}/{target} ({percent:.0f}%)")
    
    print()
    print("🎉 Demo completed! The reward skill system automatically tracks")
    print("   achievements and grants skills based on your actions and milestones.")
    print("   These reward skills can also be combined through the fusion system")
    print("   to create even more powerful legendary abilities!")


if __name__ == "__main__":
    demo_reward_skills()
