"""
Enhanced action selection system for structured gameplay
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random

class ActionCategory(Enum):
    EXPLORATION = "exploration"
    COMBAT = "combat"
    SOCIAL = "social"
    SKILL = "skill"
    INVENTORY = "inventory"
    SPECIAL = "special"

@dataclass
class ActionOption:
    id: str
    name: str
    description: str
    category: ActionCategory
    requirements: List[str]
    hidden_info: bool = False
    cooldown: int = 0
    risk_level: str = "low"  # low, medium, high

class ActionSelectionSystem:
    """Manages contextual action generation and selection"""

    def __init__(self):
        self.base_actions = self._initialize_base_actions()
        self.contextual_generators = self._initialize_contextual_generators()

    def _initialize_base_actions(self) -> Dict[str, ActionOption]:
        """Initialize base actions available in most situations"""
        return {
            "explore_area": ActionOption(
                id="explore_area",
                name="Explore the area",
                description="Look around carefully for anything interesting",
                category=ActionCategory.EXPLORATION,
                requirements=[]
            ),
            "check_inventory": ActionOption(
                id="check_inventory",
                name="Check inventory",
                description="Review your items and equipment",
                category=ActionCategory.INVENTORY,
                requirements=[]
            ),
            "rest": ActionOption(
                id="rest",
                name="Rest and recover",
                description="Take a moment to rest and regain energy",
                category=ActionCategory.SPECIAL,
                requirements=[],
                cooldown=3
            ),
            "meditate": ActionOption(
                id="meditate",
                name="Meditate",
                description="Focus your mind and reflect on your abilities",
                category=ActionCategory.SKILL,
                requirements=[]
            ),
            "examine_self": ActionOption(
                id="examine_self",
                name="Examine yourself",
                description="Check your current condition and abilities",
                category=ActionCategory.SPECIAL,
                requirements=[]
            )
        }

    def _initialize_contextual_generators(self) -> Dict[str, callable]:
        """Initialize functions that generate context-specific actions"""
        return {
            "location_based": self._generate_location_actions,
            "character_based": self._generate_character_actions,
            "encounter_based": self._generate_encounter_actions,
            "skill_based": self._generate_skill_actions,
            "environmental": self._generate_environmental_actions
        }

    def generate_action_options(self, game_state: Dict[str, Any], memory_system,
                              location_system, character, max_options: int = 5) -> List[ActionOption]:
        """Generate contextually relevant action options"""
        all_actions = []
        current_location = game_state.get("current_location", "")

        # Get anti-repetition context
        repetition_context = memory_system.get_action_suggestions_context(current_location)

        # Generate actions from different sources
        for generator_name, generator_func in self.contextual_generators.items():
            try:
                actions = generator_func(game_state, memory_system, location_system, character)
                all_actions.extend(actions)
            except Exception as e:
                print(f"Error in {generator_name} generator: {e}")

        # Generate NPC interaction actions if NPCs are available
        if hasattr(game_state, 'get_npcs_in_current_location'):
            try:
                npc_actions = self._generate_npc_actions(game_state, memory_system, location_system, character)
                all_actions.extend(npc_actions)
            except Exception as e:
                print(f"Error in NPC action generator: {e}")

        # Generate quest-related actions if quest system is available
        if hasattr(game_state, 'get_active_quests_summary'):
            try:
                quest_actions = self._generate_quest_actions(game_state, memory_system, location_system, character)
                all_actions.extend(quest_actions)
            except Exception as e:
                print(f"Error in quest action generator: {e}")

        # Add base actions
        for action in self.base_actions.values():
            if not memory_system.is_action_on_cooldown(action.id):
                all_actions.append(action)

        # Filter out repetitive actions
        filtered_actions = self._filter_repetitive_actions(
            all_actions, repetition_context, current_location, memory_system
        )

        # Score and rank actions
        scored_actions = self._score_actions(filtered_actions, game_state, memory_system)

        # Select top actions ensuring variety
        selected_actions = self._select_diverse_actions(scored_actions, max_options)

        return selected_actions

    def _generate_location_actions(self, game_state: Dict[str, Any], memory_system,
                                 location_system, character) -> List[ActionOption]:
        """Generate actions based on current location"""
        actions = []
        current_location = game_state.get("current_location", "")

        if not current_location:
            return actions

        location = location_system.get_location(current_location)
        if not location:
            return actions

        # Movement actions
        for connected_loc in location.connected_locations:
            actions.append(ActionOption(
                id=f"move_to_{connected_loc.lower().replace(' ', '_')}",
                name=f"Travel to {connected_loc}",
                description=f"Leave this area and head to {connected_loc}",
                category=ActionCategory.EXPLORATION,
                requirements=[]
            ))

        # Resource gathering actions
        for resource in location.resources:
            actions.append(ActionOption(
                id=f"gather_{resource.lower().replace(' ', '_')}",
                name=f"Gather {resource}",
                description=f"Collect {resource} from the area",
                category=ActionCategory.EXPLORATION,
                requirements=[]
            ))

        # Secret discovery actions
        if location.secrets and not memory_system.is_action_repetitive("search for secrets", current_location):
            actions.append(ActionOption(
                id="search_secrets",
                name="Search for secrets",
                description="Look for hidden passages or concealed items",
                category=ActionCategory.EXPLORATION,
                requirements=[],
                risk_level="medium"
            ))

        return actions

    def _generate_character_actions(self, game_state: Dict[str, Any], memory_system,
                                  location_system, character) -> List[ActionOption]:
        """Generate actions based on character abilities and state"""
        actions = []

        # Ability-based actions
        for ability in character.abilities:
            if ability not in ["Basic Attack", "Defend"]:  # Skip basic combat abilities
                actions.append(ActionOption(
                    id=f"use_{ability.lower().replace(' ', '_')}",
                    name=f"Use {ability}",
                    description=f"Activate your {ability} ability",
                    category=ActionCategory.SKILL,
                    requirements=[ability]
                ))

        # Health-based actions
        if character.stats["hp"] < character.stats["max_hp"] * 0.5:
            actions.append(ActionOption(
                id="seek_healing",
                name="Seek healing",
                description="Look for ways to restore your health",
                category=ActionCategory.SPECIAL,
                requirements=[]
            ))

        # Equipment actions
        if character.inventory:
            actions.append(ActionOption(
                id="use_item",
                name="Use an item",
                description="Use one of your carried items",
                category=ActionCategory.INVENTORY,
                requirements=[]
            ))

        return actions

    def _generate_encounter_actions(self, game_state: Dict[str, Any], memory_system,
                                  location_system, character) -> List[ActionOption]:
        """Generate actions based on available encounters"""
        actions = []
        current_location = game_state.get("current_location", "")

        if not current_location:
            return actions

        encounters = location_system.get_available_encounters(
            current_location, character.level, character.abilities
        )

        for encounter in encounters[:3]:  # Limit to 3 encounters
            actions.append(ActionOption(
                id=f"encounter_{encounter.name.lower().replace(' ', '_')}",
                name=encounter.name,
                description=encounter.description,
                category=ActionCategory.EXPLORATION if encounter.encounter_type.value != "combat" else ActionCategory.COMBAT,
                requirements=[],
                risk_level="medium" if encounter.encounter_type.value == "combat" else "low"
            ))

        return actions

    def _generate_skill_actions(self, game_state: Dict[str, Any], memory_system,
                              location_system, character) -> List[ActionOption]:
        """Generate skill learning and practice actions"""
        actions = []
        current_location = game_state.get("current_location", "")

        # Practice actions for existing skills
        if len(character.abilities) > 2:  # Has more than basic abilities
            actions.append(ActionOption(
                id="practice_skills",
                name="Practice your abilities",
                description="Train with your current abilities to improve mastery",
                category=ActionCategory.SKILL,
                requirements=[]
            ))

        # Environmental skill learning opportunities
        location = location_system.get_location(current_location) if current_location else None
        if location:
            if "water" in location.description.lower():
                actions.append(ActionOption(
                    id="interact_with_water",
                    name="Interact with water",
                    description="Experiment with the water in this area",
                    category=ActionCategory.SKILL,
                    requirements=[]
                ))

            if any(word in location.description.lower() for word in ["plant", "tree", "flower", "mushroom"]):
                actions.append(ActionOption(
                    id="study_plants",
                    name="Study local flora",
                    description="Examine the plants and vegetation around you",
                    category=ActionCategory.SKILL,
                    requirements=[]
                ))

        return actions

    def _generate_environmental_actions(self, game_state: Dict[str, Any], memory_system,
                                      location_system, character) -> List[ActionOption]:
        """Generate actions based on environmental conditions"""
        actions = []
        world_state = game_state.get("world_state", {})
        current_location = game_state.get("current_location", "")

        # Time-based actions
        time_of_day = world_state.get("time", "morning")
        if time_of_day == "night":
            actions.append(ActionOption(
                id="observe_stars",
                name="Observe the night sky",
                description="Look up at the stars and celestial bodies",
                category=ActionCategory.EXPLORATION,
                requirements=[]
            ))

        # Weather-based actions
        weather = world_state.get("weather", "clear")
        if weather == "rain":
            actions.append(ActionOption(
                id="collect_rainwater",
                name="Collect rainwater",
                description="Gather fresh rainwater for later use",
                category=ActionCategory.EXPLORATION,
                requirements=[]
            ))

        return actions

    def _generate_npc_actions(self, game_state: Dict[str, Any], memory_system,
                            location_system, character) -> List[ActionOption]:
        """Generate NPC interaction actions"""
        actions = []
        current_location = game_state.get("current_location", "")

        if not current_location:
            return actions

        # Get NPCs in current location from location system
        location = location_system.get_location(current_location)
        if not location or not location.npcs:
            return actions

        # Generate talk actions for each NPC
        for npc_name in location.npcs:
            # Check if already talking to this NPC
            active_conversations = getattr(game_state.get('dialogue_system', {}), 'active_conversations', {})
            if "player" in active_conversations:
                current_npc, _ = active_conversations["player"]
                if current_npc == npc_name:
                    continue  # Skip if already talking to this NPC

            actions.append(ActionOption(
                id=f"talk_to_{npc_name.lower().replace(' ', '_')}",
                name=f"Talk to {npc_name}",
                description=f"Start a conversation with {npc_name}",
                category=ActionCategory.SOCIAL,
                requirements=[]
            ))

        return actions

    def _generate_quest_actions(self, game_state: Dict[str, Any], memory_system,
                              location_system, character) -> List[ActionOption]:
        """Generate quest-related actions"""
        actions = []

        # Add quest journal action if there are active quests
        if hasattr(game_state.get('quest_system', {}), 'active_quests'):
            quest_system = game_state.get('quest_system', {})
            if hasattr(quest_system, 'active_quests') and quest_system.active_quests:
                actions.append(ActionOption(
                    id="check_quest_journal",
                    name="Check quest journal",
                    description="Review your active quests and objectives",
                    category=ActionCategory.SPECIAL,
                    requirements=[]
                ))

        # Generate objective-specific actions based on active quests
        # This would be expanded to include specific quest objective actions
        # For now, we'll keep it simple with the journal check

        return actions

    def _filter_repetitive_actions(self, actions: List[ActionOption], repetition_context: Dict[str, Any],
                                 location: str, memory_system) -> List[ActionOption]:
        """Filter out actions that would be repetitive"""
        filtered = []
        overused_actions = repetition_context.get("overused_actions", [])
        recent_actions = repetition_context.get("recent_actions", [])

        for action in actions:
            # Skip if action is overused in this location
            if action.id in overused_actions:
                continue

            # Skip if action was used very recently (last 2 actions)
            if action.id in recent_actions[-2:]:
                continue

            # Skip if action is on cooldown
            if memory_system.is_action_on_cooldown(action.id):
                continue

            filtered.append(action)

        return filtered

    def _score_actions(self, actions: List[ActionOption], game_state: Dict[str, Any],
                      memory_system) -> List[Tuple[ActionOption, float]]:
        """Score actions based on relevance and variety"""
        scored = []

        for action in actions:
            score = 1.0

            # Boost exploration actions
            if action.category == ActionCategory.EXPLORATION:
                score += 0.3

            # Boost skill actions for character development
            if action.category == ActionCategory.SKILL:
                score += 0.2

            # Reduce score for high-risk actions if character is weak
            character = game_state.get("character")
            if character and action.risk_level == "high":
                if character.stats["hp"] < character.stats["max_hp"] * 0.3:
                    score -= 0.4

            # Add randomness for variety
            score += random.uniform(-0.1, 0.1)

            scored.append((action, score))

        return sorted(scored, key=lambda x: x[1], reverse=True)

    def _select_diverse_actions(self, scored_actions: List[Tuple[ActionOption, float]],
                              max_options: int) -> List[ActionOption]:
        """Select actions ensuring category diversity"""
        selected = []
        used_categories = set()

        # First pass: select highest scoring action from each category
        for action, score in scored_actions:
            if len(selected) >= max_options:
                break

            if action.category not in used_categories:
                selected.append(action)
                used_categories.add(action.category)

        # Second pass: fill remaining slots with highest scoring actions
        for action, score in scored_actions:
            if len(selected) >= max_options:
                break

            if action not in selected:
                selected.append(action)

        return selected[:max_options]

    def parse_player_input(self, user_input: str, available_actions: List[ActionOption]) -> Tuple[Optional[ActionOption], str]:
        """Parse player input to determine if it's a selection or custom action"""
        user_input = user_input.strip()

        # Check if input is a number (action selection)
        if user_input.isdigit():
            try:
                index = int(user_input) - 1  # Convert to 0-based index
                if 0 <= index < len(available_actions):
                    return available_actions[index], "selection"
            except (ValueError, IndexError):
                pass

        # Check if input matches an action name or ID
        user_lower = user_input.lower()
        for action in available_actions:
            if (user_lower == action.name.lower() or
                user_lower == action.id.lower() or
                user_lower in action.name.lower()):
                return action, "selection"

        # If no match, treat as custom action
        return None, "custom"
