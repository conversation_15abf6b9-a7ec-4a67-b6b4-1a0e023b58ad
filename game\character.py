"""
Character class for player character management
"""
from typing import Dict, List, Any, Optional, Tuple
import config

class Character:
    def __init__(self, name: str = "", traits: List[str] = None, occupation: str = ""):
        """Initialize a new character"""
        self.name = name
        self.traits = traits or []
        self.occupation = occupation
        self.creature_type = ""
        self.level = 1
        self.experience = 0
        self.evolution_stage = 0

        # Backstory and creation information
        self.backstory_name = ""
        self.backstory_synergy_bonuses = {}  # Store synergy bonuses from character creation

        # Base stats - will be set when creature type is chosen
        self.stats = {
            "hp": 0,
            "max_hp": 0,
            "mp": 0,
            "max_mp": 0,
            "attack": 0,
            "defense": 0,
            "speed": 0
        }

        # Abilities and skills
        self.abilities = []
        self.skills = {}

        # Inventory and equipment
        self.inventory = []
        self.equipment = {}

        # Relationships with NPCs
        self.relationships = {}

        # Combat and progression tracking
        self.combat_victories = 0
        self.unique_enemies_defeated = set()
        self.skills_used_count = {}
        self.fusion_discoveries = []

        # Evolution tracking
        self.evolution_progress = {}
        self.available_evolutions = []

        # Reward skill tracking
        self.reward_progress = {}
        self.earned_reward_skills = []
        self.days_survived = 0
        self.total_items_collected = 0
        self.unique_foods_eaten = set()
        self.rare_items_found = 0
        self.leadership_events = 0

    def set_creature_type(self, creature_name: str):
        """Set the character's creature type and initialize stats"""
        for creature in config.STARTING_CREATURES:
            if creature["name"].lower() == creature_name.lower():
                self.creature_type = creature["name"]
                base_stats = creature["base_stats"]

                # Set stats
                self.stats["hp"] = base_stats["hp"]
                self.stats["max_hp"] = base_stats["hp"]
                self.stats["mp"] = base_stats["mp"]
                self.stats["max_mp"] = base_stats["mp"]
                self.stats["attack"] = base_stats["attack"]
                self.stats["defense"] = base_stats["defense"]
                self.stats["speed"] = base_stats["speed"]

                # Set starting abilities
                self.abilities = creature["abilities"].copy()
                break

    def gain_experience(self, amount: int):
        """Add experience and check for level up"""
        self.experience += amount

        # Check for level up (can level up multiple times)
        while True:
            exp_needed = self.level * 100
            if self.experience >= exp_needed:
                self.level_up()
            else:
                break

    def level_up(self):
        """Level up the character"""
        exp_needed = self.level * 100
        excess_exp = self.experience - exp_needed

        self.level += 1
        self.experience = excess_exp  # Carry over excess experience

        # Increase stats
        stat_increases = {
            "max_hp": 10,
            "max_mp": 5,
            "attack": 2,
            "defense": 2,
            "speed": 1
        }

        for stat, increase in stat_increases.items():
            self.stats[stat] += increase

        # Heal to full on level up
        self.stats["hp"] = self.stats["max_hp"]
        self.stats["mp"] = self.stats["max_mp"]

    def learn_ability(self, ability_name: str):
        """Learn a new ability"""
        if ability_name not in self.abilities:
            self.abilities.append(ability_name)
            # Track skill usage for fusion system
            self.skills_used_count[ability_name] = 0

    def use_ability(self, ability_name: str):
        """Track ability usage for progression"""
        if ability_name in self.abilities:
            self.skills_used_count[ability_name] = self.skills_used_count.get(ability_name, 0) + 1

    def record_combat_victory(self, enemy_name: str, exp_gained: int):
        """Record a combat victory for evolution tracking"""
        self.combat_victories += 1
        self.unique_enemies_defeated.add(enemy_name)
        self.gain_experience(exp_gained)

    def discover_fusion(self, fusion_name: str, components: List[str]):
        """Record a skill fusion discovery"""
        fusion_record = {
            "name": fusion_name,
            "components": components,
            "discovered_at_level": self.level
        }
        self.fusion_discoveries.append(fusion_record)

    def earn_reward_skill(self, skill_name: str):
        """Add a reward skill to the character"""
        if skill_name not in self.earned_reward_skills:
            self.earned_reward_skills.append(skill_name)
            self.learn_ability(skill_name)

    def add_item_to_inventory(self, item_name: str, is_rare: bool = False):
        """Add item to inventory and track for rewards"""
        self.inventory.append(item_name)
        self.total_items_collected += 1
        if is_rare:
            self.rare_items_found += 1

    def consume_food(self, food_name: str):
        """Track food consumption for reward system"""
        self.unique_foods_eaten.add(food_name)

    def record_leadership_event(self):
        """Record a leadership event for reward tracking"""
        self.leadership_events += 1

    def advance_day(self):
        """Advance the day counter for survival tracking"""
        self.days_survived += 1

    def take_damage(self, damage: int):
        """Take damage and return if character is still alive"""
        self.stats["hp"] = max(0, self.stats["hp"] - damage)
        return self.stats["hp"] > 0

    def heal(self, amount: int):
        """Heal the character"""
        self.stats["hp"] = min(self.stats["max_hp"], self.stats["hp"] + amount)

    def use_mp(self, amount: int) -> bool:
        """Use MP and return if successful"""
        if self.stats["mp"] >= amount:
            self.stats["mp"] -= amount
            return True
        return False

    def get_status_summary(self) -> str:
        """Get a formatted status summary"""
        return f"""
Name: {self.name}
Type: {self.creature_type}
Level: {self.level}
HP: {self.stats['hp']}/{self.stats['max_hp']}
MP: {self.stats['mp']}/{self.stats['max_mp']}
Attack: {self.stats['attack']}
Defense: {self.stats['defense']}
Speed: {self.stats['speed']}
Abilities: {', '.join(self.abilities)}
        """.strip()

    def to_dict(self) -> Dict[str, Any]:
        """Convert character to dictionary for saving"""
        return {
            "name": self.name,
            "traits": self.traits,
            "occupation": self.occupation,
            "creature_type": self.creature_type,
            "level": self.level,
            "experience": self.experience,
            "evolution_stage": self.evolution_stage,
            "backstory_name": self.backstory_name,
            "backstory_synergy_bonuses": self.backstory_synergy_bonuses,
            "stats": self.stats,
            "abilities": self.abilities,
            "skills": self.skills,
            "inventory": self.inventory,
            "equipment": self.equipment,
            "relationships": self.relationships,
            "combat_victories": self.combat_victories,
            "unique_enemies_defeated": list(self.unique_enemies_defeated),
            "skills_used_count": self.skills_used_count,
            "fusion_discoveries": self.fusion_discoveries,
            "evolution_progress": self.evolution_progress,
            "available_evolutions": self.available_evolutions,
            # Reward tracking fields
            "reward_progress": self.reward_progress,
            "earned_reward_skills": self.earned_reward_skills,
            "days_survived": self.days_survived,
            "total_items_collected": self.total_items_collected,
            "unique_foods_eaten": list(self.unique_foods_eaten),
            "rare_items_found": self.rare_items_found,
            "leadership_events": self.leadership_events
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """Create character from dictionary"""
        char = cls(data.get("name", ""), data.get("traits", []), data.get("occupation", ""))
        char.creature_type = data.get("creature_type", "")
        char.level = data.get("level", 1)
        char.experience = data.get("experience", 0)
        char.evolution_stage = data.get("evolution_stage", 0)
        char.backstory_name = data.get("backstory_name", "")
        char.backstory_synergy_bonuses = data.get("backstory_synergy_bonuses", {})
        char.stats = data.get("stats", {})
        char.abilities = data.get("abilities", [])
        char.skills = data.get("skills", {})
        char.inventory = data.get("inventory", [])
        char.equipment = data.get("equipment", {})
        char.relationships = data.get("relationships", {})
        char.combat_victories = data.get("combat_victories", 0)
        char.unique_enemies_defeated = set(data.get("unique_enemies_defeated", []))
        char.skills_used_count = data.get("skills_used_count", {})
        char.fusion_discoveries = data.get("fusion_discoveries", [])
        char.evolution_progress = data.get("evolution_progress", {})
        char.available_evolutions = data.get("available_evolutions", [])
        # Reward tracking fields
        char.reward_progress = data.get("reward_progress", {})
        char.earned_reward_skills = data.get("earned_reward_skills", [])
        char.days_survived = data.get("days_survived", 0)
        char.total_items_collected = data.get("total_items_collected", 0)
        char.unique_foods_eaten = set(data.get("unique_foods_eaten", []))
        char.rare_items_found = data.get("rare_items_found", 0)
        char.leadership_events = data.get("leadership_events", 0)
        return char
