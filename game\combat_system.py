"""
Turn-based combat system for Me? Reincarnated?
"""
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from .skill_system import SkillFusionSystem, Skill, SkillType

class CombatAction(Enum):
    ATTACK = "attack"
    DEFEND = "defend"
    SKILL = "skill"
    ITEM = "item"
    FLEE = "flee"

class StatusEffect(Enum):
    POISON = "poison"
    BURN = "burn"
    FREEZE = "freeze"
    STUN = "stun"
    REGENERATION = "regeneration"
    SPEED_BOOST = "speed_boost"
    DEFENSE_BOOST = "defense_boost"

@dataclass
class StatusEffectInstance:
    effect: StatusEffect
    duration: int
    power: int
    description: str

@dataclass
class Enemy:
    name: str
    level: int
    hp: int
    max_hp: int
    mp: int
    max_mp: int
    attack: int
    defense: int
    speed: int
    abilities: List[str]
    resistances: List[str]
    weaknesses: List[str]
    exp_reward: int
    loot_table: List[Dict[str, Any]]
    ai_behavior: str = "aggressive"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "level": self.level,
            "hp": self.hp,
            "max_hp": self.max_hp,
            "mp": self.mp,
            "max_mp": self.max_mp,
            "attack": self.attack,
            "defense": self.defense,
            "speed": self.speed,
            "abilities": self.abilities,
            "resistances": self.resistances,
            "weaknesses": self.weaknesses,
            "exp_reward": self.exp_reward,
            "loot_table": self.loot_table,
            "ai_behavior": self.ai_behavior
        }

class CombatSystem:
    """Manages turn-based combat encounters"""
    
    def __init__(self):
        self.skill_system = SkillFusionSystem()
        self.enemy_database = self._initialize_enemy_database()
        self.active_combat = None
        
    def _initialize_enemy_database(self) -> Dict[str, Enemy]:
        """Initialize enemy database with creatures for different locations"""
        enemies = {}
        
        # Forest Enemies
        enemies["Forest Wolf"] = Enemy(
            name="Forest Wolf",
            level=2,
            hp=35, max_hp=35,
            mp=10, max_mp=10,
            attack=12, defense=6, speed=8,
            abilities=["Bite", "Howl"],
            resistances=[], weaknesses=["Fire"],
            exp_reward=25,
            loot_table=[{"item": "Wolf Fang", "chance": 0.3}, {"item": "Wolf Pelt", "chance": 0.5}]
        )
        
        enemies["Forest Sprite"] = Enemy(
            name="Forest Sprite",
            level=1,
            hp=20, max_hp=20,
            mp=25, max_mp=25,
            attack=6, defense=4, speed=12,
            abilities=["Nature Magic", "Heal"],
            resistances=["Nature"], weaknesses=["Dark"],
            exp_reward=15,
            loot_table=[{"item": "Sprite Dust", "chance": 0.4}, {"item": "Healing Herb", "chance": 0.6}],
            ai_behavior="defensive"
        )
        
        enemies["Ancient Treant"] = Enemy(
            name="Ancient Treant",
            level=8,
            hp=120, max_hp=120,
            mp=40, max_mp=40,
            attack=18, defense=15, speed=3,
            abilities=["Root Strike", "Bark Armor", "Nature's Wrath"],
            resistances=["Nature", "Physical"], weaknesses=["Fire", "Acid"],
            exp_reward=100,
            loot_table=[{"item": "Ancient Wood", "chance": 0.8}, {"item": "Life Essence", "chance": 0.3}],
            ai_behavior="guardian"
        )
        
        # Cave Enemies
        enemies["Crystal Bat"] = Enemy(
            name="Crystal Bat",
            level=3,
            hp=25, max_hp=25,
            mp=15, max_mp=15,
            attack=10, defense=5, speed=15,
            abilities=["Sonic Screech", "Crystal Shard"],
            resistances=["Sound"], weaknesses=["Light"],
            exp_reward=30,
            loot_table=[{"item": "Crystal Fragment", "chance": 0.4}, {"item": "Bat Wing", "chance": 0.3}]
        )
        
        enemies["Gem Golem"] = Enemy(
            name="Gem Golem",
            level=6,
            hp=80, max_hp=80,
            mp=20, max_mp=20,
            attack=16, defense=20, speed=4,
            abilities=["Crystal Punch", "Gem Shield", "Reflect"],
            resistances=["Physical", "Magic"], weaknesses=["Sonic"],
            exp_reward=75,
            loot_table=[{"item": "Magic Crystal", "chance": 0.6}, {"item": "Golem Core", "chance": 0.2}],
            ai_behavior="defensive"
        )
        
        # Ruins Enemies
        enemies["Skeleton Warrior"] = Enemy(
            name="Skeleton Warrior",
            level=4,
            hp=40, max_hp=40,
            mp=5, max_mp=5,
            attack=14, defense=8, speed=6,
            abilities=["Bone Strike", "Undead Resilience"],
            resistances=["Dark", "Poison"], weaknesses=["Light", "Blunt"],
            exp_reward=40,
            loot_table=[{"item": "Bone Fragment", "chance": 0.5}, {"item": "Rusty Sword", "chance": 0.2}]
        )
        
        return enemies
    
    def start_combat(self, character, enemy_name: str, location: str = "") -> Dict[str, Any]:
        """Initialize a combat encounter"""
        if enemy_name not in self.enemy_database:
            return {"success": False, "message": f"Unknown enemy: {enemy_name}"}
        
        enemy = Enemy(**self.enemy_database[enemy_name].to_dict())  # Create a copy
        
        self.active_combat = {
            "character": character,
            "enemy": enemy,
            "turn": 1,
            "character_status_effects": [],
            "enemy_status_effects": [],
            "combat_log": [],
            "location": location
        }
        
        # Determine turn order based on speed
        char_speed = character.stats["speed"]
        enemy_speed = enemy.speed
        
        if char_speed >= enemy_speed:
            first_turn = "character"
        else:
            first_turn = "enemy"
        
        self.active_combat["current_turn"] = first_turn
        
        return {
            "success": True,
            "message": f"Combat started with {enemy.name}!",
            "enemy_info": {
                "name": enemy.name,
                "level": enemy.level,
                "hp": enemy.hp,
                "max_hp": enemy.max_hp
            },
            "first_turn": first_turn
        }
    
    def get_available_actions(self, character) -> List[Dict[str, Any]]:
        """Get available actions for the character"""
        actions = [
            {"action": "attack", "name": "Attack", "description": "Basic physical attack"},
            {"action": "defend", "name": "Defend", "description": "Reduce damage and gain MP"}
        ]
        
        # Add skill actions
        for ability in character.abilities:
            skill = self.skill_system.get_skill_info(ability)
            if skill and skill.skill_type == SkillType.COMBAT:
                actions.append({
                    "action": "skill",
                    "name": ability,
                    "description": skill.description,
                    "mp_cost": skill.mp_cost
                })
        
        # Add item actions (placeholder for now)
        actions.append({"action": "item", "name": "Use Item", "description": "Use an item from inventory"})
        actions.append({"action": "flee", "name": "Flee", "description": "Attempt to escape combat"})
        
        return actions
    
    def execute_character_action(self, action: str, target: str = None) -> Dict[str, Any]:
        """Execute a character's combat action"""
        if not self.active_combat:
            return {"success": False, "message": "No active combat"}
        
        character = self.active_combat["character"]
        enemy = self.active_combat["enemy"]
        result = {"success": True, "messages": [], "effects": []}
        
        if action == "attack":
            damage = self._calculate_damage(character.stats["attack"], enemy.defense)
            enemy.hp = max(0, enemy.hp - damage)
            result["messages"].append(f"You attack {enemy.name} for {damage} damage!")
            
        elif action == "defend":
            result["messages"].append("You take a defensive stance.")
            result["effects"].append({"type": "defense_boost", "duration": 1})
            # Gain some MP
            mp_gain = min(5, character.stats["max_mp"] - character.stats["mp"])
            character.stats["mp"] += mp_gain
            if mp_gain > 0:
                result["messages"].append(f"You recover {mp_gain} MP.")
                
        elif action == "skill" and target:
            skill_result = self._use_skill(character, enemy, target)
            result["messages"].extend(skill_result["messages"])
            result["effects"].extend(skill_result["effects"])
            
        elif action == "flee":
            flee_chance = min(0.8, character.stats["speed"] / (enemy.speed + 1))
            if random.random() < flee_chance:
                result["messages"].append("You successfully flee from combat!")
                result["combat_ended"] = True
                result["victory"] = False
                self.active_combat = None
                return result
            else:
                result["messages"].append("You failed to escape!")
        
        # Check if enemy is defeated
        if enemy.hp <= 0:
            result["combat_ended"] = True
            result["victory"] = True
            victory_result = self._handle_victory(character, enemy)
            result["messages"].extend(victory_result["messages"])
            result["rewards"] = victory_result["rewards"]
            self.active_combat = None
        else:
            # Enemy turn
            enemy_result = self._execute_enemy_turn()
            result["messages"].extend(enemy_result["messages"])
            
            # Check if character is defeated
            if character.stats["hp"] <= 0:
                result["combat_ended"] = True
                result["victory"] = False
                result["messages"].append("You have been defeated!")
                self.active_combat = None
        
        return result
    
    def _calculate_damage(self, attack: int, defense: int) -> int:
        """Calculate damage with some randomness"""
        base_damage = max(1, attack - defense)
        # Add 20% randomness
        multiplier = random.uniform(0.8, 1.2)
        return max(1, int(base_damage * multiplier))
    
    def _use_skill(self, character, enemy, skill_name: str) -> Dict[str, Any]:
        """Use a character skill in combat"""
        skill = self.skill_system.get_skill_info(skill_name)
        result = {"messages": [], "effects": []}
        
        if not skill:
            result["messages"].append(f"Unknown skill: {skill_name}")
            return result
        
        # Check MP cost
        if character.stats["mp"] < skill.mp_cost:
            result["messages"].append(f"Not enough MP to use {skill_name}!")
            return result
        
        # Use MP
        character.stats["mp"] -= skill.mp_cost
        
        # Apply skill effects based on skill type and effects
        if "damage" in skill.effects:
            damage = self._calculate_damage(skill.power, enemy.defense)
            enemy.hp = max(0, enemy.hp - damage)
            result["messages"].append(f"You use {skill_name} on {enemy.name} for {damage} damage!")
        
        if "poison" in skill.effects:
            result["messages"].append(f"{enemy.name} is poisoned!")
            result["effects"].append({"type": "poison", "target": "enemy", "duration": 3, "power": 5})
        
        if "trap" in skill.effects:
            result["messages"].append(f"{enemy.name} is trapped!")
            result["effects"].append({"type": "stun", "target": "enemy", "duration": 1, "power": 0})
        
        return result
    
    def _execute_enemy_turn(self) -> Dict[str, Any]:
        """Execute the enemy's turn using simple AI"""
        enemy = self.active_combat["enemy"]
        character = self.active_combat["character"]
        result = {"messages": []}
        
        # Simple AI: attack most of the time, use abilities occasionally
        if enemy.mp >= 10 and random.random() < 0.3 and enemy.abilities:
            # Use a random ability
            ability = random.choice(enemy.abilities)
            result["messages"].append(f"{enemy.name} uses {ability}!")
            
            # Simple ability effects
            if "heal" in ability.lower():
                heal_amount = min(15, enemy.max_hp - enemy.hp)
                enemy.hp += heal_amount
                result["messages"].append(f"{enemy.name} heals for {heal_amount} HP!")
            else:
                # Damage ability
                damage = self._calculate_damage(enemy.attack + 5, character.stats["defense"])
                character.stats["hp"] = max(0, character.stats["hp"] - damage)
                result["messages"].append(f"{enemy.name}'s {ability} hits you for {damage} damage!")
        else:
            # Basic attack
            damage = self._calculate_damage(enemy.attack, character.stats["defense"])
            character.stats["hp"] = max(0, character.stats["hp"] - damage)
            result["messages"].append(f"{enemy.name} attacks you for {damage} damage!")
        
        return result
    
    def _handle_victory(self, character, enemy: Enemy) -> Dict[str, Any]:
        """Handle combat victory rewards"""
        result = {"messages": [], "rewards": {}}
        
        # Experience reward
        exp_gained = enemy.exp_reward
        character.gain_experience(exp_gained)
        result["messages"].append(f"You gain {exp_gained} experience!")
        result["rewards"]["experience"] = exp_gained
        
        # Loot rewards
        loot_gained = []
        for loot_item in enemy.loot_table:
            if random.random() < loot_item["chance"]:
                loot_gained.append(loot_item["item"])
                character.inventory.append(loot_item["item"])
        
        if loot_gained:
            result["messages"].append(f"You found: {', '.join(loot_gained)}")
            result["rewards"]["loot"] = loot_gained
        
        return result
    
    def get_combat_status(self) -> Optional[Dict[str, Any]]:
        """Get current combat status"""
        if not self.active_combat:
            return None
        
        character = self.active_combat["character"]
        enemy = self.active_combat["enemy"]
        
        return {
            "turn": self.active_combat["turn"],
            "current_turn": self.active_combat["current_turn"],
            "character": {
                "hp": character.stats["hp"],
                "max_hp": character.stats["max_hp"],
                "mp": character.stats["mp"],
                "max_mp": character.stats["max_mp"]
            },
            "enemy": {
                "name": enemy.name,
                "hp": enemy.hp,
                "max_hp": enemy.max_hp,
                "level": enemy.level
            }
        }
