"""
Dialogue system for managing NPC conversations and dialogue trees
"""
from typing import Dict, List, Any, Optional, Tu<PERSON>, Callable
from dataclasses import dataclass, field
from enum import Enum
import random

class DialogueNodeType(Enum):
    GREETING = "greeting"
    QUESTION = "question"
    RESPONSE = "response"
    QUEST_OFFER = "quest_offer"
    QUEST_PROGRESS = "quest_progress"
    TRADE = "trade"
    TEACHING = "teaching"
    LORE = "lore"
    FAREWELL = "farewell"
    CONDITIONAL = "conditional"

class DialogueConditionType(Enum):
    RELATIONSHIP = "relationship"
    LEVEL = "level"
    QUEST_STATUS = "quest_status"
    ITEM_POSSESSION = "item_possession"
    SKILL_KNOWN = "skill_known"
    FIRST_MEETING = "first_meeting"
    TIME_OF_DAY = "time_of_day"
    LOCATION = "location"

@dataclass
class DialogueCondition:
    condition_type: DialogueConditionType
    parameter: str
    value: Any
    operator: str = "=="  # ==, !=, >, <, >=, <=, in, not_in

@dataclass
class DialogueChoice:
    text: str
    next_node_id: str
    conditions: List[DialogueCondition] = field(default_factory=list)
    relationship_change: int = 0
    consequences: Dict[str, Any] = field(default_factory=dict)
    skill_requirements: List[str] = field(default_factory=list)

@dataclass
class DialogueNode:
    node_id: str
    node_type: DialogueNodeType
    npc_text: str
    choices: List[DialogueChoice] = field(default_factory=list)
    conditions: List[DialogueCondition] = field(default_factory=list)
    auto_next: Optional[str] = None  # Automatically go to next node
    consequences: Dict[str, Any] = field(default_factory=dict)
    one_time: bool = False  # Can only be accessed once

@dataclass
class DialogueTree:
    npc_name: str
    root_node_id: str
    nodes: Dict[str, DialogueNode] = field(default_factory=dict)
    accessed_nodes: List[str] = field(default_factory=list)  # Track accessed one-time nodes

class DialogueSystem:
    """Manages dialogue trees and conversations with NPCs"""

    def __init__(self):
        self.dialogue_trees = self._initialize_dialogue_trees()
        self.active_conversations = {}  # player_id -> (npc_name, current_node_id)

    def _initialize_dialogue_trees(self) -> Dict[str, DialogueTree]:
        """Initialize dialogue trees for all NPCs"""
        trees = {}

        # Elder Sprite dialogue tree
        trees["Elder Sprite"] = self._create_elder_sprite_dialogue()

        # Lost Traveler dialogue tree
        trees["Lost Traveler"] = self._create_lost_traveler_dialogue()

        # Dwarf Miner dialogue tree
        trees["Dwarf Miner"] = self._create_dwarf_miner_dialogue()

        # Crystal Sage dialogue tree
        trees["Crystal Sage"] = self._create_crystal_sage_dialogue()

        # Fairy Queen dialogue tree
        trees["Fairy Queen"] = self._create_fairy_queen_dialogue()

        # Astronomer Ghost dialogue tree
        trees["Astronomer Ghost"] = self._create_astronomer_ghost_dialogue()

        return trees

    def _create_elder_sprite_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Elder Sprite"""
        tree = DialogueTree("Elder Sprite", "greeting")

        # Greeting node
        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="Young one, the forest whispers of your arrival. I am the Elder Sprite, guardian of these ancient woods. What brings you to seek my wisdom?",
            choices=[
                DialogueChoice("I want to learn about nature magic", "nature_magic", relationship_change=2),
                DialogueChoice("Can you tell me about this forest?", "forest_lore", relationship_change=1),
                DialogueChoice("I'm just exploring", "casual_chat"),
                DialogueChoice("I should go", "farewell")
            ]
        )

        # Nature magic node
        tree.nodes["nature_magic"] = DialogueNode(
            node_id="nature_magic",
            node_type=DialogueNodeType.TEACHING,
            npc_text="Ah, a seeker of natural wisdom! Nature magic flows through all living things. To master it, you must first understand the harmony between all creatures.",
            choices=[
                DialogueChoice("Teach me a nature spell", "learn_spell",
                              conditions=[DialogueCondition(DialogueConditionType.RELATIONSHIP, "Elder Sprite", 10, ">=")],
                              consequences={"learn_skill": "Nature's Touch"}),
                DialogueChoice("How do I improve my connection to nature?", "nature_advice"),
                DialogueChoice("What about the other forest spirits?", "forest_spirits"),
                DialogueChoice("Thank you for the wisdom", "farewell", relationship_change=1)
            ]
        )

        # Forest lore node
        tree.nodes["forest_lore"] = DialogueNode(
            node_id="forest_lore",
            node_type=DialogueNodeType.LORE,
            npc_text="This forest has stood for millennia, home to countless spirits and magical creatures. The trees themselves hold memories of ages past.",
            choices=[
                DialogueChoice("Tell me about the ancient times", "ancient_history"),
                DialogueChoice("Are there dangerous creatures here?", "forest_dangers"),
                DialogueChoice("Where are the other spirits?", "forest_spirits"),
                DialogueChoice("I understand", "main_menu")
            ]
        )

        # Add more nodes...
        tree.nodes["learn_spell"] = DialogueNode(
            node_id="learn_spell",
            node_type=DialogueNodeType.TEACHING,
            npc_text="Very well. Place your hand upon this ancient oak and feel the life force within. Let it flow through you...",
            auto_next="spell_learned",
            consequences={"learn_skill": "Nature's Touch", "gain_experience": 50}
        )

        tree.nodes["spell_learned"] = DialogueNode(
            node_id="spell_learned",
            node_type=DialogueNodeType.RESPONSE,
            npc_text="Excellent! You have learned Nature's Touch. Use it wisely to heal and nurture the natural world.",
            choices=[
                DialogueChoice("Thank you, Elder Sprite!", "farewell", relationship_change=3),
                DialogueChoice("Can you teach me more?", "nature_magic")
            ],
            one_time=True
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="May the forest guide your path, young one. Return when you seek wisdom.",
            auto_next="end_conversation"
        )

        return tree

    def _create_lost_traveler_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Lost Traveler"""
        tree = DialogueTree("Lost Traveler", "greeting")

        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="Oh! Another soul in this endless forest... I'm Marcus, and I've been lost here for... well, I've lost count of the days. Have you seen any way out?",
            choices=[
                DialogueChoice("How did you get lost?", "lost_story", relationship_change=1),
                DialogueChoice("I'm looking for a way out too", "fellow_seeker", relationship_change=2),
                DialogueChoice("Maybe I can help you", "offer_help", relationship_change=3),
                DialogueChoice("Sorry, I haven't seen any exits", "no_help")
            ]
        )

        tree.nodes["lost_story"] = DialogueNode(
            node_id="lost_story",
            node_type=DialogueNodeType.LORE,
            npc_text="I was a merchant, traveling between towns. Took a shortcut through what I thought was a normal forest... but the paths keep changing. I swear I've walked in circles for weeks.",
            choices=[
                DialogueChoice("The paths change?", "changing_paths"),
                DialogueChoice("What were you trading?", "merchant_goods"),
                DialogueChoice("Don't give up hope", "encourage", relationship_change=2),
                DialogueChoice("That's unfortunate", "main_menu")
            ]
        )

        tree.nodes["offer_help"] = DialogueNode(
            node_id="offer_help",
            node_type=DialogueNodeType.QUEST_OFFER,
            npc_text="You... you would help me? I have a map of where I've been, but it's incomplete. If you could help me map the forest properly, maybe we could find the way out together!",
            choices=[
                DialogueChoice("I'll help you map the forest", "accept_quest",
                              consequences={"start_quest": "map_mysterious_forest"}),
                DialogueChoice("I'll think about it", "main_menu"),
                DialogueChoice("I can't right now", "decline_quest", relationship_change=-1)
            ]
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="Safe travels, friend. If you find any paths I haven't tried, please let me know...",
            auto_next="end_conversation"
        )

        return tree

    def _create_dwarf_miner_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Dwarf Miner"""
        tree = DialogueTree("Dwarf Miner", "greeting")

        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="Aye, what brings ye to these depths? Name's Thorek Ironpick. Been mining these caves for nigh on thirty years. Finest crystals in all the realm, they are!",
            choices=[
                DialogueChoice("Can you teach me about mining?", "mining_lessons", relationship_change=1),
                DialogueChoice("What kind of crystals do you find?", "crystal_info"),
                DialogueChoice("Are these caves dangerous?", "cave_dangers"),
                DialogueChoice("I'm just passing through", "casual_visit")
            ]
        )

        tree.nodes["mining_lessons"] = DialogueNode(
            node_id="mining_lessons",
            node_type=DialogueNodeType.TEACHING,
            npc_text="Mining, eh? It's honest work, but ye need to know yer stones. Each crystal has its own song, its own way of breaking. Listen close and the mountain will teach ye.",
            choices=[
                DialogueChoice("Teach me the basics", "learn_mining",
                              conditions=[DialogueCondition(DialogueConditionType.RELATIONSHIP, "Dwarf Miner", 5, ">=")],
                              consequences={"learn_skill": "Basic Mining"}),
                DialogueChoice("What tools do I need?", "mining_tools"),
                DialogueChoice("How do you identify crystals?", "crystal_identification"),
                DialogueChoice("Thank you for the advice", "farewell", relationship_change=1)
            ]
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="Aye, off with ye then. Mind the loose rocks on yer way out!",
            auto_next="end_conversation"
        )

        return tree

    def _create_crystal_sage_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Crystal Sage"""
        tree = DialogueTree("Crystal Sage", "greeting")

        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="The crystals sing of your presence... I am Zephyr, keeper of crystal mysteries. Your aura resonates with potential. What knowledge do you seek?",
            choices=[
                DialogueChoice("Teach me crystal magic", "crystal_magic", relationship_change=1),
                DialogueChoice("Can you read my future?", "divination"),
                DialogueChoice("What are these crystals?", "crystal_lore"),
                DialogueChoice("I seek power", "power_seeker", relationship_change=-1)
            ]
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="The crystals will remember our meeting. May their light guide your path...",
            auto_next="end_conversation"
        )

        return tree

    def _create_fairy_queen_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Fairy Queen"""
        tree = DialogueTree("Fairy Queen", "greeting")

        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="Welcome to our peaceful realm, little one! I am Lunaria, Queen of the Moonlit Fairies. Your spirit shines with such interesting colors! How may we help you?",
            choices=[
                DialogueChoice("Your realm is beautiful", "compliment", relationship_change=2),
                DialogueChoice("Can you teach me fairy magic?", "fairy_magic"),
                DialogueChoice("I need protection", "seek_protection"),
                DialogueChoice("I'm just visiting", "casual_visit")
            ]
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="May moonlight bless your journey, dear one! You are always welcome in our meadow!",
            auto_next="end_conversation"
        )

        return tree

    def _create_astronomer_ghost_dialogue(self) -> DialogueTree:
        """Create dialogue tree for Astronomer Ghost"""
        tree = DialogueTree("Astronomer Ghost", "greeting")

        tree.nodes["greeting"] = DialogueNode(
            node_id="greeting",
            node_type=DialogueNodeType.GREETING,
            npc_text="Ah, a visitor to my eternal observatory... I am Professor Stellaris, once of the Royal Academy. The stars have foretold your coming, young seeker. What cosmic mysteries intrigue you?",
            choices=[
                DialogueChoice("Tell me about the stars", "astronomy", relationship_change=1),
                DialogueChoice("Can you teach me time magic?", "time_magic"),
                DialogueChoice("What happened to you?", "ghost_story"),
                DialogueChoice("I should let you study", "farewell")
            ]
        )

        tree.nodes["farewell"] = DialogueNode(
            node_id="farewell",
            node_type=DialogueNodeType.FAREWELL,
            npc_text="Time flows ever onward, young one. May the stars illuminate your path through eternity...",
            auto_next="end_conversation"
        )

        return tree

    def start_conversation(self, player_id: str, npc_name: str, character, memory_system) -> Tuple[str, List[str]]:
        """Start a conversation with an NPC"""
        tree = self.dialogue_trees.get(npc_name)
        if not tree:
            return f"No dialogue available for {npc_name}.", []

        # Check if this is first meeting
        met_npcs = memory_system.long_term_memory.get("met_npcs", {})
        is_first_meeting = npc_name not in met_npcs

        # Find appropriate starting node
        current_node_id = self._find_starting_node(tree, character, memory_system, is_first_meeting)
        current_node = tree.nodes.get(current_node_id)

        if not current_node:
            return f"Dialogue error with {npc_name}.", []

        # Store active conversation
        self.active_conversations[player_id] = (npc_name, current_node_id)

        # Get available choices
        choices = self._get_available_choices(current_node, character, memory_system)
        choice_texts = [f"{i+1}. {choice.text}" for i, choice in enumerate(choices)]

        return current_node.npc_text, choice_texts

    def process_dialogue_choice(self, player_id: str, choice_index: int, character, memory_system) -> Tuple[str, List[str], Dict[str, Any]]:
        """Process a dialogue choice and return response"""
        if player_id not in self.active_conversations:
            return "No active conversation.", [], {}

        npc_name, current_node_id = self.active_conversations[player_id]
        tree = self.dialogue_trees[npc_name]
        current_node = tree.nodes[current_node_id]

        # Get available choices
        choices = self._get_available_choices(current_node, character, memory_system)

        if choice_index < 0 or choice_index >= len(choices):
            return "Invalid choice.", [f"{i+1}. {choice.text}" for i, choice in enumerate(choices)], {}

        selected_choice = choices[choice_index]
        consequences = {}

        # Apply relationship change
        if selected_choice.relationship_change != 0:
            memory_system.update_npc_relationship(
                npc_name,
                f"Dialogue choice: {selected_choice.text}",
                selected_choice.relationship_change
            )
            consequences["relationship_change"] = selected_choice.relationship_change

        # Apply choice consequences
        consequences.update(selected_choice.consequences)

        # Move to next node
        next_node_id = selected_choice.next_node_id

        if next_node_id == "end_conversation":
            del self.active_conversations[player_id]
            return "Conversation ended.", [], consequences

        next_node = tree.nodes.get(next_node_id)
        if not next_node:
            del self.active_conversations[player_id]
            return "Conversation ended unexpectedly.", [], consequences

        # Update conversation state
        self.active_conversations[player_id] = (npc_name, next_node_id)

        # Mark one-time nodes as accessed
        if next_node.one_time and next_node_id not in tree.accessed_nodes:
            tree.accessed_nodes.append(next_node_id)

        # Apply node consequences
        consequences.update(next_node.consequences)

        # Check for auto-next
        if next_node.auto_next:
            auto_next_node = tree.nodes.get(next_node.auto_next)
            if auto_next_node:
                self.active_conversations[player_id] = (npc_name, next_node.auto_next)
                auto_choices = self._get_available_choices(auto_next_node, character, memory_system)
                auto_choice_texts = [f"{i+1}. {choice.text}" for i, choice in enumerate(auto_choices)]
                return auto_next_node.npc_text, auto_choice_texts, consequences

        # Get new choices
        new_choices = self._get_available_choices(next_node, character, memory_system)
        choice_texts = [f"{i+1}. {choice.text}" for i, choice in enumerate(new_choices)]

        return next_node.npc_text, choice_texts, consequences

    def _find_starting_node(self, tree: DialogueTree, character, memory_system, is_first_meeting: bool) -> str:
        """Find the appropriate starting node for a conversation"""
        # For now, always start with greeting
        # In the future, this could be more sophisticated based on conditions
        return tree.root_node_id

    def _get_available_choices(self, node: DialogueNode, character, memory_system) -> List[DialogueChoice]:
        """Get choices available to the player based on conditions"""
        available_choices = []

        for choice in node.choices:
            if self._check_choice_conditions(choice, character, memory_system):
                available_choices.append(choice)

        return available_choices

    def _check_choice_conditions(self, choice: DialogueChoice, character, memory_system) -> bool:
        """Check if a dialogue choice is available based on conditions"""
        # Check skill requirements
        for skill in choice.skill_requirements:
            if skill not in character.abilities:
                return False

        # Check dialogue conditions
        for condition in choice.conditions:
            if not self._evaluate_condition(condition, character, memory_system):
                return False

        return True

    def _evaluate_condition(self, condition: DialogueCondition, character, memory_system) -> bool:
        """Evaluate a dialogue condition"""
        if condition.condition_type == DialogueConditionType.RELATIONSHIP:
            npc_data = memory_system.long_term_memory.get("met_npcs", {}).get(condition.parameter, {})
            relationship = npc_data.get("relationship", 0)
            return self._compare_values(relationship, condition.value, condition.operator)

        elif condition.condition_type == DialogueConditionType.LEVEL:
            return self._compare_values(character.level, condition.value, condition.operator)

        elif condition.condition_type == DialogueConditionType.SKILL_KNOWN:
            has_skill = condition.parameter in character.abilities
            return self._compare_values(has_skill, condition.value, condition.operator)

        # Add more condition types as needed
        return True

    def _compare_values(self, actual, expected, operator: str) -> bool:
        """Compare two values using the specified operator"""
        if operator == "==":
            return actual == expected
        elif operator == "!=":
            return actual != expected
        elif operator == ">":
            return actual > expected
        elif operator == "<":
            return actual < expected
        elif operator == ">=":
            return actual >= expected
        elif operator == "<=":
            return actual <= expected
        elif operator == "in":
            return actual in expected
        elif operator == "not_in":
            return actual not in expected
        return False
