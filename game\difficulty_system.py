"""
Dynamic Difficulty Adjustment System for Me? Reincarnated?
"""
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import statistics

class DifficultyLevel(Enum):
    VERY_EASY = "very_easy"
    EASY = "easy"
    NORMAL = "normal"
    HARD = "hard"
    VERY_HARD = "very_hard"

@dataclass
class PerformanceMetrics:
    combat_win_rate: float
    average_combat_duration: float
    death_count: int
    skill_usage_frequency: float
    exploration_thoroughness: float
    quest_completion_rate: float

class DifficultyAdjustmentSystem:
    """Dynamically adjusts game difficulty based on player performance"""
    
    def __init__(self):
        self.current_difficulty = DifficultyLevel.NORMAL
        self.performance_history = []
        self.adjustment_thresholds = self._initialize_thresholds()
        self.difficulty_modifiers = self._initialize_modifiers()
        
        # Performance tracking
        self.combat_results = []  # List of (victory: bool, duration: int)
        self.deaths = 0
        self.skills_used = 0
        self.actions_taken = 0
        self.locations_discovered = set()
        self.quests_completed = 0
        self.quests_attempted = 0
        
    def _initialize_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Initialize thresholds for difficulty adjustment"""
        return {
            "combat_win_rate": {
                "very_easy": 0.95,
                "easy": 0.85,
                "normal": 0.70,
                "hard": 0.55,
                "very_hard": 0.40
            },
            "death_frequency": {
                "very_easy": 0.02,  # Deaths per 50 actions
                "easy": 0.04,
                "normal": 0.08,
                "hard": 0.15,
                "very_hard": 0.25
            },
            "skill_usage": {
                "very_easy": 0.15,  # Skills per action
                "easy": 0.20,
                "normal": 0.30,
                "hard": 0.40,
                "very_hard": 0.50
            }
        }
    
    def _initialize_modifiers(self) -> Dict[DifficultyLevel, Dict[str, float]]:
        """Initialize difficulty modifiers for game systems"""
        return {
            DifficultyLevel.VERY_EASY: {
                "enemy_health": 0.6,
                "enemy_damage": 0.5,
                "exp_multiplier": 1.5,
                "loot_chance": 1.3,
                "skill_mp_cost": 0.7
            },
            DifficultyLevel.EASY: {
                "enemy_health": 0.8,
                "enemy_damage": 0.7,
                "exp_multiplier": 1.2,
                "loot_chance": 1.1,
                "skill_mp_cost": 0.85
            },
            DifficultyLevel.NORMAL: {
                "enemy_health": 1.0,
                "enemy_damage": 1.0,
                "exp_multiplier": 1.0,
                "loot_chance": 1.0,
                "skill_mp_cost": 1.0
            },
            DifficultyLevel.HARD: {
                "enemy_health": 1.3,
                "enemy_damage": 1.2,
                "exp_multiplier": 0.9,
                "loot_chance": 0.8,
                "skill_mp_cost": 1.2
            },
            DifficultyLevel.VERY_HARD: {
                "enemy_health": 1.6,
                "enemy_damage": 1.5,
                "exp_multiplier": 0.8,
                "loot_chance": 0.6,
                "skill_mp_cost": 1.4
            }
        }
    
    def record_combat_result(self, victory: bool, duration: int):
        """Record the result of a combat encounter"""
        self.combat_results.append((victory, duration))
        
        # Keep only recent results (last 20 combats)
        if len(self.combat_results) > 20:
            self.combat_results.pop(0)
    
    def record_death(self):
        """Record a player death"""
        self.deaths += 1
    
    def record_skill_usage(self):
        """Record skill usage"""
        self.skills_used += 1
    
    def record_action(self):
        """Record any player action"""
        self.actions_taken += 1
    
    def record_location_discovery(self, location_name: str):
        """Record discovery of a new location"""
        self.locations_discovered.add(location_name)
    
    def record_quest_completion(self, success: bool):
        """Record quest completion attempt"""
        self.quests_attempted += 1
        if success:
            self.quests_completed += 1
    
    def calculate_performance_metrics(self) -> PerformanceMetrics:
        """Calculate current performance metrics"""
        
        # Combat win rate
        if self.combat_results:
            wins = sum(1 for victory, _ in self.combat_results if victory)
            combat_win_rate = wins / len(self.combat_results)
            
            # Average combat duration
            durations = [duration for _, duration in self.combat_results]
            average_combat_duration = statistics.mean(durations) if durations else 0
        else:
            combat_win_rate = 1.0  # No combat yet, assume good
            average_combat_duration = 0
        
        # Death frequency (deaths per 50 actions)
        death_frequency = (self.deaths / max(self.actions_taken, 1)) * 50
        
        # Skill usage frequency
        skill_usage_frequency = self.skills_used / max(self.actions_taken, 1)
        
        # Exploration thoroughness (placeholder - would need more complex calculation)
        exploration_thoroughness = min(len(self.locations_discovered) / 7, 1.0)
        
        # Quest completion rate
        quest_completion_rate = (self.quests_completed / max(self.quests_attempted, 1)) if self.quests_attempted > 0 else 1.0
        
        return PerformanceMetrics(
            combat_win_rate=combat_win_rate,
            average_combat_duration=average_combat_duration,
            death_count=self.deaths,
            skill_usage_frequency=skill_usage_frequency,
            exploration_thoroughness=exploration_thoroughness,
            quest_completion_rate=quest_completion_rate
        )
    
    def should_adjust_difficulty(self) -> Tuple[bool, DifficultyLevel]:
        """Determine if difficulty should be adjusted and to what level"""
        
        # Need minimum data to make adjustments
        if self.actions_taken < 20:
            return False, self.current_difficulty
        
        metrics = self.calculate_performance_metrics()
        
        # Calculate difficulty score based on performance
        difficulty_score = 0
        
        # Combat performance (40% weight)
        if metrics.combat_win_rate > 0.9:
            difficulty_score += 2  # Too easy
        elif metrics.combat_win_rate > 0.8:
            difficulty_score += 1
        elif metrics.combat_win_rate < 0.4:
            difficulty_score -= 2  # Too hard
        elif metrics.combat_win_rate < 0.6:
            difficulty_score -= 1
        
        # Death frequency (30% weight)
        death_rate = (metrics.death_count / max(self.actions_taken, 1)) * 50
        if death_rate > 0.2:
            difficulty_score -= 2  # Too many deaths
        elif death_rate > 0.1:
            difficulty_score -= 1
        elif death_rate < 0.02:
            difficulty_score += 1  # Too few deaths
        
        # Skill usage (20% weight)
        if metrics.skill_usage_frequency < 0.1:
            difficulty_score += 1  # Not using skills, maybe too easy
        elif metrics.skill_usage_frequency > 0.5:
            difficulty_score -= 1  # Overusing skills, maybe too hard
        
        # Quest completion (10% weight)
        if metrics.quest_completion_rate > 0.9:
            difficulty_score += 0.5
        elif metrics.quest_completion_rate < 0.5:
            difficulty_score -= 0.5
        
        # Determine new difficulty level
        current_index = list(DifficultyLevel).index(self.current_difficulty)
        
        if difficulty_score >= 2:
            # Make it harder
            new_index = min(current_index + 1, len(DifficultyLevel) - 1)
        elif difficulty_score <= -2:
            # Make it easier
            new_index = max(current_index - 1, 0)
        else:
            # No change needed
            return False, self.current_difficulty
        
        new_difficulty = list(DifficultyLevel)[new_index]
        
        # Only adjust if it's actually different
        if new_difficulty != self.current_difficulty:
            return True, new_difficulty
        
        return False, self.current_difficulty
    
    def adjust_difficulty(self, new_difficulty: DifficultyLevel) -> str:
        """Adjust the difficulty level"""
        old_difficulty = self.current_difficulty
        self.current_difficulty = new_difficulty
        
        # Clear some performance history to allow for adaptation
        if len(self.combat_results) > 10:
            self.combat_results = self.combat_results[-10:]
        
        # Generate adjustment message
        if new_difficulty.value > old_difficulty.value:
            return f"🔥 Difficulty increased to {new_difficulty.value.replace('_', ' ').title()}! You're doing great!"
        else:
            return f"🛡️ Difficulty decreased to {new_difficulty.value.replace('_', ' ').title()}. Take your time to learn!"
    
    def get_difficulty_modifiers(self) -> Dict[str, float]:
        """Get current difficulty modifiers"""
        return self.difficulty_modifiers[self.current_difficulty].copy()
    
    def apply_combat_modifiers(self, enemy_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Apply difficulty modifiers to enemy stats"""
        modifiers = self.get_difficulty_modifiers()
        modified_stats = enemy_stats.copy()
        
        # Apply health modifier
        if "hp" in modified_stats:
            modified_stats["hp"] = int(modified_stats["hp"] * modifiers["enemy_health"])
            modified_stats["max_hp"] = modified_stats["hp"]
        
        # Apply damage modifier
        if "attack" in modified_stats:
            modified_stats["attack"] = int(modified_stats["attack"] * modifiers["enemy_damage"])
        
        return modified_stats
    
    def apply_reward_modifiers(self, base_exp: int, base_loot_chance: float) -> Tuple[int, float]:
        """Apply difficulty modifiers to rewards"""
        modifiers = self.get_difficulty_modifiers()
        
        modified_exp = int(base_exp * modifiers["exp_multiplier"])
        modified_loot_chance = base_loot_chance * modifiers["loot_chance"]
        
        return modified_exp, modified_loot_chance
    
    def apply_skill_modifiers(self, base_mp_cost: int) -> int:
        """Apply difficulty modifiers to skill MP costs"""
        modifiers = self.get_difficulty_modifiers()
        return int(base_mp_cost * modifiers["skill_mp_cost"])
    
    def get_performance_summary(self) -> str:
        """Get a summary of current performance"""
        if self.actions_taken < 10:
            return "Not enough data to analyze performance yet."
        
        metrics = self.calculate_performance_metrics()
        
        summary = f"""
Performance Summary:
• Combat Win Rate: {metrics.combat_win_rate:.1%}
• Deaths: {metrics.death_count}
• Skill Usage: {metrics.skill_usage_frequency:.1%} of actions
• Exploration: {metrics.exploration_thoroughness:.1%} complete
• Quest Success: {metrics.quest_completion_rate:.1%}
• Current Difficulty: {self.current_difficulty.value.replace('_', ' ').title()}
        """.strip()
        
        return summary
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for saving"""
        return {
            "current_difficulty": self.current_difficulty.value,
            "combat_results": self.combat_results,
            "deaths": self.deaths,
            "skills_used": self.skills_used,
            "actions_taken": self.actions_taken,
            "locations_discovered": list(self.locations_discovered),
            "quests_completed": self.quests_completed,
            "quests_attempted": self.quests_attempted
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DifficultyAdjustmentSystem':
        """Create from dictionary"""
        system = cls()
        system.current_difficulty = DifficultyLevel(data.get("current_difficulty", "normal"))
        system.combat_results = data.get("combat_results", [])
        system.deaths = data.get("deaths", 0)
        system.skills_used = data.get("skills_used", 0)
        system.actions_taken = data.get("actions_taken", 0)
        system.locations_discovered = set(data.get("locations_discovered", []))
        system.quests_completed = data.get("quests_completed", 0)
        system.quests_attempted = data.get("quests_attempted", 0)
        return system
