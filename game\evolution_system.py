"""
Evolution system for Me? Reincarnated?
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class EvolutionTriggerType(Enum):
    LEVEL = "level"
    SKILL_COUNT = "skill_count"
    COMBAT_VICTORIES = "combat_victories"
    ABILITY_MASTERY = "ability_mastery"
    RELATIONSHIP = "relationship"
    QUEST_COMPLETION = "quest_completion"
    ITEM_POSSESSION = "item_possession"
    LOCATION_DISCOVERY = "location_discovery"

@dataclass
class EvolutionRequirement:
    trigger_type: EvolutionTriggerType
    target: str
    value: int
    description: str

@dataclass
class EvolutionPath:
    name: str
    description: str
    from_creature: str
    to_creature: str
    requirements: List[EvolutionRequirement]
    stat_multipliers: Dict[str, float]
    new_abilities: List[str]
    special_features: List[str]
    evolution_story: str

class EvolutionSystem:
    """Manages creature evolution paths and triggers"""

    def __init__(self):
        self.evolution_paths = self._initialize_evolution_paths()
        self.evolution_history = []

    def _initialize_evolution_paths(self) -> Dict[str, List[EvolutionPath]]:
        """Initialize all evolution paths for each creature type"""
        paths = {}

        # Slime Evolution Paths
        paths["Slime"] = [
            EvolutionPath(
                name="Elemental Slime",
                description="A slime that has mastered elemental resistances",
                from_creature="Slime",
                to_creature="Elemental Slime",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 10, "Reach level 10"),
                    EvolutionRequirement(EvolutionTriggerType.SKILL_COUNT, "resistance_skills", 3, "Learn 3 elemental resistance skills"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Temperature Regulation", 1, "Master Temperature Regulation fusion")
                ],
                stat_multipliers={"hp": 1.5, "mp": 2.0, "defense": 1.8, "attack": 1.2, "speed": 1.1},
                new_abilities=["Elemental Burst", "Adaptive Form", "Elemental Immunity"],
                special_features=["Immunity to all elemental damage", "Can change elemental affinity"],
                evolution_story="Your mastery over the elements transforms your gelatinous form into a shimmering, ever-changing mass of elemental energy."
            ),
            EvolutionPath(
                name="King Slime",
                description="A massive slime with absorbed powers from many creatures",
                from_creature="Slime",
                to_creature="King Slime",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 12, "Reach level 12"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Absorb", 20, "Use Absorb ability 20 times successfully"),
                    EvolutionRequirement(EvolutionTriggerType.COMBAT_VICTORIES, "unique_enemies", 15, "Defeat 15 different enemy types"),
                    EvolutionRequirement(EvolutionTriggerType.RELATIONSHIP, "leadership_experience", 1, "Lead or command others successfully")
                ],
                stat_multipliers={"hp": 2.5, "mp": 1.5, "attack": 1.8, "defense": 2.0, "speed": 0.8},
                new_abilities=["Royal Command", "Mass Absorption", "Slime Army"],
                special_features=["Can summon lesser slimes", "Massive size increase", "Leadership aura"],
                evolution_story="Your countless absorbed abilities coalesce into royal power, transforming you into a massive, crown-bearing slime monarch."
            ),
            EvolutionPath(
                name="Mimic Slime",
                description="A slime specialized in perfect mimicry and stealth",
                from_creature="Slime",
                to_creature="Mimic Slime",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 8, "Reach level 8"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Mimic", 15, "Successfully mimic 15 different creatures"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Stealth", 1, "Master stealth abilities"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Perfect Copy", 1, "Achieve Perfect Copy fusion")
                ],
                stat_multipliers={"hp": 1.3, "mp": 1.8, "attack": 1.5, "defense": 1.2, "speed": 2.0},
                new_abilities=["Perfect Mimicry", "Shape Memory", "Identity Theft"],
                special_features=["Can perfectly copy any creature's appearance", "Retains copied abilities temporarily"],
                evolution_story="Your mimicry becomes so perfect that reality itself struggles to distinguish between you and your copies."
            )
        ]

        # Spider Evolution Paths
        paths["Spider"] = [
            EvolutionPath(
                name="Arachne",
                description="A spider that has gained humanoid intelligence and form",
                from_creature="Spider",
                to_creature="Arachne",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 12, "Reach level 12"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "web_skills", 5, "Master 5 web-based abilities"),
                    EvolutionRequirement(EvolutionTriggerType.COMBAT_VICTORIES, "humanoid_enemies", 10, "Defeat 10 humanoid enemies"),
                    EvolutionRequirement(EvolutionTriggerType.RELATIONSHIP, "intelligent_beings", 3, "Form relationships with 3 intelligent beings")
                ],
                stat_multipliers={"hp": 1.8, "mp": 2.2, "attack": 1.6, "defense": 1.4, "speed": 1.5},
                new_abilities=["Web Mastery", "Silk Armor", "Arachnid Wisdom"],
                special_features=["Humanoid intelligence", "Can craft complex web structures", "Magic affinity"],
                evolution_story="Your spider form elongates and gains humanoid features, your mind expanding with newfound intelligence and magical potential."
            ),
            EvolutionPath(
                name="Widow Spider",
                description="A deadly spider specialized in poison and assassination",
                from_creature="Spider",
                to_creature="Widow Spider",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 10, "Reach level 10"),
                    EvolutionRequirement(EvolutionTriggerType.COMBAT_VICTORIES, "poison_kills", 25, "Defeat 25 enemies with poison"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Venomous Web Trap", 1, "Master Venomous Web Trap fusion"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "stealth_kills", 10, "Defeat 10 enemies using stealth")
                ],
                stat_multipliers={"hp": 1.4, "mp": 1.6, "attack": 2.2, "defense": 1.2, "speed": 1.8},
                new_abilities=["Lethal Venom", "Death Web", "Assassinate"],
                special_features=["Extremely potent poison", "Can kill with single bite", "Perfect stealth"],
                evolution_story="Your venom becomes lethally potent as your form darkens, gaining the distinctive markings of a deadly predator."
            ),
            EvolutionPath(
                name="Phase Spider",
                description="A spider that can manipulate dimensions and space",
                from_creature="Spider",
                to_creature="Phase Spider",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 14, "Reach level 14"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "dimensional_magic", 3, "Learn 3 dimensional magic abilities"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Dimensional Web", 1, "Master Dimensional Web fusion"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "spatial_puzzles", 5, "Solve 5 spatial/dimensional puzzles")
                ],
                stat_multipliers={"hp": 1.6, "mp": 2.5, "attack": 1.4, "defense": 1.3, "speed": 2.2},
                new_abilities=["Phase Shift", "Dimensional Anchor", "Reality Web"],
                special_features=["Can phase between dimensions", "Immune to physical attacks while phased", "Can trap enemies in pocket dimensions"],
                evolution_story="Your connection to dimensional magic transforms your very essence, allowing you to exist partially outside normal reality."
            )
        ]

        # Wisp Evolution Paths
        paths["Wisp"] = [
            EvolutionPath(
                name="Elemental Wisp",
                description="A wisp that has mastered elemental magic",
                from_creature="Wisp",
                to_creature="Elemental Wisp",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 8, "Reach level 8"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Light Manipulation", 1, "Master light manipulation"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Radiant Burst", 1, "Learn Radiant Burst fusion")
                ],
                stat_multipliers={"hp": 1.2, "mp": 1.5, "attack": 1.3, "defense": 1.1, "speed": 1.2},
                new_abilities=["Elemental Magic", "Energy Shield"],
                special_features=["Elemental immunity", "Enhanced magical damage"],
                evolution_story="Your essence resonates with the elemental forces of the world. Light bends to your will as you become a conduit for pure magical energy."
            ),
            EvolutionPath(
                name="Guardian Spirit",
                description="A protective spirit that watches over others",
                from_creature="Wisp",
                to_creature="Guardian Spirit",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 10, "Reach level 10"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Magic Sense", 1, "Develop magical awareness"),
                    EvolutionRequirement(EvolutionTriggerType.RELATIONSHIP, "npc_alliances", 3, "Form bonds with 3 allies")
                ],
                stat_multipliers={"hp": 1.4, "mp": 1.3, "attack": 1.0, "defense": 1.5, "speed": 1.1},
                new_abilities=["Protective Aura", "Healing Light"],
                special_features=["Ally protection", "Healing abilities"],
                evolution_story="Your desire to protect others transforms you into a guardian spirit. Your light now serves as a beacon of hope and protection for those in need."
            )
        ]

        # Rat Evolution Paths
        paths["Rat"] = [
            EvolutionPath(
                name="Dire Rat",
                description="A larger, more aggressive rat with enhanced physical abilities",
                from_creature="Rat",
                to_creature="Dire Rat",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 6, "Reach level 6"),
                    EvolutionRequirement(EvolutionTriggerType.COMBAT_VICTORIES, "battle_victories", 10, "Win 10 combat encounters"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Keen Senses", 1, "Sharpen your senses")
                ],
                stat_multipliers={"hp": 1.5, "mp": 1.1, "attack": 1.4, "defense": 1.2, "speed": 1.3},
                new_abilities=["Intimidate", "Pack Leader"],
                special_features=["Enhanced size", "Leadership abilities"],
                evolution_story="Survival has made you stronger and fiercer. You've grown larger and more intimidating, becoming a natural leader among your kind."
            ),
            EvolutionPath(
                name="Shadow Rat",
                description="A rat that has learned to manipulate shadows and stealth",
                from_creature="Rat",
                to_creature="Shadow Rat",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 8, "Reach level 8"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Stealth", 1, "Master stealth techniques"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Rat's Shadow Step", 1, "Learn shadow manipulation")
                ],
                stat_multipliers={"hp": 1.2, "mp": 1.3, "attack": 1.3, "defense": 1.0, "speed": 1.5},
                new_abilities=["Shadow Blend", "Sneak Attack"],
                special_features=["Shadow manipulation", "Enhanced stealth"],
                evolution_story="You've learned to become one with the shadows. Darkness is now your ally, concealing your movements and enhancing your abilities."
            )
        ]

        # Mushroom Evolution Paths
        paths["Mushroom"] = [
            EvolutionPath(
                name="Mycelium Network",
                description="A mushroom connected to a vast underground network",
                from_creature="Mushroom",
                to_creature="Mycelium Network",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 10, "Reach level 10"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Natural Healing", 1, "Master natural healing"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "location_visits", 5, "Visit 5 different locations")
                ],
                stat_multipliers={"hp": 1.3, "mp": 1.4, "attack": 1.0, "defense": 1.3, "speed": 0.9},
                new_abilities=["Network Communication", "Remote Sensing"],
                special_features=["Information network", "Area awareness"],
                evolution_story="Your roots have connected with the ancient mycelium network that spans the entire world. You now have access to knowledge and communication across vast distances."
            ),
            EvolutionPath(
                name="Toxic Mushroom",
                description="A mushroom specialized in deadly toxins and poisons",
                from_creature="Mushroom",
                to_creature="Toxic Mushroom",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 8, "Reach level 8"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Spore Release", 1, "Master spore production"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Toxic Cloud", 1, "Learn toxic spore fusion")
                ],
                stat_multipliers={"hp": 1.2, "mp": 1.3, "attack": 1.4, "defense": 1.2, "speed": 1.0},
                new_abilities=["Deadly Spores", "Poison Immunity"],
                special_features=["Toxic attacks", "Poison resistance"],
                evolution_story="You've specialized in creating deadly toxins. Your spores now carry potent poisons that can fell even the mightiest enemies."
            )
        ]

        # Goblin Evolution Paths
        paths["Goblin"] = [
            EvolutionPath(
                name="Hobgoblin",
                description="A larger, stronger goblin with enhanced combat prowess",
                from_creature="Goblin",
                to_creature="Hobgoblin",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 15, "Reach level 15"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "combat_skills", 5, "Master 5 combat skills"),
                    EvolutionRequirement(EvolutionTriggerType.COMBAT_VICTORIES, "battle_victories", 30, "Win 30 combat encounters"),
                    EvolutionRequirement(EvolutionTriggerType.RELATIONSHIP, "battle_leadership", 3, "Lead others in 3 successful battles")
                ],
                stat_multipliers={"hp": 2.0, "mp": 1.3, "attack": 2.2, "defense": 1.8, "speed": 1.4},
                new_abilities=["Battle Fury", "Weapon Master", "Intimidate"],
                special_features=["Increased size and strength", "Natural weapon proficiency", "Battle leadership"],
                evolution_story="Your body grows larger and more muscular, your combat instincts sharpening into those of a true warrior."
            ),
            EvolutionPath(
                name="Goblin Shaman",
                description="A goblin who has mastered the mystical arts",
                from_creature="Goblin",
                to_creature="Goblin Shaman",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 12, "Reach level 12"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "magic_schools", 5, "Master 5 different magic schools"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "spirit_communion", 3, "Successfully commune with 3 spirits"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "wisdom_trials", 1, "Complete wisdom trials")
                ],
                stat_multipliers={"hp": 1.4, "mp": 2.5, "attack": 1.2, "defense": 1.3, "speed": 1.2},
                new_abilities=["Spirit Communion", "Elemental Mastery", "Ritual Magic"],
                special_features=["Can communicate with spirits", "Enhanced magical power", "Ritual casting"],
                evolution_story="Your connection to the spiritual realm deepens, marking your skin with mystical tattoos and filling your eyes with otherworldly wisdom."
            ),
            EvolutionPath(
                name="Goblin King",
                description="A goblin ruler with unmatched leadership and political power",
                from_creature="Goblin",
                to_creature="Goblin King",
                requirements=[
                    EvolutionRequirement(EvolutionTriggerType.LEVEL, "character_level", 18, "Reach level 18"),
                    EvolutionRequirement(EvolutionTriggerType.RELATIONSHIP, "npc_alliances", 10, "Build alliances with 10 NPCs"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "territory_control", 1, "Control significant territory"),
                    EvolutionRequirement(EvolutionTriggerType.ABILITY_MASTERY, "Tactical Genius", 1, "Master Tactical Genius fusion"),
                    EvolutionRequirement(EvolutionTriggerType.QUEST_COMPLETION, "leadership_trials", 3, "Complete 3 leadership trials")
                ],
                stat_multipliers={"hp": 1.8, "mp": 1.8, "attack": 1.6, "defense": 1.6, "speed": 1.3},
                new_abilities=["Royal Command", "Political Mastery", "Kingdom Management"],
                special_features=["Can command large groups", "Political influence", "Territory management"],
                evolution_story="A crown materializes upon your head as your presence becomes truly regal, commanding respect and loyalty from all who see you."
            )
        ]

        return paths

    def check_evolution_eligibility(self, character, memory_system) -> List[Tuple[str, List[str]]]:
        """Check which evolutions the character is eligible for"""
        creature_type = character.creature_type
        if creature_type not in self.evolution_paths:
            return []

        eligible_evolutions = []

        for evolution in self.evolution_paths[creature_type]:
            missing_requirements = []

            for requirement in evolution.requirements:
                if not self._check_requirement(character, memory_system, requirement):
                    missing_requirements.append(requirement.description)

            if not missing_requirements:
                eligible_evolutions.append((evolution.name, []))
            else:
                # Include partially eligible evolutions with missing requirements
                eligible_evolutions.append((evolution.name, missing_requirements))

        return eligible_evolutions

    def _check_requirement(self, character, memory_system, requirement: EvolutionRequirement) -> bool:
        """Check if a specific evolution requirement is met"""
        if requirement.trigger_type == EvolutionTriggerType.LEVEL:
            return character.level >= requirement.value

        elif requirement.trigger_type == EvolutionTriggerType.SKILL_COUNT:
            if requirement.target == "resistance_skills":
                resistance_skills = [skill for skill in character.abilities if "Resistance" in skill]
                return len(resistance_skills) >= requirement.value
            elif requirement.target == "web_skills":
                web_skills = [skill for skill in character.abilities if "Web" in skill]
                return len(web_skills) >= requirement.value
            elif requirement.target == "combat_skills":
                # This would need to be tracked in character progression
                return len(character.abilities) >= requirement.value  # Simplified

        elif requirement.trigger_type == EvolutionTriggerType.ABILITY_MASTERY:
            if requirement.target in character.abilities:
                return True
            # Check for fusion abilities
            return requirement.target in character.abilities

        elif requirement.trigger_type == EvolutionTriggerType.COMBAT_VICTORIES:
            # This would need to be tracked in memory system
            completed_quests = memory_system.long_term_memory.get("completed_quests", [])
            return len(completed_quests) >= requirement.value  # Simplified

        elif requirement.trigger_type == EvolutionTriggerType.RELATIONSHIP:
            npcs = memory_system.long_term_memory.get("met_npcs", {})
            positive_relationships = [npc for npc, data in npcs.items() if data.get("relationship", 0) > 20]
            return len(positive_relationships) >= requirement.value

        elif requirement.trigger_type == EvolutionTriggerType.QUEST_COMPLETION:
            completed_quests = memory_system.long_term_memory.get("completed_quests", [])
            return len(completed_quests) >= requirement.value  # Simplified

        return False

    def perform_evolution(self, character, evolution_name: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Perform character evolution"""
        creature_type = character.creature_type
        if creature_type not in self.evolution_paths:
            return False, "No evolution paths available for this creature type", {}

        # Find the evolution path
        evolution_path = None
        for evolution in self.evolution_paths[creature_type]:
            if evolution.name == evolution_name:
                evolution_path = evolution
                break

        if not evolution_path:
            return False, f"Evolution path '{evolution_name}' not found", {}

        # Apply evolution changes
        old_stats = character.stats.copy()

        # Apply stat multipliers
        for stat, multiplier in evolution_path.stat_multipliers.items():
            if stat in character.stats:
                character.stats[stat] = int(character.stats[stat] * multiplier)

        # Add new abilities
        for ability in evolution_path.new_abilities:
            if ability not in character.abilities:
                character.abilities.append(ability)

        # Update creature type
        character.creature_type = evolution_path.to_creature
        character.evolution_stage += 1

        # Record evolution in history
        evolution_record = {
            "from": evolution_path.from_creature,
            "to": evolution_path.to_creature,
            "level": character.level,
            "story": evolution_path.evolution_story
        }
        self.evolution_history.append(evolution_record)

        return True, evolution_path.evolution_story, {
            "old_stats": old_stats,
            "new_stats": character.stats.copy(),
            "new_abilities": evolution_path.new_abilities,
            "special_features": evolution_path.special_features
        }

    def get_evolution_info(self, creature_type: str, evolution_name: str) -> Optional[EvolutionPath]:
        """Get detailed information about a specific evolution"""
        if creature_type not in self.evolution_paths:
            return None

        for evolution in self.evolution_paths[creature_type]:
            if evolution.name == evolution_name:
                return evolution

        return None

    def get_all_evolution_paths(self, creature_type: str) -> List[EvolutionPath]:
        """Get all evolution paths for a creature type"""
        return self.evolution_paths.get(creature_type, [])
