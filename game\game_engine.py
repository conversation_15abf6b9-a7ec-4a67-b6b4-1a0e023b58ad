"""
Main game engine for Me? Reincarnated?
"""
import async<PERSON>
from typing import Dict, Any, Optional, List
from enum import Enum

from .character import Character
from .memory_system import MemorySystem
from .save_system import SaveSystem
from .skill_system import SkillFusionSystem
from .skill_learning_system import SkillLearningSystem
from .combat_system import CombatSystem
from .evolution_system import EvolutionSystem
from .location_system import LocationSystem
from .item_system import ItemSystem
from .difficulty_system import DifficultyAdjustmentSystem
from .lore_system import LoreSystem
from .portrait_system import PortraitSystem
from .reward_skill_system import RewardSkillSystem
from .action_selection_system import ActionSelectionSystem
from .npc_system import NPCSystem
from .dialogue_system import DialogueSystem
from .quest_system import QuestSystem
from api.gemini_client import GeminiClient
import config

class GameState(Enum):
    MENU = "menu"
    CHARACTER_CREATION = "character_creation"
    PLAYING = "playing"
    COMBAT = "combat"
    PAUSED = "paused"

class GameEngine:
    def __init__(self):
        """Initialize the game engine"""
        self.state = GameState.MENU
        self.character = Character()
        self.memory = MemorySystem()
        self.save_system = SaveSystem()
        self.gemini_client = GeminiClient()

        # New game systems
        self.skill_system = SkillFusionSystem()
        self.skill_learning_system = SkillLearningSystem()
        self.combat_system = CombatSystem()
        self.evolution_system = EvolutionSystem()
        self.location_system = LocationSystem()
        self.item_system = ItemSystem()
        self.difficulty_system = DifficultyAdjustmentSystem()
        self.lore_system = LoreSystem()
        self.portrait_system = PortraitSystem()
        self.reward_system = RewardSkillSystem()
        self.action_system = ActionSelectionSystem()
        self.npc_system = NPCSystem()
        self.dialogue_system = DialogueSystem()
        self.quest_system = QuestSystem()

        # Character creation state
        self.creation_stage = "name"  # name -> traits -> occupation -> creature -> backstory
        self.selected_backstory = None

        # Game world state
        self.current_location = "Mysterious Forest"
        self.world_state = {
            "day": 1,
            "time": "morning",
            "weather": "clear"
        }

        # Game session data
        self.playtime = 0
        self.last_auto_save = 0

        # Action selection state
        self.current_action_options = []
        self.last_response_with_actions = ""

    async def start_new_game(self):
        """Start a new game"""
        self.state = GameState.CHARACTER_CREATION
        self.creation_stage = "name"
        self.character = Character()
        self.memory = MemorySystem()
        self.selected_backstory = None

        # Initialize starting location
        self.memory.update_current_context(location=self.current_location)

        # Start with name selection
        return """Welcome to a new beginning...

You find yourself in a space between worlds, a realm of infinite possibility. A gentle voice speaks to you:

"Before we explore how your previous life ended, let us start with who you wish to become. In this new world, you will need a name - something that will define your identity as you grow and evolve."

What would you like to be called in this new world?"""

    async def process_character_creation(self, user_input: str) -> str:
        """Process character creation input"""
        if self.creation_stage == "name":
            self.character.name = user_input.strip()
            self.creation_stage = "traits"

            # Generate response for name selection
            response = await self.gemini_client.generate_character_creation_response(
                user_input, "name"
            )

            return response + """\n\nNow, let's explore your personality. What traits define who you are? These characteristics will shape how you interact with this new world and influence your growth.

Please describe your personality traits (e.g., 'curious, brave, analytical'):"""

        elif self.creation_stage == "traits":
            traits = [trait.strip() for trait in user_input.split(',')]
            self.character.traits = traits
            self.creation_stage = "occupation"

            # Generate response for traits selection
            response = await self.gemini_client.generate_character_creation_response(
                user_input, "traits"
            )

            return response + """\n\nExcellent! Your personality is taking shape. Now, let's think about your past life. What was your occupation before you came to this world? Your previous profession may influence how you approach challenges in this new realm.

What was your occupation in your previous life?"""

        elif self.creation_stage == "occupation":
            self.character.occupation = user_input.strip()
            self.creation_stage = "creature"

            # Generate response for occupation selection
            response = await self.gemini_client.generate_character_creation_response(
                user_input, "occupation"
            )

            # Show creature selection without backstory recommendations yet
            creature_options = []
            for i, creature in enumerate(config.STARTING_CREATURES):
                stats = creature['base_stats']
                abilities = ", ".join(creature['abilities'])
                evolutions = ", ".join(creature['evolution_paths'])

                creature_info = f"""
{i+1}. {creature['name']} - {creature['description']}
   Stats: HP:{stats['hp']} MP:{stats['mp']} ATK:{stats['attack']} DEF:{stats['defense']} SPD:{stats['speed']}
   Starting Abilities: {abilities}
   Evolution Paths: {evolutions}"""
                creature_options.append(creature_info)

            creature_display = "\n".join(creature_options)

            return response + f"""\n\nNow comes a crucial choice - your new form in this world. Each creature offers unique abilities and growth potential:

{creature_display}

Enter the number (1-{len(config.STARTING_CREATURES)}) of your chosen form:"""

        elif self.creation_stage == "creature":
            # Find matching creature - handle both numeric and name input
            chosen_creature = None
            user_choice = user_input.strip()

            # Try numeric input first
            try:
                choice_num = int(user_choice) - 1
                if 0 <= choice_num < len(config.STARTING_CREATURES):
                    chosen_creature = config.STARTING_CREATURES[choice_num]["name"]
            except ValueError:
                # Fall back to name matching
                user_choice_lower = user_choice.lower()
                for creature in config.STARTING_CREATURES:
                    if creature["name"].lower() in user_choice_lower:
                        chosen_creature = creature["name"]
                        break

            if chosen_creature:
                self.character.set_creature_type(chosen_creature)
                self.creation_stage = "backstory"

                # Now present backstory selection based on character choices
                return await self._present_backstory_selection()
            else:
                creature_names = [creature["name"] for creature in config.STARTING_CREATURES]
                return f"I didn't understand your choice. Please enter a number (1-{len(config.STARTING_CREATURES)}) or choose from: {', '.join(creature_names)}."

        elif self.creation_stage == "backstory":
            # Handle backstory selection
            user_choice = user_input.strip().lower()

            if user_choice == "random":
                self.selected_backstory = self.lore_system.get_random_backstory()
            else:
                try:
                    choice_num = int(user_choice) - 1
                    if 0 <= choice_num < len(self.lore_system.backstory_scenarios):
                        self.selected_backstory = self.lore_system.backstory_scenarios[choice_num]
                    else:
                        return f"Please enter a number between 1 and {len(self.lore_system.backstory_scenarios)}, or 'random'."
                except ValueError:
                    return f"Please enter a number between 1 and {len(self.lore_system.backstory_scenarios)}, or 'random'."

            if self.selected_backstory:
                return await self._complete_character_creation()
            else:
                return "Something went wrong with backstory selection."

        return "Something went wrong with character creation."

    async def _present_backstory_selection(self) -> str:
        """Present backstory selection based on character choices made so far"""
        # Analyze character choices to recommend backstories
        recommended_backstories = self._get_recommended_backstories()

        # Present backstory options with recommendations
        backstory_options = []
        for i, scenario in enumerate(self.lore_system.backstory_scenarios):
            recommendation_mark = " ⭐ RECOMMENDED" if scenario.name in recommended_backstories else ""
            backstory_options.append(f"{i+1}. {scenario.name}{recommendation_mark}: {scenario.description}")

        backstory_display = "\n".join(backstory_options)

        recommendation_note = ""
        if recommended_backstories:
            recommendation_note = f"\n\n⭐ Based on your personality ({', '.join(self.character.traits)}), occupation ({self.character.occupation}), and chosen form ({self.character.creature_type}), these backstories would create a particularly compelling origin story."

        return f"""Perfect! Now that we know who you are and what form you'll take, let's explore how your previous life ended. Your backstory will shape the final details of your character and provide context for your journey.

How did your previous life come to an end?

{backstory_display}{recommendation_note}

Choose your backstory by entering the number (1-{len(self.lore_system.backstory_scenarios)}) or type 'random' for a surprise:"""

    def _get_recommended_backstories(self) -> List[str]:
        """Get recommended backstories based on character choices"""
        recommendations = []

        # Analyze traits to recommend backstories
        trait_keywords = [trait.lower() for trait in self.character.traits]

        for scenario in self.lore_system.backstory_scenarios:
            # Check if character traits match backstory personality keywords
            matching_keywords = set(trait_keywords).intersection(
                set(keyword.lower() for keyword in scenario.personality_keywords)
            )

            # Check if creature type is recommended for this backstory
            creature_match = self.character.creature_type in scenario.recommended_creatures

            # Check if occupation has synergy
            occupation_synergy = self.lore_system.validate_occupation_synergy(scenario, self.character.occupation)
            occupation_match = occupation_synergy['is_perfect_match'] or occupation_synergy['is_partial_match']

            # Recommend if there are multiple matches
            score = len(matching_keywords) + (2 if creature_match else 0) + (1 if occupation_match else 0)
            if score >= 2:  # Require at least 2 points of synergy
                recommendations.append(scenario.name)

        return recommendations[:3]  # Return top 3 recommendations

    async def _complete_character_creation(self) -> str:
        """Complete character creation with backstory integration"""
        # Store backstory information
        self.character.backstory_name = self.selected_backstory.name

        # Validate synergies now that we have all information
        trait_synergy = self.lore_system.validate_trait_synergy(self.selected_backstory, self.character.traits)
        occupation_synergy = self.lore_system.validate_occupation_synergy(self.selected_backstory, self.character.occupation)

        # Store synergy bonuses
        self.character.backstory_synergy_bonuses = {
            "trait_synergy": trait_synergy,
            "occupation_synergy": occupation_synergy
        }

        # Apply backstory stat bonuses
        backstory_bonuses = self.lore_system.get_stat_bonuses(self.selected_backstory)
        for stat, bonus in backstory_bonuses.items():
            if stat in self.character.stats:
                self.character.stats[stat] += bonus
                if stat in ["hp", "mp"]:
                    self.character.stats[f"max_{stat}"] += bonus

        # Store applied bonuses for reference
        self.character.backstory_synergy_bonuses["stat_bonuses"] = backstory_bonuses

        # Complete character creation
        self.state = GameState.PLAYING

        # Record important events
        self.memory.add_important_event(f"Reincarnated as a {self.character.creature_type}", "reincarnation")
        self.memory.discover_location(self.current_location, "A mysterious forest where you first awakened")

        # Generate character portrait
        if config.ENABLE_PORTRAIT_GENERATION:
            character_data = self.character.to_dict()
            self.portrait_system.preload_character_portraits(character_data)

        # Create backstory narrative
        backstory_narrative = f"""{self.selected_backstory.death_scene}

{self.selected_backstory.transition_scene}

{self.selected_backstory.reincarnation_explanation}"""

        # Show synergy feedback
        synergy_feedback = []
        if trait_synergy['narrative_bonus']:
            synergy_feedback.append(f"✨ Trait Synergy: {trait_synergy['bonus_description']}")
        if occupation_synergy['narrative_bonus']:
            synergy_feedback.append(f"✨ Occupation Synergy: {occupation_synergy['bonus_description']}")
        if backstory_bonuses:
            bonus_text = ", ".join([f"+{bonus} {stat.upper()}" for stat, bonus in backstory_bonuses.items()])
            synergy_feedback.append(f"✨ Backstory Bonuses: {bonus_text}")

        synergy_text = "\n".join(synergy_feedback) if synergy_feedback else ""

        # Generate opening scene
        opening_prompt = f"""
        Generate an opening scene for a reincarnated {self.character.creature_type} named {self.character.name}
        who just awakened in a mysterious forest. The character was formerly a {self.character.occupation}
        with traits: {', '.join(self.character.traits)}, and died from: {self.selected_backstory.name}.

        Describe their first moments of consciousness, what they see, feel, and their immediate surroundings.
        Reference their past life and death circumstances subtly. End with some options for what they can do first.
        """

        opening_scene = await self.gemini_client.generate_text(opening_prompt)
        self.memory.add_short_term_event(f"Awakened as {self.character.creature_type} in {self.current_location}")

        # Generate initial action options for the new character
        game_state_dict = self.get_game_state()
        initial_action_options = self.action_system.generate_action_options(
            game_state_dict, self.memory, self.location_system, self.character
        )
        self.current_action_options = initial_action_options

        result = f"""Your transformation is complete!

{backstory_narrative}

{synergy_text}

{opening_scene}"""

        # Add initial action options
        if initial_action_options:
            result += "\n\n" + self._format_action_options(initial_action_options)

        return result

    async def process_game_action(self, user_input: str) -> str:
        """Process player actions during gameplay with enhanced action selection"""
        if self.state != GameState.PLAYING:
            return "Game is not in playing state."

        # Check if player is in a dialogue first
        if "player" in self.dialogue_system.active_conversations:
            return await self.process_dialogue_choice(user_input)

        # Parse player input to determine if it's a selection or custom action
        selected_action, input_type = self.action_system.parse_player_input(
            user_input, self.current_action_options
        )

        # Check if this is an NPC conversation action
        if selected_action and selected_action.id.startswith("talk_to_"):
            npc_name = selected_action.name.replace("Talk to ", "")
            return await self.start_npc_conversation(npc_name)

        # Check if this is a quest journal action
        if selected_action and selected_action.id == "check_quest_journal":
            return self.get_active_quests_summary()

        # Check if user input is a direct "talk to" command
        if user_input.lower().startswith("talk to "):
            npc_name = user_input[8:].strip().title()  # Extract NPC name
            return await self.start_npc_conversation(npc_name)

        # Check if user input is a quest-related command
        if user_input.lower() in ["check quests", "quest journal", "quests", "journal"]:
            return self.get_active_quests_summary()

        # Record the action for anti-repetition tracking
        actual_action = selected_action.name if selected_action else user_input
        self.memory.record_player_action(actual_action, self.current_location)

        # Update cooldowns
        self.memory.update_cooldowns()

        # Add user action to memory
        self.memory.add_short_term_event(f"Player: {actual_action}")

        # Record action for difficulty tracking
        self.difficulty_system.record_action()

        # Set cooldown for selected action if applicable
        if selected_action and selected_action.cooldown > 0:
            self.memory.set_action_cooldown(selected_action.id, selected_action.cooldown)

        # Get current context for AI
        location_context = self.memory.get_location_context(self.current_location)

        # Build world context
        world_context = f"Location: {location_context}. Time: {self.world_state['time']} of day {self.world_state['day']}."

        # Check for contextual skill learning
        learned_skills = self.skill_learning_system.analyze_action_for_learning(
            actual_action, self.character, self.current_location, self.memory
        )

        # Apply learned skills
        skill_learning_messages = []
        for skill_name in learned_skills:
            self.character.learn_ability(skill_name)
            skill_learning_messages.append(f"🎓 You learned a new skill: {skill_name}!")
            self.memory.add_important_event(f"Learned skill: {skill_name}", "skill_learning")

        # Generate response using Gemini with enhanced context
        game_state_dict = self.get_game_state()

        # Add action selection context to the prompt
        action_context = ""
        if selected_action:
            action_context = f"The player selected: {selected_action.name} - {selected_action.description}"
        else:
            action_context = f"The player chose a custom action: {user_input}"

        response = await self.gemini_client.generate_game_response(
            actual_action, game_state_dict, list(self.memory.short_term_memory),
            world_context, action_context
        )

        # Record AI response pattern for variety tracking
        self.memory.record_ai_response_pattern(response)

        # Update quest progress based on action
        self.update_quest_progress(actual_action, "")

        # Add skill learning messages to response
        if skill_learning_messages:
            response += "\n\n" + "\n".join(skill_learning_messages)

        # Check for difficulty adjustment
        should_adjust, new_difficulty = self.difficulty_system.should_adjust_difficulty()
        if should_adjust:
            adjustment_message = self.difficulty_system.adjust_difficulty(new_difficulty)
            response += f"\n\n{adjustment_message}"

        # Generate new action options for next turn
        new_action_options = self.action_system.generate_action_options(
            game_state_dict, self.memory, self.location_system, self.character
        )
        self.current_action_options = new_action_options

        # Format action options for display
        if new_action_options:
            response += "\n\n" + self._format_action_options(new_action_options)

        # Add AI response to memory
        self.memory.add_short_term_event(f"Game: {response[:100]}...")

        # Store the response for potential UI enhancements
        self.last_response_with_actions = response

        # Check for auto-save
        self.playtime += 1
        if self.playtime - self.last_auto_save >= config.AUTO_SAVE_INTERVAL:
            self.auto_save()

        return response

    async def start_npc_conversation(self, npc_name: str) -> str:
        """Start a conversation with an NPC"""
        # Check if NPC exists and is available
        npc = self.npc_system.get_npc(npc_name)
        if not npc:
            return f"There is no one named {npc_name} here."

        # Check if NPC is in current location
        if npc.location != self.current_location:
            return f"{npc_name} is not in this location."

        # Check availability conditions
        if not self.npc_system.is_npc_available(npc_name, self.character, self.memory):
            return f"{npc_name} is not available for conversation right now."

        # Record meeting if first time
        met_npcs = self.memory.long_term_memory.get("met_npcs", {})
        if npc_name not in met_npcs:
            # Generate first impression
            first_impression = await self.gemini_client.generate_npc_dialogue(
                npc_name, f"{npc.personality.value}, {npc.role.value}", 0,
                f"First meeting with {self.character.name} in {self.current_location}"
            )
            self.memory.meet_npc(npc_name, first_impression[:100], 0)

        # Start dialogue
        npc_response, choices = self.dialogue_system.start_conversation(
            "player", npc_name, self.character, self.memory
        )

        # Format response
        response = f"**{npc_name}**: {npc_response}"
        if choices:
            response += "\n\n" + "\n".join(choices)
            response += "\n\nChoose a response (1, 2, 3...) or type 'end' to stop talking."

        return response

    async def process_dialogue_choice(self, choice_input: str) -> str:
        """Process a dialogue choice during NPC conversation"""
        # Check if there's an active conversation
        if "player" not in self.dialogue_system.active_conversations:
            return "You're not currently talking to anyone."

        # Handle ending conversation
        if choice_input.lower() in ["end", "quit", "stop", "bye", "goodbye"]:
            npc_name, _ = self.dialogue_system.active_conversations["player"]
            del self.dialogue_system.active_conversations["player"]
            return f"You end your conversation with {npc_name}."

        # Parse choice number
        try:
            choice_index = int(choice_input) - 1
        except ValueError:
            return "Please enter a number (1, 2, 3...) or 'end' to stop talking."

        # Process the choice
        npc_response, new_choices, consequences = self.dialogue_system.process_dialogue_choice(
            "player", choice_index, self.character, self.memory
        )

        # Apply consequences
        consequence_messages = []
        if "learn_skill" in consequences:
            skill_name = consequences["learn_skill"]
            self.character.learn_ability(skill_name)
            consequence_messages.append(f"🎓 You learned: {skill_name}!")

        if "gain_experience" in consequences:
            exp = consequences["gain_experience"]
            self.character.gain_experience(exp)
            consequence_messages.append(f"✨ You gained {exp} experience!")

        if "start_quest" in consequences:
            quest_id = consequences["start_quest"]
            quest = self.quest_system.get_quest(quest_id)
            if quest:
                if self.quest_system.start_quest(quest_id, self.character, self.memory):
                    consequence_messages.append(f"📜 New quest started: {quest.name}")
                else:
                    consequence_messages.append(f"📜 Quest offered: {quest.name} (requirements not met)")
            else:
                consequence_messages.append(f"📜 Quest error: Unknown quest {quest_id}")

        if "relationship_change" in consequences:
            change = consequences["relationship_change"]
            if change > 0:
                consequence_messages.append(f"💚 Your relationship improved!")
            elif change < 0:
                consequence_messages.append(f"💔 Your relationship worsened.")

        # Format response
        npc_name, _ = self.dialogue_system.active_conversations.get("player", ("Unknown", ""))
        response = f"**{npc_name}**: {npc_response}"

        if consequence_messages:
            response += "\n\n" + "\n".join(consequence_messages)

        if new_choices:
            response += "\n\n" + "\n".join(new_choices)
            response += "\n\nChoose a response (1, 2, 3...) or type 'end' to stop talking."

        return response

    def get_npcs_in_current_location(self) -> List[str]:
        """Get list of NPCs in the current location"""
        npcs = self.npc_system.get_npcs_in_location(self.current_location)
        available_npcs = []

        for npc in npcs:
            if self.npc_system.is_npc_available(npc.name, self.character, self.memory):
                available_npcs.append(npc.name)

        return available_npcs

    def get_available_quests(self) -> List[str]:
        """Get list of available quests in current location"""
        available_quests = self.quest_system.get_available_quests(
            self.character, self.memory, self.current_location
        )
        return [quest.name for quest in available_quests]

    def get_active_quests_summary(self) -> str:
        """Get formatted summary of active quests"""
        summaries = self.quest_system.get_active_quests_summary()
        if not summaries:
            return "No active quests."

        result = "📜 **Active Quests:**\n"
        for quest_name, progress in summaries.items():
            result += f"\n**{quest_name}**\n{progress}\n"

        return result

    def update_quest_progress(self, action: str, target: str = "", progress: int = 1):
        """Update quest progress based on player actions"""
        # Check all active quests for relevant objectives
        for quest_id, quest in self.quest_system.active_quests.items():
            objectives_to_check = quest.get_active_objectives()

            for objective in objectives_to_check:
                should_update = False

                # Check different objective types
                if objective.objective_type.value == "visit" and "visit" in action.lower():
                    if objective.target in target or objective.target in action:
                        should_update = True

                elif objective.objective_type.value == "collect" and "collect" in action.lower():
                    if objective.target in target or objective.target in action:
                        should_update = True

                elif objective.objective_type.value == "talk_to" and "talk" in action.lower():
                    if objective.target in target:
                        should_update = True

                elif objective.objective_type.value == "kill" and "defeat" in action.lower():
                    if objective.target in target:
                        should_update = True

                elif objective.objective_type.value == "learn_skill" and "learn" in action.lower():
                    if objective.target in target:
                        should_update = True

                elif objective.objective_type.value == "custom":
                    # Custom objectives need specific triggers
                    if objective.target in action.lower() or objective.target in target.lower():
                        should_update = True

                if should_update:
                    self.quest_system.update_quest_progress(quest_id, objective.id, progress)

                    # Check if quest is now complete
                    if self.quest_system.check_quest_completion(quest_id):
                        rewards = self.quest_system.complete_quest(quest_id, self.character, self.memory)
                        self._apply_quest_rewards(rewards)

    def _apply_quest_rewards(self, rewards) -> List[str]:
        """Apply quest rewards and return messages"""
        messages = []

        # Apply experience
        if rewards.experience > 0:
            self.character.gain_experience(rewards.experience)
            messages.append(f"✨ Gained {rewards.experience} experience!")

        # Apply skills
        for skill in rewards.skills:
            self.character.learn_ability(skill)
            messages.append(f"🎓 Learned skill: {skill}!")

        # Apply items
        for item in rewards.items:
            # Add to inventory when item system is integrated
            messages.append(f"🎁 Received item: {item}!")

        # Apply relationship changes
        for npc_name, change in rewards.relationship_changes.items():
            self.memory.update_npc_relationship(npc_name, "Quest completion reward", change)
            messages.append(f"💚 Relationship with {npc_name} improved!")

        # Apply world changes
        for change in rewards.world_changes:
            self.memory.record_world_change(change, "major")
            messages.append(f"🌍 {change}")

        return messages

    def _format_action_options(self, action_options) -> str:
        """Format action options for display to the player"""
        if not action_options:
            return ""

        formatted = "What would you like to do?\n"
        for i, action in enumerate(action_options, 1):
            risk_indicator = ""
            if action.risk_level == "medium":
                risk_indicator = " ⚠️"
            elif action.risk_level == "high":
                risk_indicator = " ⚠️⚠️"

            formatted += f"{i}. {action.name}{risk_indicator}\n"

        formatted += "\nYou can select an option by number (1, 2, 3...) or describe your own action."
        return formatted

    def get_game_state(self) -> Dict[str, Any]:
        """Get complete game state for saving"""
        return {
            "character": self.character.to_dict(),
            "memory": self.memory.to_dict(),
            "current_location": self.current_location,
            "world_state": self.world_state,
            "playtime": self.playtime,
            "creation_stage": self.creation_stage,
            "state": self.state.value,
            "reward_progress": self.reward_system.save_progress(),
            "current_action_options": [
                {
                    "id": action.id,
                    "name": action.name,
                    "description": action.description,
                    "category": action.category.value,
                    "requirements": action.requirements,
                    "hidden_info": action.hidden_info,
                    "cooldown": action.cooldown,
                    "risk_level": action.risk_level
                } for action in self.current_action_options
            ]
        }

    def load_game_state(self, game_state: Dict[str, Any]):
        """Load game state from save data"""
        try:
            # Load character
            if "character" in game_state:
                self.character = Character.from_dict(game_state["character"])

            # Load memory
            if "memory" in game_state:
                self.memory = MemorySystem.from_dict(game_state["memory"])

            # Load world state
            self.current_location = game_state.get("current_location", "Mysterious Forest")
            self.world_state = game_state.get("world_state", self.world_state)
            self.playtime = game_state.get("playtime", 0)
            self.creation_stage = game_state.get("creation_stage", "name")

            # Load game state
            state_value = game_state.get("state", "menu")
            self.state = GameState(state_value)

            # Load reward progress
            if "reward_progress" in game_state:
                self.reward_system.load_progress(game_state["reward_progress"])

            # Load action options (regenerate if not available)
            if "current_action_options" in game_state:
                from .action_selection_system import ActionOption, ActionCategory
                self.current_action_options = []
                for action_data in game_state["current_action_options"]:
                    action = ActionOption(
                        id=action_data["id"],
                        name=action_data["name"],
                        description=action_data["description"],
                        category=ActionCategory(action_data["category"]),
                        requirements=action_data["requirements"],
                        hidden_info=action_data.get("hidden_info", False),
                        cooldown=action_data.get("cooldown", 0),
                        risk_level=action_data.get("risk_level", "low")
                    )
                    self.current_action_options.append(action)
            else:
                # Regenerate action options if not saved
                if self.state == GameState.PLAYING:
                    game_state_dict = self.get_game_state()
                    self.current_action_options = self.action_system.generate_action_options(
                        game_state_dict, self.memory, self.location_system, self.character
                    )

            return True

        except Exception as e:
            print(f"Error loading game state: {e}")
            return False

    def save_game(self, save_name: Optional[str] = None) -> bool:
        """Save the current game"""
        game_state = self.get_game_state()
        return self.save_system.save_game(game_state, save_name)

    def auto_save(self) -> bool:
        """Perform auto-save"""
        game_state = self.get_game_state()
        success = self.save_system.auto_save(game_state)
        if success:
            self.last_auto_save = self.playtime
        return success

    def load_game(self, save_name: str) -> bool:
        """Load a saved game"""
        game_state = self.save_system.load_game(save_name)
        if game_state:
            return self.load_game_state(game_state)
        return False

    def get_character_status(self) -> str:
        """Get formatted character status"""
        if self.character.name:
            status = self.character.get_status_summary()

            # Add fusion hints
            fusion_hints = self.skill_system.get_fusion_hints(self.character.abilities)
            if fusion_hints:
                status += f"\n\nFusion Hints:\n" + "\n".join(f"• {hint}" for hint in fusion_hints[:3])

            # Add skill learning hints
            learning_hints = self.skill_learning_system.get_skill_learning_hints(self.character, self.current_location)
            if learning_hints:
                status += f"\n\nSkill Learning Progress:\n" + "\n".join(f"• {hint}" for hint in learning_hints[:3])

            # Add evolution progress
            eligible_evolutions = self.evolution_system.check_evolution_eligibility(self.character, self.memory)
            if eligible_evolutions:
                status += f"\n\nEvolution Status:\n"
                for evo_name, missing_reqs in eligible_evolutions:
                    if not missing_reqs:
                        status += f"• {evo_name}: READY TO EVOLVE!\n"
                    else:
                        status += f"• {evo_name}: Missing {len(missing_reqs)} requirements\n"

            return status
        return "No character created yet."

    async def process_combat_action(self, action: str, target: str = None) -> str:
        """Process combat actions"""
        if self.state != GameState.COMBAT:
            return "Not in combat!"

        result = self.combat_system.execute_character_action(action, target)

        if result["success"]:
            response = "\n".join(result["messages"])

            # Check if combat ended
            if result.get("combat_ended"):
                self.state = GameState.PLAYING
                if result.get("victory"):
                    # Handle victory tracking (experience already awarded in combat system)
                    if "rewards" in result:
                        rewards = result["rewards"]
                        # Record combat victory for evolution tracking (without double experience)
                        self.character.combat_victories += 1

                        # Update reward system progress
                        enemy_name = self.combat_system.active_combat.enemy.name if self.combat_system.active_combat else "Unknown Enemy"
                        newly_earned_rewards = self._update_reward_progress_from_combat(enemy_name)

                        # Check for combat-based skill learning
                        combat_learned_skills = self.skill_learning_system.record_action(
                            "combat_victory",
                            {
                                "character_level": self.character.level,
                                "creature_type": self.character.creature_type,
                                "current_abilities": self.character.abilities,
                                "combat_victories": self.character.combat_victories
                            }
                        )

                        # Apply combat learned skills
                        for skill_name in combat_learned_skills:
                            self.character.learn_ability(skill_name)
                            response += f"\n🎓 Combat experience taught you: {skill_name}!"
                            self.memory.add_important_event(f"Learned skill from combat: {skill_name}", "skill_learning")

                        # Apply newly earned reward skills
                        for reward_skill in newly_earned_rewards:
                            self.character.earn_reward_skill(reward_skill)
                            response += f"\n🏆 Achievement unlocked! You earned: {reward_skill}!"
                            self.memory.add_important_event(f"Earned reward skill: {reward_skill}", "reward_skill")

                        if "experience" in rewards:
                            # Track for difficulty system
                            self.difficulty_system.record_combat_result(True, 5)  # Assume 5 turn duration for now
                        if "loot" in rewards:
                            for item in rewards["loot"]:
                                self.character.inventory.append(item)

                    # Check for skill fusions after combat
                    await self.check_skill_fusions()

                    # Check for evolution eligibility
                    await self.check_evolution_eligibility()

            return response
        else:
            return result.get("message", "Combat action failed")

    async def check_skill_fusions(self) -> str:
        """Check and automatically perform skill fusions"""
        possible_fusions = self.skill_system.check_fusion_possibilities(self.character.abilities)
        fusion_messages = []

        for fusion_name in possible_fusions:
            success, message, fused_skill = self.skill_system.perform_fusion(
                self.character.abilities, fusion_name, self.character.creature_type
            )

            if success and fused_skill:
                # Remove component skills and add fused skill
                recipe = self.skill_system.fusion_recipes[fusion_name]
                for component in recipe["components"]:
                    if component in self.character.abilities:
                        self.character.abilities.remove(component)

                self.character.learn_ability(fused_skill.name)
                self.character.discover_fusion(fusion_name, recipe["components"])

                fusion_messages.append(f"🌟 SKILL FUSION! {message}")

                # Record in memory
                self.memory.add_important_event(f"Discovered skill fusion: {fusion_name}", "fusion")

                # Update reward progress from fusion
                fusion_rewards = self._update_reward_progress_from_fusion()
                for reward_skill in fusion_rewards:
                    self.character.earn_reward_skill(reward_skill)
                    fusion_messages.append(f"🏆 Fusion mastery unlocked: {reward_skill}!")

        return "\n".join(fusion_messages) if fusion_messages else ""

    def _update_reward_progress_from_combat(self, enemy_name: str) -> List[str]:
        """Update reward system progress from combat victory"""
        newly_earned = []

        # Update combat victories
        newly_earned.extend(self.reward_system.update_progress("combat_victories", self.character.combat_victories, "set"))

        # Update unique enemies defeated
        newly_earned.extend(self.reward_system.update_progress("unique_enemies_defeated", enemy_name, "add_to_set"))

        # Update character level
        newly_earned.extend(self.reward_system.update_progress("character_level", self.character.level, "set"))

        # Update total skills learned
        newly_earned.extend(self.reward_system.update_progress("total_skills_learned", len(self.character.abilities), "set"))

        return newly_earned

    def _update_reward_progress_from_evolution(self) -> List[str]:
        """Update reward system progress from evolution"""
        newly_earned = []

        # Update evolution count
        newly_earned.extend(self.reward_system.update_progress("evolution_count", self.character.evolution_stage, "set"))

        return newly_earned

    def _update_reward_progress_from_fusion(self) -> List[str]:
        """Update reward system progress from skill fusion"""
        newly_earned = []

        # Update skill fusions performed
        current_fusions = len(self.character.fusion_discoveries)
        newly_earned.extend(self.reward_system.update_progress("skill_fusions_performed", current_fusions, "set"))

        return newly_earned

    def _update_reward_progress_from_quest(self, quest_name: str) -> List[str]:
        """Update reward system progress from quest completion"""
        newly_earned = []

        # Update completed quests
        completed_count = len(self.memory.long_term_memory.get("completed_quests", []))
        newly_earned.extend(self.reward_system.update_progress("completed_quests", completed_count, "set"))

        return newly_earned

    def _update_reward_progress_from_location_discovery(self, location_name: str) -> List[str]:
        """Update reward system progress from discovering new location"""
        newly_earned = []

        # Update discovered locations
        newly_earned.extend(self.reward_system.update_progress("discovered_locations", location_name, "add_to_set"))

        return newly_earned

    def _update_reward_progress_from_relationship(self, npc_name: str, relationship_value: int) -> List[str]:
        """Update reward system progress from NPC relationship changes"""
        newly_earned = []

        # Update max NPC relationship
        newly_earned.extend(self.reward_system.update_progress("max_npc_relationship", relationship_value, "max"))

        # Count positive relationships
        npcs = self.memory.long_term_memory.get("met_npcs", {})
        positive_count = sum(1 for npc_data in npcs.values() if npc_data.get("relationship", 0) > 20)
        newly_earned.extend(self.reward_system.update_progress("positive_relationships", positive_count, "set"))

        return newly_earned

    async def check_evolution_eligibility(self) -> str:
        """Check if character can evolve"""
        eligible_evolutions = self.evolution_system.check_evolution_eligibility(self.character, self.memory)
        ready_evolutions = [name for name, missing in eligible_evolutions if not missing]

        if ready_evolutions:
            evolution_name = ready_evolutions[0]  # Take first available evolution
            success, story, details = self.evolution_system.perform_evolution(self.character, evolution_name)

            if success:
                self.memory.add_important_event(f"Evolved into {self.character.creature_type}", "evolution")

                # Generate portrait for evolved form
                if config.ENABLE_PORTRAIT_GENERATION:
                    character_data = self.character.to_dict()
                    self.portrait_system.queue_portrait_generation(character_data, "evolved", priority=1)

                return f"\n🎉 EVOLUTION! {story}\n\nNew abilities: {', '.join(details['new_abilities'])}"

        return ""

    async def start_combat(self, enemy_name: str) -> str:
        """Start a combat encounter"""
        result = self.combat_system.start_combat(self.character, enemy_name, self.current_location)

        if result["success"]:
            self.state = GameState.COMBAT
            return result["message"]
        else:
            return result["message"]

    def get_available_actions(self) -> List[Dict[str, Any]]:
        """Get available actions based on current game state"""
        if self.state == GameState.COMBAT:
            return self.combat_system.get_available_actions(self.character)
        elif self.state == GameState.PLAYING:
            # Regular gameplay actions
            actions = [
                {"action": "explore", "name": "Explore", "description": "Look around the current area"},
                {"action": "move", "name": "Move", "description": "Travel to a connected location"},
                {"action": "inventory", "name": "Inventory", "description": "Check your items and equipment"},
                {"action": "skills", "name": "Skills", "description": "View your abilities and fusion options"}
            ]

            # Add location-specific actions
            location = self.location_system.get_location(self.current_location)
            if location:
                encounters = self.location_system.get_available_encounters(
                    self.current_location, self.character.level, self.character.abilities
                )
                for encounter in encounters[:3]:  # Show up to 3 encounters
                    actions.append({
                        "action": "encounter",
                        "name": encounter.name,
                        "description": encounter.description,
                        "encounter_type": encounter.encounter_type.value
                    })

            return actions

        return []

    async def get_character_portrait(self):
        """Get the current character's portrait"""
        if self.character.name and config.ENABLE_PORTRAIT_GENERATION:
            character_data = self.character.to_dict()
            evolution_stage = "evolved" if hasattr(self.character, 'evolution_count') and self.character.evolution_count > 0 else "base"
            return await self.portrait_system.get_character_portrait(character_data, evolution_stage)
        return None

    def get_portrait_system_info(self) -> Dict[str, Any]:
        """Get information about the portrait system"""
        return self.portrait_system.get_cache_info()
