"""
Item and Inventory system for Me? Reincarnated?
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ItemType(Enum):
    WEAPON = "weapon"
    ARMOR = "armor"
    ACCESSORY = "accessory"
    CONSUMABLE = "consumable"
    MATERIAL = "material"
    QUEST = "quest"
    FUSION_CATALYST = "fusion_catalyst"

class ItemRarity(Enum):
    COMMON = "common"
    UNCOMMON = "uncommon"
    RARE = "rare"
    EPIC = "epic"
    LEGENDARY = "legendary"

@dataclass
class Item:
    name: str
    item_type: ItemType
    rarity: ItemRarity
    description: str
    effects: Dict[str, Any]
    requirements: Dict[str, Any]
    value: int
    weight: float
    stackable: bool = False
    max_stack: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "item_type": self.item_type.value,
            "rarity": self.rarity.value,
            "description": self.description,
            "effects": self.effects,
            "requirements": self.requirements,
            "value": self.value,
            "weight": self.weight,
            "stackable": self.stackable,
            "max_stack": self.max_stack
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Item':
        return cls(
            name=data["name"],
            item_type=ItemType(data["item_type"]),
            rarity=ItemRarity(data["rarity"]),
            description=data["description"],
            effects=data["effects"],
            requirements=data["requirements"],
            value=data["value"],
            weight=data["weight"],
            stackable=data.get("stackable", False),
            max_stack=data.get("max_stack", 1)
        )

@dataclass
class InventorySlot:
    item: Item
    quantity: int = 1

class ItemSystem:
    """Manages items, inventory, and equipment"""
    
    def __init__(self):
        self.item_database = self._initialize_item_database()
        self.equipment_slots = {
            "weapon": None,
            "armor": None,
            "accessory1": None,
            "accessory2": None
        }
    
    def _initialize_item_database(self) -> Dict[str, Item]:
        """Initialize the complete item database"""
        items = {}
        
        # Weapons (creature-specific)
        items["Sharp Claws"] = Item(
            name="Sharp Claws",
            item_type=ItemType.WEAPON,
            rarity=ItemRarity.COMMON,
            description="Natural claws sharpened to deadly points",
            effects={"attack": 3, "crit_chance": 0.1},
            requirements={"creature_type": ["Slime", "Spider", "Goblin"]},
            value=50,
            weight=0.1
        )
        
        items["Venomous Fangs"] = Item(
            name="Venomous Fangs",
            item_type=ItemType.WEAPON,
            rarity=ItemRarity.UNCOMMON,
            description="Fangs that inject poison with each bite",
            effects={"attack": 5, "poison_chance": 0.3},
            requirements={"creature_type": ["Spider"], "skill": "Poison Bite"},
            value=120,
            weight=0.2
        )
        
        items["Goblin Spear"] = Item(
            name="Goblin Spear",
            item_type=ItemType.WEAPON,
            rarity=ItemRarity.COMMON,
            description="A crude but effective spear made by goblins",
            effects={"attack": 8, "reach": True},
            requirements={"creature_type": ["Goblin"], "skill": "Tool Use"},
            value=80,
            weight=1.5
        )
        
        # Armor
        items["Hardened Carapace"] = Item(
            name="Hardened Carapace",
            item_type=ItemType.ARMOR,
            rarity=ItemRarity.UNCOMMON,
            description="A natural shell that provides excellent protection",
            effects={"defense": 6, "acid_resistance": 0.5},
            requirements={"creature_type": ["Slime", "Spider"]},
            value=150,
            weight=2.0
        )
        
        items["Leather Vest"] = Item(
            name="Leather Vest",
            item_type=ItemType.ARMOR,
            rarity=ItemRarity.COMMON,
            description="Simple leather protection for humanoid creatures",
            effects={"defense": 4, "mobility": True},
            requirements={"creature_type": ["Goblin"]},
            value=100,
            weight=1.0
        )
        
        # Accessories
        items["Crystal Pendant"] = Item(
            name="Crystal Pendant",
            item_type=ItemType.ACCESSORY,
            rarity=ItemRarity.RARE,
            description="A pendant that amplifies magical abilities",
            effects={"mp": 20, "magic_power": 1.2},
            requirements={},
            value=300,
            weight=0.1
        )
        
        items["Speed Ring"] = Item(
            name="Speed Ring",
            item_type=ItemType.ACCESSORY,
            rarity=ItemRarity.UNCOMMON,
            description="A ring that enhances movement speed",
            effects={"speed": 3, "dodge_chance": 0.1},
            requirements={},
            value=200,
            weight=0.05
        )
        
        # Consumables
        items["Health Potion"] = Item(
            name="Health Potion",
            item_type=ItemType.CONSUMABLE,
            rarity=ItemRarity.COMMON,
            description="Restores 50 HP when consumed",
            effects={"heal": 50},
            requirements={},
            value=25,
            weight=0.2,
            stackable=True,
            max_stack=10
        )
        
        items["Mana Potion"] = Item(
            name="Mana Potion",
            item_type=ItemType.CONSUMABLE,
            rarity=ItemRarity.COMMON,
            description="Restores 30 MP when consumed",
            effects={"mp_restore": 30},
            requirements={},
            value=30,
            weight=0.2,
            stackable=True,
            max_stack=10
        )
        
        items["Antidote"] = Item(
            name="Antidote",
            item_type=ItemType.CONSUMABLE,
            rarity=ItemRarity.UNCOMMON,
            description="Cures poison and provides temporary immunity",
            effects={"cure_poison": True, "poison_immunity": 300},
            requirements={},
            value=50,
            weight=0.1,
            stackable=True,
            max_stack=5
        )
        
        # Materials
        items["Wolf Fang"] = Item(
            name="Wolf Fang",
            item_type=ItemType.MATERIAL,
            rarity=ItemRarity.COMMON,
            description="A sharp fang from a forest wolf",
            effects={},
            requirements={},
            value=15,
            weight=0.1,
            stackable=True,
            max_stack=20
        )
        
        items["Magic Crystal"] = Item(
            name="Magic Crystal",
            item_type=ItemType.MATERIAL,
            rarity=ItemRarity.RARE,
            description="A crystal infused with magical energy",
            effects={},
            requirements={},
            value=100,
            weight=0.3,
            stackable=True,
            max_stack=5
        )
        
        items["Ancient Seed"] = Item(
            name="Ancient Seed",
            item_type=ItemType.MATERIAL,
            rarity=ItemRarity.LEGENDARY,
            description="A seed from the wisdom tree, pulsing with ancient power",
            effects={},
            requirements={},
            value=500,
            weight=0.05,
            stackable=False
        )
        
        # Fusion Catalysts
        items["Fusion Stone"] = Item(
            name="Fusion Stone",
            item_type=ItemType.FUSION_CATALYST,
            rarity=ItemRarity.RARE,
            description="A stone that enables certain skill fusions",
            effects={"enable_fusion": ["Temperature Regulation", "Steam Mastery"]},
            requirements={},
            value=250,
            weight=0.5,
            stackable=True,
            max_stack=3
        )
        
        items["Harmony Crystal"] = Item(
            name="Harmony Crystal",
            item_type=ItemType.FUSION_CATALYST,
            rarity=ItemRarity.EPIC,
            description="A crystal that harmonizes opposing forces",
            effects={"enable_fusion": ["Perfect Copy", "Dimensional Web", "Tactical Genius"]},
            requirements={},
            value=500,
            weight=0.3,
            stackable=False
        )
        
        # Quest Items
        items["Ancient Key"] = Item(
            name="Ancient Key",
            item_type=ItemType.QUEST,
            rarity=ItemRarity.UNCOMMON,
            description="An ornate key that opens ancient locks",
            effects={},
            requirements={},
            value=0,  # Quest items have no trade value
            weight=0.1,
            stackable=False
        )
        
        return items
    
    def get_item(self, item_name: str) -> Optional[Item]:
        """Get item by name from database"""
        return self.item_database.get(item_name)
    
    def create_inventory_slot(self, item_name: str, quantity: int = 1) -> Optional[InventorySlot]:
        """Create an inventory slot with the specified item"""
        item = self.get_item(item_name)
        if not item:
            return None
        
        # Check if quantity is valid for this item
        if not item.stackable and quantity > 1:
            quantity = 1
        elif item.stackable and quantity > item.max_stack:
            quantity = item.max_stack
        
        return InventorySlot(item=item, quantity=quantity)
    
    def can_equip_item(self, character, item: Item) -> Tuple[bool, str]:
        """Check if character can equip an item"""
        # Check creature type requirements
        if "creature_type" in item.requirements:
            allowed_types = item.requirements["creature_type"]
            if character.creature_type not in allowed_types:
                return False, f"This item can only be used by: {', '.join(allowed_types)}"
        
        # Check skill requirements
        if "skill" in item.requirements:
            required_skill = item.requirements["skill"]
            if required_skill not in character.abilities:
                return False, f"Requires skill: {required_skill}"
        
        # Check level requirements
        if "level" in item.requirements:
            required_level = item.requirements["level"]
            if character.level < required_level:
                return False, f"Requires level {required_level}"
        
        return True, "Can equip"
    
    def equip_item(self, character, item: Item, slot: str = None) -> Tuple[bool, str, Optional[Item]]:
        """Equip an item to character"""
        can_equip, message = self.can_equip_item(character, item)
        if not can_equip:
            return False, message, None
        
        # Determine equipment slot
        if slot is None:
            if item.item_type == ItemType.WEAPON:
                slot = "weapon"
            elif item.item_type == ItemType.ARMOR:
                slot = "armor"
            elif item.item_type == ItemType.ACCESSORY:
                # Find first available accessory slot
                if self.equipment_slots["accessory1"] is None:
                    slot = "accessory1"
                elif self.equipment_slots["accessory2"] is None:
                    slot = "accessory2"
                else:
                    return False, "No available accessory slots", None
            else:
                return False, "Item cannot be equipped", None
        
        # Store previously equipped item
        previously_equipped = self.equipment_slots.get(slot)
        
        # Equip new item
        self.equipment_slots[slot] = item
        
        # Apply item effects to character
        self._apply_item_effects(character, item, equip=True)
        
        return True, f"Equipped {item.name}", previously_equipped
    
    def unequip_item(self, character, slot: str) -> Tuple[bool, str, Optional[Item]]:
        """Unequip an item from character"""
        if slot not in self.equipment_slots:
            return False, "Invalid equipment slot", None
        
        item = self.equipment_slots[slot]
        if item is None:
            return False, "No item equipped in that slot", None
        
        # Remove item effects from character
        self._apply_item_effects(character, item, equip=False)
        
        # Unequip item
        self.equipment_slots[slot] = None
        
        return True, f"Unequipped {item.name}", item
    
    def _apply_item_effects(self, character, item: Item, equip: bool = True):
        """Apply or remove item effects to/from character"""
        multiplier = 1 if equip else -1
        
        effects = item.effects
        
        # Apply stat bonuses
        for stat in ["hp", "mp", "attack", "defense", "speed"]:
            if stat in effects:
                if stat in ["hp", "mp"]:
                    # For HP/MP, also update max values
                    max_stat = f"max_{stat}"
                    if max_stat in character.stats:
                        character.stats[max_stat] += effects[stat] * multiplier
                        # Adjust current value if it exceeds new max
                        if character.stats[stat] > character.stats[max_stat]:
                            character.stats[stat] = character.stats[max_stat]
                else:
                    character.stats[stat] += effects[stat] * multiplier
    
    def use_consumable(self, character, item: Item) -> Tuple[bool, str, Dict[str, Any]]:
        """Use a consumable item"""
        if item.item_type != ItemType.CONSUMABLE:
            return False, "Item is not consumable", {}
        
        effects_applied = {}
        
        # Apply consumable effects
        if "heal" in item.effects:
            heal_amount = min(item.effects["heal"], character.stats["max_hp"] - character.stats["hp"])
            character.stats["hp"] += heal_amount
            effects_applied["healed"] = heal_amount
        
        if "mp_restore" in item.effects:
            mp_amount = min(item.effects["mp_restore"], character.stats["max_mp"] - character.stats["mp"])
            character.stats["mp"] += mp_amount
            effects_applied["mp_restored"] = mp_amount
        
        if "cure_poison" in item.effects:
            # This would remove poison status effect
            effects_applied["poison_cured"] = True
        
        return True, f"Used {item.name}", effects_applied
    
    def get_equipment_info(self) -> Dict[str, Any]:
        """Get information about currently equipped items"""
        equipment_info = {}
        
        for slot, item in self.equipment_slots.items():
            if item:
                equipment_info[slot] = {
                    "name": item.name,
                    "type": item.item_type.value,
                    "rarity": item.rarity.value,
                    "effects": item.effects
                }
            else:
                equipment_info[slot] = None
        
        return equipment_info
    
    def calculate_total_equipment_effects(self) -> Dict[str, Any]:
        """Calculate total effects from all equipped items"""
        total_effects = {}
        
        for item in self.equipment_slots.values():
            if item:
                for effect, value in item.effects.items():
                    if effect in total_effects:
                        if isinstance(value, (int, float)):
                            total_effects[effect] += value
                        elif isinstance(value, bool) and value:
                            total_effects[effect] = True
                    else:
                        total_effects[effect] = value
        
        return total_effects
    
    def get_items_by_type(self, item_type: ItemType) -> List[Item]:
        """Get all items of a specific type"""
        return [item for item in self.item_database.values() if item.item_type == item_type]
    
    def get_fusion_catalysts(self) -> List[Item]:
        """Get all fusion catalyst items"""
        return self.get_items_by_type(ItemType.FUSION_CATALYST)
