"""
Location system for Me? Reincarnated?
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random

class LocationType(Enum):
    FOREST = "forest"
    CAVE = "cave"
    RUINS = "ruins"
    MEADOW = "meadow"
    SWAMP = "swamp"
    CLIFFS = "cliffs"
    VILLAGE = "village"

class EncounterType(Enum):
    COMBAT = "combat"
    NPC = "npc"
    TREASURE = "treasure"
    PUZZLE = "puzzle"
    LORE = "lore"
    RESOURCE = "resource"
    SECRET = "secret"

@dataclass
class Encounter:
    name: str
    encounter_type: EncounterType
    description: str
    requirements: Dict[str, Any]
    rewards: Dict[str, Any]
    one_time: bool = False
    rarity: float = 1.0  # 0.0 to 1.0, higher = more common

@dataclass
class Location:
    name: str
    location_type: LocationType
    description: str
    long_description: str
    encounters: List[Encounter]
    connected_locations: List[str]
    resources: List[str]
    secrets: List[str]
    npcs: List[str]
    level_range: Tuple[int, int]
    special_features: List[str]

class LocationSystem:
    """Manages game world locations and encounters"""
    
    def __init__(self):
        self.locations = self._initialize_locations()
        self.discovered_secrets = set()
        self.completed_encounters = set()
    
    def _initialize_locations(self) -> Dict[str, Location]:
        """Initialize all game locations"""
        locations = {}
        
        # 1. Mysterious Forest (Starting Area)
        locations["Mysterious Forest"] = Location(
            name="Mysterious Forest",
            location_type=LocationType.FOREST,
            description="A dense forest filled with ancient trees and mystical energy",
            long_description="Towering trees stretch toward the sky, their canopy filtering sunlight into dancing patterns. The air hums with magical energy, and you can hear the whispers of forest spirits in the wind. Moss-covered stones hint at ancient secrets hidden within.",
            encounters=[
                Encounter(
                    name="Forest Wolf Pack",
                    encounter_type=EncounterType.COMBAT,
                    description="A pack of wolves emerges from the underbrush",
                    requirements={},
                    rewards={"experience": 25, "loot": ["Wolf Fang", "Wolf Pelt"]},
                    rarity=0.7
                ),
                Encounter(
                    name="Elder Sprite",
                    encounter_type=EncounterType.NPC,
                    description="A wise forest sprite offers guidance",
                    requirements={},
                    rewards={"skill": "Nature Magic", "relationship": "Elder Sprite"},
                    one_time=True,
                    rarity=0.3
                ),
                Encounter(
                    name="Hidden Grove",
                    encounter_type=EncounterType.SECRET,
                    description="A secret grove with a wisdom tree",
                    requirements={"skill": "Detect Magic"},
                    rewards={"skill": "Wisdom", "item": "Ancient Seed"},
                    one_time=True,
                    rarity=0.1
                ),
                Encounter(
                    name="Healing Herbs",
                    encounter_type=EncounterType.RESOURCE,
                    description="You find medicinal plants growing here",
                    requirements={},
                    rewards={"item": "Healing Herb"},
                    rarity=0.5
                )
            ],
            connected_locations=["Crystal Caves", "Moonlit Meadow"],
            resources=["Healing Herbs", "Fresh Water", "Wood"],
            secrets=["Ancient Shrine", "Wisdom Tree"],
            npcs=["Elder Sprite", "Lost Traveler"],
            level_range=(1, 5),
            special_features=["Magical energy concentration", "Spirit activity"]
        )
        
        # 2. Crystal Caves
        locations["Crystal Caves"] = Location(
            name="Crystal Caves",
            location_type=LocationType.CAVE,
            description="Glittering caves filled with magical crystals",
            long_description="The cave walls sparkle with embedded crystals that pulse with inner light. The air is cool and echoes with the sound of dripping water. Deeper passages lead into darkness, while crystal formations create natural chambers of breathtaking beauty.",
            encounters=[
                Encounter(
                    name="Crystal Bat Swarm",
                    encounter_type=EncounterType.COMBAT,
                    description="Crystalline bats attack with sonic screeches",
                    requirements={},
                    rewards={"experience": 30, "loot": ["Crystal Fragment", "Bat Wing"]},
                    rarity=0.6
                ),
                Encounter(
                    name="Dwarf Miner",
                    encounter_type=EncounterType.NPC,
                    description="A friendly dwarf offers to trade crystals",
                    requirements={},
                    rewards={"trade": True, "relationship": "Dwarf Miner"},
                    rarity=0.4
                ),
                Encounter(
                    name="Crystal Heart Chamber",
                    encounter_type=EncounterType.SECRET,
                    description="A chamber with a massive crystal heart",
                    requirements={"level": 5},
                    rewards={"skill": "Crystal Resonance", "item": "Heart Crystal"},
                    one_time=True,
                    rarity=0.1
                ),
                Encounter(
                    name="Gem Golem Guardian",
                    encounter_type=EncounterType.COMBAT,
                    description="An ancient golem protects the crystal deposits",
                    requirements={"level": 6},
                    rewards={"experience": 75, "loot": ["Magic Crystal", "Golem Core"]},
                    one_time=True,
                    rarity=0.2
                )
            ],
            connected_locations=["Mysterious Forest", "Abandoned Ruins"],
            resources=["Magic Crystals", "Rare Minerals", "Cave Mushrooms"],
            secrets=["Crystal Heart Chamber", "Underground River"],
            npcs=["Dwarf Miner", "Crystal Sage"],
            level_range=(3, 8),
            special_features=["Crystal resonance", "Echo chambers"]
        )
        
        # 3. Abandoned Ruins
        locations["Abandoned Ruins"] = Location(
            name="Abandoned Ruins",
            location_type=LocationType.RUINS,
            description="Ancient stone ruins covered in mysterious symbols",
            long_description="Crumbling stone structures rise from overgrown vegetation. Ancient symbols cover the walls, glowing faintly with residual magic. The air feels heavy with the weight of forgotten history and the presence of restless spirits.",
            encounters=[
                Encounter(
                    name="Skeleton Warriors",
                    encounter_type=EncounterType.COMBAT,
                    description="Undead guardians still patrol these halls",
                    requirements={},
                    rewards={"experience": 40, "loot": ["Bone Fragment", "Ancient Coin"]},
                    rarity=0.7
                ),
                Encounter(
                    name="Ghost Scholar",
                    encounter_type=EncounterType.NPC,
                    description="The spirit of an ancient scholar shares knowledge",
                    requirements={"skill": "Detect Magic"},
                    rewards={"lore": "Ancient History", "skill": "Ancient Language"},
                    one_time=True,
                    rarity=0.3
                ),
                Encounter(
                    name="Hidden Vault",
                    encounter_type=EncounterType.TREASURE,
                    description="A secret vault filled with ancient treasures",
                    requirements={"item": "Ancient Key"},
                    rewards={"item": ["Ancient Weapon", "Spell Scroll", "Gold"]},
                    one_time=True,
                    rarity=0.1
                ),
                Encounter(
                    name="Ancient Puzzle Door",
                    encounter_type=EncounterType.PUZZLE,
                    description="A door sealed with an ancient riddle",
                    requirements={"skill": "Ancient Language"},
                    rewards={"access": "Inner Sanctum", "skill": "Puzzle Solving"},
                    one_time=True,
                    rarity=0.2
                )
            ],
            connected_locations=["Crystal Caves", "Whispering Swamp"],
            resources=["Ancient Weapons", "Spell Scrolls", "Stone"],
            secrets=["Hidden Vault", "Teleportation Circle", "Ancient Library"],
            npcs=["Ghost Scholar", "Construct Guardian"],
            level_range=(4, 10),
            special_features=["Undead presence", "Ancient magic", "Historical significance"]
        )
        
        # 4. Moonlit Meadow
        locations["Moonlit Meadow"] = Location(
            name="Moonlit Meadow",
            location_type=LocationType.MEADOW,
            description="A peaceful meadow that glows under moonlight",
            long_description="Rolling hills covered in silver grass stretch under an eternal twilight sky. Luminescent flowers bloom in patterns that seem to shift with your gaze. The air is filled with the gentle chiming of wind through crystal formations.",
            encounters=[
                Encounter(
                    name="Moon Rabbit",
                    encounter_type=EncounterType.NPC,
                    description="A magical rabbit offers cryptic wisdom",
                    requirements={},
                    rewards={"skill": "Lunar Magic", "item": "Moon Essence"},
                    rarity=0.4
                ),
                Encounter(
                    name="Fairy Ring",
                    encounter_type=EncounterType.SECRET,
                    description="A circle of mushrooms pulses with fairy magic",
                    requirements={"time": "night"},
                    rewards={"skill": "Fairy Magic", "teleport": "Fairy Realm"},
                    one_time=True,
                    rarity=0.2
                ),
                Encounter(
                    name="Lunar Wisps",
                    encounter_type=EncounterType.COMBAT,
                    description="Playful but dangerous light spirits",
                    requirements={},
                    rewards={"experience": 20, "loot": ["Wisp Essence", "Moonstone"]},
                    rarity=0.5
                ),
                Encounter(
                    name="Starlight Meditation",
                    encounter_type=EncounterType.RESOURCE,
                    description="The peaceful energy here aids meditation",
                    requirements={},
                    rewards={"mp_restore": "full", "skill": "Meditation"},
                    rarity=0.6
                )
            ],
            connected_locations=["Mysterious Forest", "Windswept Cliffs"],
            resources=["Moonflowers", "Starlight Dew", "Peace"],
            secrets=["Fairy Ring", "Celestial Observatory", "Time Distortion"],
            npcs=["Fairy Queen", "Astronomer Ghost"],
            level_range=(2, 6),
            special_features=["Eternal twilight", "Peaceful aura", "Time distortion"]
        )
        
        # 5. Whispering Swamp
        locations["Whispering Swamp"] = Location(
            name="Whispering Swamp",
            location_type=LocationType.SWAMP,
            description="A murky swamp filled with dangerous creatures and hidden magic",
            long_description="Twisted trees rise from dark water, their branches draped with hanging moss. The air is thick with mist and the sound of unseen creatures. Ancient magic permeates this place, making it both dangerous and full of potential.",
            encounters=[
                Encounter(
                    name="Bog Monster",
                    encounter_type=EncounterType.COMBAT,
                    description="A creature of mud and vegetation attacks",
                    requirements={},
                    rewards={"experience": 45, "loot": ["Bog Essence", "Swamp Gas"]},
                    rarity=0.6
                ),
                Encounter(
                    name="Swamp Witch",
                    encounter_type=EncounterType.NPC,
                    description="A mysterious witch offers to teach skill fusion",
                    requirements={"skill_count": 5},
                    rewards={"skill_fusion": True, "relationship": "Swamp Witch"},
                    one_time=True,
                    rarity=0.2
                ),
                Encounter(
                    name="Ancient Ritual Site",
                    encounter_type=EncounterType.SECRET,
                    description="Stones arranged in a powerful ritual circle",
                    requirements={"skill": "Ancient Language"},
                    rewards={"skill": "Ritual Magic", "item": "Power Stone"},
                    one_time=True,
                    rarity=0.1
                ),
                Encounter(
                    name="Will-o'-Wisps",
                    encounter_type=EncounterType.PUZZLE,
                    description="Mysterious lights that lead you through the swamp",
                    requirements={},
                    rewards={"access": "Hidden Treasure", "skill": "Navigation"},
                    rarity=0.3
                )
            ],
            connected_locations=["Abandoned Ruins", "Sunken Village"],
            resources=["Rare Herbs", "Poison Ingredients", "Murky Water"],
            secrets=["Witch's Hut", "Ancient Ritual Site", "Hidden Treasure"],
            npcs=["Swamp Witch", "Hermit Sage"],
            level_range=(6, 12),
            special_features=["Poison resistance training", "Skill fusion teaching", "Ancient magic"]
        )
        
        return locations
    
    def get_location(self, location_name: str) -> Optional[Location]:
        """Get location by name"""
        return self.locations.get(location_name)
    
    def get_available_encounters(self, location_name: str, character_level: int, character_skills: List[str]) -> List[Encounter]:
        """Get available encounters for a location based on character state"""
        location = self.get_location(location_name)
        if not location:
            return []
        
        available_encounters = []
        
        for encounter in location.encounters:
            # Check if encounter is one-time and already completed
            encounter_id = f"{location_name}_{encounter.name}"
            if encounter.one_time and encounter_id in self.completed_encounters:
                continue
            
            # Check requirements
            if self._check_encounter_requirements(encounter, character_level, character_skills):
                available_encounters.append(encounter)
        
        return available_encounters
    
    def _check_encounter_requirements(self, encounter: Encounter, character_level: int, character_skills: List[str]) -> bool:
        """Check if character meets encounter requirements"""
        requirements = encounter.requirements
        
        # Check level requirement
        if "level" in requirements and character_level < requirements["level"]:
            return False
        
        # Check skill requirement
        if "skill" in requirements and requirements["skill"] not in character_skills:
            return False
        
        # Check skill count requirement
        if "skill_count" in requirements and len(character_skills) < requirements["skill_count"]:
            return False
        
        # Check item requirement (would need inventory check)
        if "item" in requirements:
            # This would need to check character inventory
            pass
        
        # Check time requirement (would need game time system)
        if "time" in requirements:
            # This would need to check current game time
            pass
        
        return True
    
    def trigger_encounter(self, location_name: str, encounter_name: str) -> Dict[str, Any]:
        """Trigger a specific encounter"""
        location = self.get_location(location_name)
        if not location:
            return {"success": False, "message": "Location not found"}
        
        encounter = None
        for enc in location.encounters:
            if enc.name == encounter_name:
                encounter = enc
                break
        
        if not encounter:
            return {"success": False, "message": "Encounter not found"}
        
        # Mark as completed if one-time
        if encounter.one_time:
            encounter_id = f"{location_name}_{encounter.name}"
            self.completed_encounters.add(encounter_id)
        
        return {
            "success": True,
            "encounter": encounter,
            "rewards": encounter.rewards,
            "description": encounter.description
        }
    
    def get_random_encounter(self, location_name: str, character_level: int, character_skills: List[str]) -> Optional[Encounter]:
        """Get a random encounter based on rarity weights"""
        available_encounters = self.get_available_encounters(location_name, character_level, character_skills)
        
        if not available_encounters:
            return None
        
        # Weight encounters by rarity
        weights = [encounter.rarity for encounter in available_encounters]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return None
        
        # Random selection based on weights
        rand_value = random.random() * total_weight
        current_weight = 0
        
        for i, encounter in enumerate(available_encounters):
            current_weight += weights[i]
            if rand_value <= current_weight:
                return encounter
        
        return available_encounters[-1]  # Fallback
    
    def get_connected_locations(self, location_name: str) -> List[str]:
        """Get locations connected to the current location"""
        location = self.get_location(location_name)
        return location.connected_locations if location else []
    
    def discover_secret(self, location_name: str, secret_name: str) -> bool:
        """Mark a secret as discovered"""
        secret_id = f"{location_name}_{secret_name}"
        if secret_id not in self.discovered_secrets:
            self.discovered_secrets.add(secret_id)
            return True
        return False
    
    def get_location_info(self, location_name: str, detailed: bool = False) -> Dict[str, Any]:
        """Get information about a location"""
        location = self.get_location(location_name)
        if not location:
            return {}
        
        info = {
            "name": location.name,
            "description": location.description,
            "type": location.location_type.value,
            "level_range": location.level_range,
            "connected_locations": location.connected_locations
        }
        
        if detailed:
            info.update({
                "long_description": location.long_description,
                "resources": location.resources,
                "npcs": location.npcs,
                "special_features": location.special_features,
                "encounter_count": len(location.encounters)
            })
        
        return info
