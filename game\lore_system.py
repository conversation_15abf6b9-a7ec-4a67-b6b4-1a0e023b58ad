"""
Lore and backstory system for character creation
"""
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class BackstoryScenario:
    """Represents a death/reincarnation scenario"""
    name: str
    description: str
    death_scene: str
    transition_scene: str
    reincarnation_explanation: str
    suggested_traits: List[str]
    suggested_occupations: List[str]
    # New fields for enhanced integration
    recommended_creatures: List[str]  # Creature types that fit this backstory
    stat_bonuses: Dict[str, int]  # Stat bonuses for this backstory
    personality_keywords: List[str]  # Keywords for AI response generation
    thematic_elements: List[str]  # Story elements to reference later

class LoreSystem:
    """Manages backstory scenarios and world lore"""

    def __init__(self):
        self.backstory_scenarios = self._initialize_backstory_scenarios()
        self.world_lore = self._initialize_world_lore()

    def _initialize_backstory_scenarios(self) -> List[BackstoryScenario]:
        """Initialize various death/reincarnation scenarios"""
        return [
            BackstoryScenario(
                name="Overwork Death",
                description="Died from exhaustion after working too many late nights",
                death_scene="""You remember the fluorescent lights of your office, the endless stack of papers, the constant ping of notifications. Your vision blurred as you reached for another cup of coffee, your heart racing from too much caffeine and too little sleep. The last thing you remember is your head hitting the desk as darkness consumed you...""",
                transition_scene="""You find yourself floating in a warm, ethereal space filled with gentle light. A voice, ancient and kind, speaks to you: "Your dedication was admirable, but your body could not keep pace with your spirit. You deserve a second chance, a new beginning where you can grow at your own pace.\"""",
                reincarnation_explanation="""The voice continues: "In this new world, strength comes not from endless toil, but from understanding yourself and growing through experience. Choose your new form wisely - each path offers unique opportunities for growth and discovery.\"""",
                suggested_traits=["determined", "hardworking", "analytical", "perfectionist"],
                suggested_occupations=["programmer", "accountant", "lawyer", "doctor", "engineer"],
                recommended_creatures=["Wisp", "Spider", "Mushroom"],  # Intellectual, methodical creatures
                stat_bonuses={"mp": 5, "defense": 3},  # Mental focus and resilience
                personality_keywords=["methodical", "persistent", "logical", "detail-oriented"],
                thematic_elements=["work ethic", "systematic approach", "gradual improvement", "mental fortitude"]
            ),
            BackstoryScenario(
                name="Heroic Sacrifice",
                description="Died saving someone from danger",
                death_scene="""The child's scream pierced through the busy street noise. Without thinking, you lunged forward, pushing them out of the path of the speeding truck. The impact was instant - a moment of searing pain, then peaceful darkness. Your last thought was relief that the child was safe...""",
                transition_scene="""You awaken in a realm of golden light, where a majestic presence radiates warmth and approval. "Your selfless act has not gone unnoticed," the voice resonates through your being. "Such courage and compassion deserve to be rewarded with a new chance at life.\"""",
                reincarnation_explanation="""\"In this world of magic and monsters, your heroic spirit will serve you well. But remember - true strength comes from protecting others, not just yourself. Your new form will reflect your noble heart.\"""",
                suggested_traits=["brave", "selfless", "protective", "compassionate"],
                suggested_occupations=["firefighter", "police officer", "paramedic", "teacher", "social worker"],
                recommended_creatures=["Goblin", "Rat", "Slime"],  # Creatures that can grow strong through determination
                stat_bonuses={"hp": 8, "attack": 2},  # Physical courage and protective instincts
                personality_keywords=["heroic", "selfless", "protective", "noble"],
                thematic_elements=["sacrifice", "protecting others", "courage under pressure", "moral strength"]
            ),
            BackstoryScenario(
                name="Accident",
                description="Died in an unexpected accident",
                death_scene="""It happened so fast. One moment you were walking down the familiar street, lost in thought about tomorrow's plans. The next, you heard the screech of brakes, the crash of metal, and felt yourself being thrown through the air. As consciousness faded, you wondered if you'd ever get to finish that book you were reading...""",
                transition_scene="""You drift in a space between spaces, where time seems meaningless. A gentle voice speaks: "Life is fragile and precious, and yours was cut short by mere chance. But endings can become beginnings, and every soul deserves to write their story fully.\"""",
                reincarnation_explanation="""\"This new world offers adventures beyond your wildest dreams. Your previous life's experiences will guide you, but your new form will determine how you interact with this magical realm. Choose carefully - your journey is just beginning.\"""",
                suggested_traits=["curious", "adaptable", "thoughtful", "resilient"],
                suggested_occupations=["student", "librarian", "artist", "writer", "researcher"],
                recommended_creatures=["Wisp", "Mushroom", "Spider"],  # Adaptable, observant creatures
                stat_bonuses={"speed": 4, "mp": 4},  # Quick adaptation and mental flexibility
                personality_keywords=["adaptable", "curious", "observant", "philosophical"],
                thematic_elements=["unexpected change", "adaptation", "new beginnings", "embracing uncertainty"]
            ),
            BackstoryScenario(
                name="Natural Causes",
                description="Died peacefully of old age or illness",
                death_scene="""You felt the weight of years settling on your shoulders as you lay in the hospital bed, surrounded by family photos and get-well cards. Your breathing grew shallow, but there was no fear - only a sense of completion and curiosity about what comes next. As your eyes closed for the final time, you smiled, thinking of all the memories you'd made...""",
                transition_scene="""You find yourself in a serene garden where time flows like honey. An ancient, wise presence speaks: "You lived a full life and touched many hearts. But every ending is also a beginning, and your wisdom should not be lost to the world.\"""",
                reincarnation_explanation="""\"In this new realm, your accumulated wisdom will be your greatest asset. Though your body will be young and different, your soul carries the experience of a lifetime. Use it well to guide others and build something lasting.\"""",
                suggested_traits=["wise", "patient", "kind", "experienced"],
                suggested_occupations=["teacher", "counselor", "manager", "parent", "mentor"],
                recommended_creatures=["Mushroom", "Wisp", "Slime"],  # Wise, nurturing creatures
                stat_bonuses={"hp": 6, "defense": 4},  # Life experience and resilience
                personality_keywords=["wise", "patient", "nurturing", "experienced"],
                thematic_elements=["life experience", "wisdom", "mentorship", "peaceful transition"]
            ),
            BackstoryScenario(
                name="Gaming Accident",
                description="Died while playing games (classic isekai trope)",
                death_scene="""You were deep into your favorite RPG, having just reached a new area after hours of grinding. Your eyes burned from staring at the screen, but you were so close to the next level. You reached for your energy drink, not noticing the frayed power cord. The electric shock was brief but fatal, your last sight being your character standing at the entrance to a mysterious dungeon...""",
                transition_scene="""You awaken in what looks suspiciously like a game menu screen, complete with floating text and ethereal music. A voice that sounds like a game narrator speaks: "Player, your dedication to virtual worlds has been noted. Perhaps it's time you experienced the real thing.\"""",
                reincarnation_explanation="""\"Welcome to a world where stats are real, skills can be learned through practice, and evolution is possible. Your gaming knowledge will serve you well here - but remember, there are no respawn points. Make your choices count.\"""",
                suggested_traits=["strategic", "persistent", "competitive", "analytical"],
                suggested_occupations=["gamer", "programmer", "student", "streamer", "game designer"],
                recommended_creatures=["Spider", "Goblin", "Rat"],  # Strategic, tactical creatures
                stat_bonuses={"attack": 3, "speed": 5},  # Gaming reflexes and strategic thinking
                personality_keywords=["strategic", "competitive", "tactical", "min-maxing"],
                thematic_elements=["gaming knowledge", "strategic thinking", "progression systems", "virtual experience"]
            )
        ]

    def _initialize_world_lore(self) -> Dict[str, Any]:
        """Initialize world lore and background information"""
        return {
            "world_name": "Aethermoor",
            "creation_myth": "Born from the dreams of sleeping gods, Aethermoor is a realm where consciousness shapes reality and evolution is driven by will and experience.",
            "magic_system": "Magic flows through all living things as 'Essence' - the more you understand yourself and your place in the world, the stronger your magical abilities become.",
            "reincarnation_rules": "Souls from other worlds are drawn to Aethermoor when they die with unfulfilled potential. The form they take depends on their nature, experiences, and deepest desires.",
            "evolution_philosophy": "In Aethermoor, evolution is not just physical - it's a transformation of the entire being. Creatures evolve by mastering skills, forming relationships, and understanding their true nature.",
            "starting_location_lore": {
                "Mysterious Forest": "An ancient woodland where new souls first awaken. The trees here are said to remember every reincarnated being that has passed through, and sometimes whisper advice to those who listen carefully."
            }
        }

    def get_random_backstory(self) -> BackstoryScenario:
        """Get a random backstory scenario"""
        return random.choice(self.backstory_scenarios)

    def get_backstory_by_name(self, name: str) -> Optional[BackstoryScenario]:
        """Get a specific backstory scenario by name"""
        for scenario in self.backstory_scenarios:
            if scenario.name.lower() == name.lower():
                return scenario
        return None

    def get_world_lore(self, topic: str) -> Optional[str]:
        """Get world lore about a specific topic"""
        return self.world_lore.get(topic)

    def get_all_backstory_names(self) -> List[str]:
        """Get names of all available backstory scenarios"""
        return [scenario.name for scenario in self.backstory_scenarios]

    def get_trait_suggestions(self, backstory: BackstoryScenario) -> List[str]:
        """Get trait suggestions based on backstory"""
        return backstory.suggested_traits

    def get_occupation_suggestions(self, backstory: BackstoryScenario) -> List[str]:
        """Get occupation suggestions based on backstory"""
        return backstory.suggested_occupations

    def get_creature_recommendations(self, backstory: BackstoryScenario) -> List[str]:
        """Get creature recommendations based on backstory"""
        return backstory.recommended_creatures

    def get_stat_bonuses(self, backstory: BackstoryScenario) -> Dict[str, int]:
        """Get stat bonuses for a backstory"""
        return backstory.stat_bonuses

    def validate_trait_synergy(self, backstory: BackstoryScenario, traits: List[str]) -> Dict[str, Any]:
        """Validate how well chosen traits match the backstory"""
        suggested_traits = [trait.lower() for trait in backstory.suggested_traits]
        chosen_traits = [trait.lower().strip() for trait in traits]

        matches = [trait for trait in chosen_traits if trait in suggested_traits]
        synergy_score = len(matches) / len(suggested_traits) if suggested_traits else 0

        return {
            "synergy_score": synergy_score,
            "matching_traits": matches,
            "bonus_description": self._get_synergy_bonus_description(synergy_score),
            "narrative_bonus": synergy_score >= 0.5  # 50% or more matching traits
        }

    def validate_occupation_synergy(self, backstory: BackstoryScenario, occupation: str) -> Dict[str, Any]:
        """Validate how well chosen occupation matches the backstory"""
        suggested_occupations = [occ.lower() for occ in backstory.suggested_occupations]
        chosen_occupation = occupation.lower().strip()

        is_match = chosen_occupation in suggested_occupations

        # Enhanced partial matching with keyword overlap
        partial_match = False
        if not is_match:
            # Check for substring matches
            partial_match = any(occ in chosen_occupation or chosen_occupation in occ
                              for occ in suggested_occupations)

            # Check for keyword overlap (e.g., "software engineer" and "programmer")
            if not partial_match:
                occupation_keywords = set(chosen_occupation.split())
                for suggested_occ in suggested_occupations:
                    suggested_keywords = set(suggested_occ.split())
                    # If there's any keyword overlap, consider it a partial match
                    if occupation_keywords.intersection(suggested_keywords):
                        partial_match = True
                        break

        return {
            "is_perfect_match": is_match,
            "is_partial_match": partial_match,
            "bonus_description": self._get_occupation_bonus_description(is_match, partial_match),
            "narrative_bonus": is_match or partial_match
        }

    def _get_synergy_bonus_description(self, synergy_score: float) -> str:
        """Get description for trait synergy bonus"""
        if synergy_score >= 0.8:
            return "Perfect harmony with your past life grants significant bonuses!"
        elif synergy_score >= 0.5:
            return "Good alignment with your past experiences provides moderate bonuses."
        elif synergy_score >= 0.3:
            return "Some connection to your past life offers minor bonuses."
        else:
            return "Your new personality differs greatly from your past, but offers unique growth potential."

    def _get_occupation_bonus_description(self, perfect_match: bool, partial_match: bool) -> str:
        """Get description for occupation synergy bonus"""
        if perfect_match:
            return "Your past profession perfectly aligns with your reincarnation circumstances!"
        elif partial_match:
            return "Your past profession has some relevance to your new situation."
        else:
            return "Your past profession offers a unique perspective in this new world."

    def get_backstory_context_for_ai(self, backstory: BackstoryScenario) -> str:
        """Get backstory context formatted for AI responses"""
        return f"""
        BACKSTORY CONTEXT:
        - Death Type: {backstory.name}
        - Personality Keywords: {', '.join(backstory.personality_keywords)}
        - Thematic Elements: {', '.join(backstory.thematic_elements)}
        - Character Archetype: {backstory.description}

        Use these elements to create responses that feel consistent with the character's origin story.
        Reference their past life experiences and death circumstances when appropriate.
        """
