"""
Two-tiered memory system for the game
"""
from typing import List, Dict, Any
from collections import deque
import config

class MemorySystem:
    def __init__(self):
        """Initialize the memory system"""
        # Short-term memory: Recent interactions and immediate context
        self.short_term_memory = deque(maxlen=config.MAX_SHORT_TERM_MEMORY)

        # Long-term memory: Persistent world state and important events
        self.long_term_memory = {
            "important_events": [],
            "discovered_locations": [],
            "met_npcs": {},
            "completed_quests": [],
            "world_changes": [],
            "learned_lore": []
        }

        # Current context for immediate decision making
        self.current_context = {
            "location": "",
            "active_npcs": [],
            "current_quest": None,
            "immediate_threats": [],
            "available_actions": []
        }

        # Anti-repetition tracking
        self.action_history = deque(maxlen=20)  # Track last 20 actions
        self.action_cooldowns = {}  # Track action cooldowns
        self.response_patterns = deque(maxlen=10)  # Track AI response patterns
        self.location_action_counts = {}  # Track actions per location

    def add_short_term_event(self, event: str):
        """Add an event to short-term memory"""
        self.short_term_memory.append(event)

    def add_important_event(self, event: str, category: str = "general"):
        """Add an important event to long-term memory"""
        event_data = {
            "event": event,
            "category": category,
            "timestamp": len(self.long_term_memory["important_events"])
        }
        self.long_term_memory["important_events"].append(event_data)

    def discover_location(self, location_name: str, description: str):
        """Record a newly discovered location"""
        if location_name not in [loc["name"] for loc in self.long_term_memory["discovered_locations"]]:
            location_data = {
                "name": location_name,
                "description": description,
                "first_visited": len(self.long_term_memory["discovered_locations"]),
                "times_visited": 1
            }
            self.long_term_memory["discovered_locations"].append(location_data)
        else:
            # Increment visit count
            for loc in self.long_term_memory["discovered_locations"]:
                if loc["name"] == location_name:
                    loc["times_visited"] += 1
                    break

    def meet_npc(self, npc_name: str, first_impression: str, relationship_value: int = 0):
        """Record meeting a new NPC"""
        if npc_name not in self.long_term_memory["met_npcs"]:
            self.long_term_memory["met_npcs"][npc_name] = {
                "first_impression": first_impression,
                "relationship": relationship_value,
                "interactions": [],
                "last_seen": "recently"
            }

    def update_npc_relationship(self, npc_name: str, interaction: str, relationship_change: int = 0):
        """Update relationship with an NPC"""
        if npc_name in self.long_term_memory["met_npcs"]:
            npc_data = self.long_term_memory["met_npcs"][npc_name]
            npc_data["interactions"].append(interaction)
            npc_data["relationship"] += relationship_change
            npc_data["relationship"] = max(-100, min(100, npc_data["relationship"]))  # Clamp to -100 to 100
            npc_data["last_seen"] = "recently"

    def complete_quest(self, quest_name: str, outcome: str):
        """Record a completed quest"""
        quest_data = {
            "name": quest_name,
            "outcome": outcome,
            "completion_order": len(self.long_term_memory["completed_quests"])
        }
        self.long_term_memory["completed_quests"].append(quest_data)

    def record_world_change(self, change_description: str, impact_level: str = "minor"):
        """Record a change to the world state"""
        change_data = {
            "description": change_description,
            "impact": impact_level,
            "order": len(self.long_term_memory["world_changes"])
        }
        self.long_term_memory["world_changes"].append(change_data)

    def learn_lore(self, lore_item: str, source: str = "unknown"):
        """Add a piece of lore to memory"""
        lore_data = {
            "lore": lore_item,
            "source": source,
            "learned_order": len(self.long_term_memory["learned_lore"])
        }
        self.long_term_memory["learned_lore"].append(lore_data)

    def update_current_context(self, **kwargs):
        """Update the current context"""
        for key, value in kwargs.items():
            if key in self.current_context:
                self.current_context[key] = value

    def get_recent_memory_summary(self, count: int = 5) -> str:
        """Get a summary of recent events"""
        recent_events = list(self.short_term_memory)[-count:]
        return " ".join(recent_events) if recent_events else "Nothing recent to remember."

    def get_location_context(self, location_name: str) -> str:
        """Get context about a specific location"""
        for loc in self.long_term_memory["discovered_locations"]:
            if loc["name"] == location_name:
                return f"You've been to {location_name} {loc['times_visited']} time(s). {loc['description']}"
        return f"You haven't been to {location_name} before."

    def get_npc_context(self, npc_name: str) -> str:
        """Get context about a specific NPC"""
        if npc_name in self.long_term_memory["met_npcs"]:
            npc = self.long_term_memory["met_npcs"][npc_name]
            relationship_desc = "neutral"
            if npc["relationship"] > 20:
                relationship_desc = "friendly"
            elif npc["relationship"] > 50:
                relationship_desc = "close ally"
            elif npc["relationship"] < -20:
                relationship_desc = "hostile"
            elif npc["relationship"] < -50:
                relationship_desc = "enemy"

            return f"{npc_name}: {relationship_desc} relationship ({npc['relationship']}). {npc['first_impression']}"
        return f"You haven't met {npc_name} yet."

    def record_player_action(self, action: str, location: str):
        """Record a player action for anti-repetition tracking"""
        # Add to action history
        self.action_history.append({
            "action": action.lower().strip(),
            "location": location,
            "timestamp": len(self.action_history)
        })

        # Track location-specific action counts
        if location not in self.location_action_counts:
            self.location_action_counts[location] = {}

        action_key = action.lower().strip()
        if action_key not in self.location_action_counts[location]:
            self.location_action_counts[location][action_key] = 0
        self.location_action_counts[location][action_key] += 1

    def record_ai_response_pattern(self, response: str):
        """Record AI response pattern for variety tracking"""
        # Extract key phrases and patterns from response
        response_lower = response.lower()
        patterns = []

        # Check for common response patterns
        if "you see" in response_lower:
            patterns.append("descriptive_sight")
        if "you hear" in response_lower:
            patterns.append("descriptive_sound")
        if "you feel" in response_lower:
            patterns.append("descriptive_feeling")
        if any(word in response_lower for word in ["suddenly", "unexpectedly", "without warning"]):
            patterns.append("sudden_event")
        if any(word in response_lower for word in ["nothing happens", "no change", "remains the same"]):
            patterns.append("no_effect")

        self.response_patterns.append({
            "patterns": patterns,
            "length": len(response),
            "timestamp": len(self.response_patterns)
        })

    def is_action_repetitive(self, action: str, location: str, threshold: int = 3) -> bool:
        """Check if an action is being repeated too frequently"""
        action_key = action.lower().strip()

        # Check recent action history (last 5 actions)
        recent_actions = list(self.action_history)[-5:]
        recent_same_actions = [a for a in recent_actions if a["action"] == action_key]

        if len(recent_same_actions) >= 2:  # Same action twice in last 5
            return True

        # Check location-specific repetition
        if location in self.location_action_counts:
            location_count = self.location_action_counts[location].get(action_key, 0)
            if location_count >= threshold:
                return True

        return False

    def get_action_suggestions_context(self, location: str) -> Dict[str, Any]:
        """Get context for generating action suggestions"""
        recent_actions = [a["action"] for a in list(self.action_history)[-5:]]
        location_actions = self.location_action_counts.get(location, {})

        return {
            "recent_actions": recent_actions,
            "location_action_counts": location_actions,
            "overused_actions": [action for action, count in location_actions.items() if count >= 3],
            "response_patterns": list(self.response_patterns)[-3:]  # Last 3 response patterns
        }

    def set_action_cooldown(self, action: str, duration: int):
        """Set a cooldown for a specific action"""
        self.action_cooldowns[action.lower().strip()] = duration

    def is_action_on_cooldown(self, action: str) -> bool:
        """Check if an action is on cooldown"""
        action_key = action.lower().strip()
        if action_key in self.action_cooldowns:
            if self.action_cooldowns[action_key] > 0:
                return True
            else:
                del self.action_cooldowns[action_key]
        return False

    def update_cooldowns(self):
        """Update all action cooldowns (call each turn)"""
        for action in list(self.action_cooldowns.keys()):
            self.action_cooldowns[action] -= 1
            if self.action_cooldowns[action] <= 0:
                del self.action_cooldowns[action]

    def to_dict(self) -> Dict[str, Any]:
        """Convert memory system to dictionary for saving"""
        return {
            "short_term_memory": list(self.short_term_memory),
            "long_term_memory": self.long_term_memory,
            "current_context": self.current_context,
            "action_history": list(self.action_history),
            "action_cooldowns": self.action_cooldowns,
            "response_patterns": list(self.response_patterns),
            "location_action_counts": self.location_action_counts
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemorySystem':
        """Create memory system from dictionary"""
        memory = cls()

        # Restore short-term memory
        for event in data.get("short_term_memory", []):
            memory.short_term_memory.append(event)

        # Restore long-term memory
        memory.long_term_memory = data.get("long_term_memory", memory.long_term_memory)

        # Restore current context
        memory.current_context = data.get("current_context", memory.current_context)

        # Restore anti-repetition data
        if "action_history" in data:
            memory.action_history.extend(data["action_history"])
        if "action_cooldowns" in data:
            memory.action_cooldowns.update(data["action_cooldowns"])
        if "response_patterns" in data:
            memory.response_patterns.extend(data["response_patterns"])
        if "location_action_counts" in data:
            memory.location_action_counts.update(data["location_action_counts"])

        return memory
