"""
NPC system for managing non-player characters and their interactions
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import random

class NPCPersonality(Enum):
    FRIENDLY = "friendly"
    WISE = "wise"
    MYSTERIOUS = "mysterious"
    GRUFF = "gruff"
    CHEERFUL = "cheerful"
    MELANCHOLIC = "melancholic"
    SCHOLARLY = "scholarly"
    MISCHIEVOUS = "mischievous"
    PROTECTIVE = "protective"
    SUSPICIOUS = "suspicious"

class NPCRole(Enum):
    MERCHANT = "merchant"
    TEACHER = "teacher"
    QUEST_GIVER = "quest_giver"
    LORE_KEEPER = "lore_keeper"
    GUARDIAN = "guardian"
    WANDERER = "wanderer"
    CRAFTSMAN = "craftsman"
    MYSTIC = "mystic"

class RelationshipLevel(Enum):
    ENEMY = "enemy"           # -100 to -51
    HOSTILE = "hostile"       # -50 to -21
    UNFRIENDLY = "unfriendly" # -20 to -1
    NEUTRAL = "neutral"       # 0 to 20
    FRIENDLY = "friendly"     # 21 to 50
    CLOSE = "close"          # 51 to 80
    DEVOTED = "devoted"      # 81 to 100

@dataclass
class NPCData:
    name: str
    personality: NPCPersonality
    role: NPCRole
    description: str
    location: str
    level: int
    
    # Dialogue and interaction data
    greeting_messages: List[str] = field(default_factory=list)
    personality_traits: List[str] = field(default_factory=list)
    interests: List[str] = field(default_factory=list)
    knowledge_areas: List[str] = field(default_factory=list)
    
    # Relationship modifiers
    likes: List[str] = field(default_factory=list)  # Things that improve relationship
    dislikes: List[str] = field(default_factory=list)  # Things that worsen relationship
    
    # Quest and service data
    available_quests: List[str] = field(default_factory=list)
    services: List[str] = field(default_factory=list)  # What they can do for player
    
    # Special conditions
    availability_conditions: Dict[str, Any] = field(default_factory=dict)
    unique_dialogue_triggers: Dict[str, str] = field(default_factory=dict)

class NPCSystem:
    """Manages all NPCs in the game world"""
    
    def __init__(self):
        self.npcs = self._initialize_npcs()
        self.active_conversations = {}  # Track ongoing conversations
        
    def _initialize_npcs(self) -> Dict[str, NPCData]:
        """Initialize all NPCs in the game world"""
        npcs = {}
        
        # Mysterious Forest NPCs
        npcs["Elder Sprite"] = NPCData(
            name="Elder Sprite",
            personality=NPCPersonality.WISE,
            role=NPCRole.TEACHER,
            description="An ancient forest spirit with deep knowledge of nature magic",
            location="Mysterious Forest",
            level=15,
            greeting_messages=[
                "Young one, the forest whispers of your arrival...",
                "I sense great potential within you, little creature.",
                "The ancient trees have been expecting you."
            ],
            personality_traits=["patient", "cryptic", "nurturing", "ancient"],
            interests=["nature magic", "forest lore", "spiritual growth", "balance"],
            knowledge_areas=["Nature Magic", "Forest Secrets", "Spiritual Wisdom", "Ancient History"],
            likes=["respect for nature", "curiosity", "helping others", "peaceful actions"],
            dislikes=["destroying nature", "impatience", "greed", "violence"],
            services=["Nature Magic Training", "Forest Guidance", "Spiritual Advice"],
            unique_dialogue_triggers={
                "first_meeting": "Ah, a newly reincarnated soul. How fascinating...",
                "high_nature_skills": "I see you've been learning the ways of nature. Excellent.",
                "environmental_damage": "I am disappointed in your disregard for the natural world."
            }
        )
        
        npcs["Lost Traveler"] = NPCData(
            name="Lost Traveler",
            personality=NPCPersonality.MELANCHOLIC,
            role=NPCRole.WANDERER,
            description="A human who became lost in the magical forest years ago",
            location="Mysterious Forest",
            level=8,
            greeting_messages=[
                "Oh! Another soul in this endless forest...",
                "Have you seen the way out? I've been searching for so long...",
                "You're not from around here either, are you?"
            ],
            personality_traits=["lonely", "nostalgic", "helpful", "lost"],
            interests=["finding home", "sharing stories", "human culture", "survival"],
            knowledge_areas=["Human World", "Survival Skills", "Local Geography", "Trade Routes"],
            likes=["companionship", "sharing stories", "help with directions", "kindness"],
            dislikes=["being ignored", "false hope", "mockery", "abandonment"],
            services=["Trade Information", "Survival Tips", "Human World Knowledge"],
            available_quests=["Find the Lost Path", "Deliver Message to Family"],
            unique_dialogue_triggers={
                "fellow_reincarnated": "Wait... you're not originally from this world either, are you?",
                "found_exit": "You found a way out? Please, tell me everything!",
                "long_conversation": "It's been so long since I had someone to talk to..."
            }
        )
        
        # Crystal Caves NPCs
        npcs["Dwarf Miner"] = NPCData(
            name="Dwarf Miner",
            personality=NPCPersonality.GRUFF,
            role=NPCRole.CRAFTSMAN,
            description="A sturdy dwarf who has spent decades mining the crystal caves",
            location="Crystal Caves",
            level=12,
            greeting_messages=[
                "Aye, what brings ye to these depths?",
                "Another surface dweller, eh? Watch yer step down here.",
                "These crystals don't mine themselves, ye know."
            ],
            personality_traits=["hardworking", "practical", "gruff", "skilled"],
            interests=["mining", "crafting", "gems", "dwarven culture"],
            knowledge_areas=["Mining Techniques", "Crystal Properties", "Crafting", "Cave Systems"],
            likes=["hard work", "quality craftsmanship", "fair trade", "respect"],
            dislikes=["laziness", "poor quality", "surface dweller arrogance", "cave damage"],
            services=["Crystal Identification", "Mining Training", "Tool Crafting"],
            available_quests=["Clear the Blocked Tunnel", "Find Rare Crystal Samples"],
            unique_dialogue_triggers={
                "mining_skills": "Ye've got the hands of a miner, I'll give ye that.",
                "crystal_knowledge": "Impressive! Not many know their crystals like that.",
                "cave_damage": "What have ye done to my beautiful caves?!"
            }
        )
        
        npcs["Crystal Sage"] = NPCData(
            name="Crystal Sage",
            personality=NPCPersonality.MYSTERIOUS,
            role=NPCRole.MYSTIC,
            description="An enigmatic figure who studies the magical properties of crystals",
            location="Crystal Caves",
            level=20,
            greeting_messages=[
                "The crystals sing of your presence...",
                "I have been expecting you, though I know not why.",
                "The resonance around you is... unusual."
            ],
            personality_traits=["mystical", "insightful", "cryptic", "powerful"],
            interests=["crystal magic", "divination", "magical theory", "ancient mysteries"],
            knowledge_areas=["Crystal Magic", "Divination", "Magical Theory", "Ancient Prophecies"],
            likes=["magical curiosity", "respect for power", "seeking knowledge", "mystical discussions"],
            dislikes=["magical recklessness", "disrespect", "ignorance", "mundane concerns"],
            services=["Crystal Magic Training", "Magical Consultation", "Divination"],
            available_quests=["Retrieve the Lost Crystal", "Investigate Magical Disturbance"],
            unique_dialogue_triggers={
                "high_magic": "Your magical aura has grown considerably. Fascinating.",
                "prophecy_related": "The prophecies speak of one such as you...",
                "crystal_mastery": "You understand the crystals' song. Remarkable."
            }
        )
        
        # Moonlit Meadow NPCs
        npcs["Fairy Queen"] = NPCData(
            name="Fairy Queen",
            personality=NPCPersonality.CHEERFUL,
            role=NPCRole.GUARDIAN,
            description="The radiant ruler of the meadow fairies, protector of peaceful creatures",
            location="Moonlit Meadow",
            level=25,
            greeting_messages=[
                "Welcome to our peaceful realm, little one!",
                "The moonlight dances with joy at your arrival!",
                "Such a curious creature you are! How delightful!"
            ],
            personality_traits=["joyful", "protective", "magical", "caring"],
            interests=["protecting nature", "fairy magic", "peaceful coexistence", "moonlight"],
            knowledge_areas=["Fairy Magic", "Nature Protection", "Peaceful Arts", "Lunar Magic"],
            likes=["kindness to small creatures", "peaceful actions", "natural beauty", "joy"],
            dislikes=["violence", "cruelty", "darkness", "destroying beauty"],
            services=["Fairy Magic Blessing", "Nature Protection", "Peaceful Guidance"],
            available_quests=["Protect the Fairy Ring", "Restore Moonlight Balance"],
            unique_dialogue_triggers={
                "peaceful_nature": "Your gentle spirit shines like moonlight!",
                "violence_history": "I sense darkness in your past... but also potential for light.",
                "fairy_friend": "The fairies have told me wonderful things about you!"
            }
        )
        
        npcs["Astronomer Ghost"] = NPCData(
            name="Astronomer Ghost",
            personality=NPCPersonality.SCHOLARLY,
            role=NPCRole.LORE_KEEPER,
            description="The spirit of an ancient scholar who studied the stars and time",
            location="Moonlit Meadow",
            level=18,
            greeting_messages=[
                "Ah, a visitor to my eternal observatory...",
                "The stars have foretold your coming, young seeker.",
                "Time flows strangely here... have we met before?"
            ],
            personality_traits=["intellectual", "ethereal", "patient", "wise"],
            interests=["astronomy", "time magic", "ancient knowledge", "cosmic mysteries"],
            knowledge_areas=["Astronomy", "Time Magic", "Ancient History", "Cosmic Lore"],
            likes=["intellectual discussion", "curiosity about cosmos", "patience", "learning"],
            dislikes=["ignorance", "impatience", "disrespect for knowledge", "time waste"],
            services=["Astronomical Knowledge", "Time Magic Guidance", "Historical Records"],
            available_quests=["Realign the Celestial Observatory", "Recover Lost Star Charts"],
            unique_dialogue_triggers={
                "time_magic": "You dabble in temporal arts? How intriguing...",
                "star_knowledge": "Your understanding of the cosmos is impressive.",
                "ancient_history": "Yes, I remember those times... such memories..."
            }
        )
        
        return npcs
    
    def get_npc(self, npc_name: str) -> Optional[NPCData]:
        """Get NPC data by name"""
        return self.npcs.get(npc_name)
    
    def get_npcs_in_location(self, location: str) -> List[NPCData]:
        """Get all NPCs in a specific location"""
        return [npc for npc in self.npcs.values() if npc.location == location]
    
    def is_npc_available(self, npc_name: str, character, memory_system) -> bool:
        """Check if an NPC is available for interaction"""
        npc = self.get_npc(npc_name)
        if not npc:
            return False
        
        # Check availability conditions
        conditions = npc.availability_conditions
        
        # Level requirements
        if "min_level" in conditions and character.level < conditions["min_level"]:
            return False
        
        # Quest requirements
        if "required_quest" in conditions:
            completed_quests = memory_system.long_term_memory.get("completed_quests", [])
            quest_names = [q["name"] for q in completed_quests]
            if conditions["required_quest"] not in quest_names:
                return False
        
        # Relationship requirements
        if "min_relationship" in conditions:
            npc_data = memory_system.long_term_memory.get("met_npcs", {}).get(npc_name, {})
            relationship = npc_data.get("relationship", 0)
            if relationship < conditions["min_relationship"]:
                return False
        
        return True
    
    def get_relationship_level(self, relationship_value: int) -> RelationshipLevel:
        """Convert numeric relationship to relationship level"""
        if relationship_value <= -51:
            return RelationshipLevel.ENEMY
        elif relationship_value <= -21:
            return RelationshipLevel.HOSTILE
        elif relationship_value <= -1:
            return RelationshipLevel.UNFRIENDLY
        elif relationship_value <= 20:
            return RelationshipLevel.NEUTRAL
        elif relationship_value <= 50:
            return RelationshipLevel.FRIENDLY
        elif relationship_value <= 80:
            return RelationshipLevel.CLOSE
        else:
            return RelationshipLevel.DEVOTED
    
    def calculate_relationship_change(self, npc_name: str, action: str, context: Dict[str, Any]) -> int:
        """Calculate how an action affects relationship with an NPC"""
        npc = self.get_npc(npc_name)
        if not npc:
            return 0
        
        change = 0
        action_lower = action.lower()
        
        # Check likes
        for like in npc.likes:
            if like.lower() in action_lower:
                change += random.randint(2, 5)
        
        # Check dislikes
        for dislike in npc.dislikes:
            if dislike.lower() in action_lower:
                change -= random.randint(2, 5)
        
        # Personality-based modifiers
        if npc.personality == NPCPersonality.FRIENDLY:
            change += 1  # Generally more positive
        elif npc.personality == NPCPersonality.SUSPICIOUS:
            change -= 1  # Generally more negative
        elif npc.personality == NPCPersonality.GRUFF:
            if "respect" in action_lower or "work" in action_lower:
                change += 2
        
        # Context-based modifiers
        if context.get("helped_npc", False):
            change += random.randint(3, 8)
        if context.get("harmed_npc", False):
            change -= random.randint(5, 10)
        if context.get("gift_given", False):
            change += random.randint(1, 4)
        
        return change
