"""
Character Portrait Management System for Me? Reincarnated?
"""
import asyncio
from typing import Optional, Dict, Any, List
from PIL import Image
import config
from api.imagen_client import ImagenClient

class PortraitSystem:
    """Manages character portrait generation, caching, and display"""

    def __init__(self):
        """Initialize the portrait system"""
        self.imagen_client = ImagenClient()
        self.portrait_cache = {}  # In-memory cache for quick access
        self.generation_queue = []  # Queue for portrait generation requests
        self.is_generating = False

    async def get_character_portrait(self, character_data: Dict[str, Any],
                                   evolution_stage: str = "base",
                                   force_regenerate: bool = False) -> Optional[Image.Image]:
        """
        Get character portrait, generating if necessary

        Args:
            character_data: Character information
            evolution_stage: Current evolution stage
            force_regenerate: Force regeneration even if cached

        Returns:
            PIL Image if available, None otherwise
        """
        if not config.ENABLE_PORTRAIT_GENERATION:
            return None

        character_name = character_data.get('name', 'Unknown')
        cache_key = f"{character_name}_{evolution_stage}"

        # Check cache first (unless forcing regeneration)
        if not force_regenerate and config.PORTRAIT_CACHE_ENABLED:
            # Check in-memory cache
            if cache_key in self.portrait_cache:
                return self.portrait_cache[cache_key]

            # Check disk cache
            cached_portrait = self.imagen_client.load_portrait(character_name, evolution_stage)
            if cached_portrait:
                self.portrait_cache[cache_key] = cached_portrait
                return cached_portrait

        # Generate new portrait
        return await self._generate_portrait(character_data, evolution_stage)

    async def _generate_portrait(self, character_data: Dict[str, Any],
                               evolution_stage: str) -> Optional[Image.Image]:
        """Generate a new character portrait"""
        try:
            character_name = character_data.get('name', 'Unknown')
            cache_key = f"{character_name}_{evolution_stage}"

            print(f"Generating portrait for {character_name} ({evolution_stage})...")

            # Try Imagen 3 first if available (higher quality)
            if config.IMAGEN3_ENABLED:
                image_bytes_list = await self.imagen_client.generate_imagen3_portrait(
                    character_data, evolution_stage
                )
                if image_bytes_list and len(image_bytes_list) > 0:
                    image_bytes = image_bytes_list[0]  # Take first generated image
                else:
                    image_bytes = None
            else:
                # Use Gemini 2.0 Flash Image Generation (free tier)
                image_bytes = await self.imagen_client.generate_character_portrait(
                    character_data, evolution_stage
                )

            if image_bytes:
                # Save to disk
                filepath = self.imagen_client.save_portrait(
                    image_bytes, character_name, evolution_stage
                )

                if filepath:
                    # Load and cache the image
                    portrait = self.imagen_client.load_portrait(character_name, evolution_stage)
                    if portrait and config.PORTRAIT_CACHE_ENABLED:
                        self.portrait_cache[cache_key] = portrait

                    print(f"Portrait generated and saved: {filepath}")
                    return portrait

            print(f"Failed to generate portrait for {character_name}")
            return None

        except Exception as e:
            print(f"Error generating portrait: {e}")
            return None

    async def generate_evolution_portraits(self, character_data: Dict[str, Any],
                                         evolution_paths: List[str]) -> Dict[str, Optional[Image.Image]]:
        """
        Generate portraits for all evolution paths

        Args:
            character_data: Character information
            evolution_paths: List of evolution path names

        Returns:
            Dictionary mapping evolution path to portrait image
        """
        portraits = {}

        for evolution_path in evolution_paths:
            # Create modified character data for evolution
            evolved_character = character_data.copy()
            evolved_character['creature_type'] = evolution_path

            portrait = await self.get_character_portrait(
                evolved_character, "evolved"
            )
            portraits[evolution_path] = portrait

        return portraits

    def queue_portrait_generation(self, character_data: Dict[str, Any],
                                evolution_stage: str = "base",
                                priority: int = 1):
        """
        Queue a portrait for background generation

        Args:
            character_data: Character information
            evolution_stage: Evolution stage
            priority: Generation priority (lower = higher priority)
        """
        request = {
            'character_data': character_data,
            'evolution_stage': evolution_stage,
            'priority': priority
        }

        # Insert based on priority
        inserted = False
        for i, queued_request in enumerate(self.generation_queue):
            if priority < queued_request['priority']:
                self.generation_queue.insert(i, request)
                inserted = True
                break

        if not inserted:
            self.generation_queue.append(request)

        # Start processing if not already running
        if not self.is_generating:
            try:
                # Try to create task if event loop is running
                asyncio.create_task(self._process_generation_queue())
            except RuntimeError:
                # No event loop running, will be processed later
                pass

    async def _process_generation_queue(self):
        """Process the portrait generation queue in background"""
        if self.is_generating:
            return

        self.is_generating = True

        try:
            while self.generation_queue:
                request = self.generation_queue.pop(0)

                character_data = request['character_data']
                evolution_stage = request['evolution_stage']

                # Check if already cached
                character_name = character_data.get('name', 'Unknown')
                cache_key = f"{character_name}_{evolution_stage}"

                if cache_key not in self.portrait_cache:
                    await self._generate_portrait(character_data, evolution_stage)

                # Small delay to prevent API rate limiting
                await asyncio.sleep(1)

        finally:
            self.is_generating = False

    async def process_queue_manually(self):
        """Manually process the generation queue (useful for testing)"""
        if not self.is_generating and self.generation_queue:
            await self._process_generation_queue()

    def clear_cache(self):
        """Clear the in-memory portrait cache"""
        self.portrait_cache.clear()
        print("Portrait cache cleared")

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the current cache state"""
        return {
            'cached_portraits': len(self.portrait_cache),
            'queue_length': len(self.generation_queue),
            'is_generating': self.is_generating,
            'cache_enabled': config.PORTRAIT_CACHE_ENABLED,
            'generation_enabled': config.ENABLE_PORTRAIT_GENERATION
        }

    def preload_character_portraits(self, character_data: Dict[str, Any]):
        """
        Preload portraits for character and their evolution paths

        Args:
            character_data: Character information
        """
        if not config.ENABLE_PORTRAIT_GENERATION:
            return

        # Queue base character portrait
        self.queue_portrait_generation(character_data, "base", priority=1)

        # Queue evolution portraits with lower priority
        creature_type = character_data.get('creature_type', '')
        evolution_paths = self._get_evolution_paths(creature_type)

        for evolution_path in evolution_paths:
            evolved_character = character_data.copy()
            evolved_character['creature_type'] = evolution_path
            self.queue_portrait_generation(evolved_character, "evolved", priority=3)

    def _get_evolution_paths(self, creature_type: str) -> List[str]:
        """Get evolution paths for a creature type"""
        # This should match the evolution paths from config.py
        evolution_map = {
            "Slime": ["Elemental Slime", "King Slime", "Mimic Slime"],
            "Spider": ["Arachne", "Widow Spider", "Phase Spider"],
            "Goblin": ["Hobgoblin", "Goblin Shaman", "Goblin King"],
            "Wisp": ["Elemental Wisp", "Guardian Spirit", "Arcane Wisp"],
            "Rat": ["Dire Rat", "Plague Rat", "Shadow Rat"],
            "Mushroom": ["Mycelium Network", "Toxic Mushroom", "Healing Mushroom"]
        }

        return evolution_map.get(creature_type, [])

    async def generate_backstory_illustration(self, backstory_scenario: str) -> Optional[Image.Image]:
        """
        Generate an illustration for a backstory scenario

        Args:
            backstory_scenario: Name of the backstory scenario

        Returns:
            PIL Image if successful, None otherwise
        """
        if not config.ENABLE_PORTRAIT_GENERATION:
            return None

        try:
            # Create a prompt for the backstory illustration
            prompt = self._build_backstory_prompt(backstory_scenario)

            # Generate using Gemini 2.0 Flash with custom prompt
            image_bytes = await self.imagen_client.generate_image_from_prompt(prompt)

            if image_bytes:
                # Save and return the image
                filepath = self.imagen_client.save_portrait(
                    image_bytes, f"backstory_{backstory_scenario}", "illustration"
                )

                if filepath:
                    return self.imagen_client.load_portrait(
                        f"backstory_{backstory_scenario}", "illustration"
                    )

            return None

        except Exception as e:
            print(f"Error generating backstory illustration: {e}")
            return None

    def _build_backstory_prompt(self, scenario_name: str) -> str:
        """Build prompt for backstory illustration"""

        backstory_prompts = {
            "The Overworked Salaryman": "A tired office worker at a desk late at night, city lights outside, dramatic lighting, melancholic atmosphere",
            "The Heroic Sacrifice": "A brave person protecting others from danger, heroic pose, dramatic scene, noble sacrifice",
            "The Tragic Accident": "A moment of unexpected danger, dramatic scene, emotional impact",
            "The Mysterious Illness": "A person in a hospital bed, peaceful but sad atmosphere, soft lighting",
            "The Noble's Downfall": "A once-wealthy person facing their downfall, dramatic contrast, medieval setting"
        }

        base_prompt = backstory_prompts.get(scenario_name,
            "A dramatic scene representing a life-changing moment, emotional atmosphere, cinematic lighting")

        return f"{base_prompt}. High quality digital art, detailed illustration, emotional storytelling, fantasy art style."
