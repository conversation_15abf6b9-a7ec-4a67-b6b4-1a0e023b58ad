"""
Quest system for managing objectives and branching storylines
"""
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import random

class QuestType(Enum):
    MAIN_STORY = "main_story"
    SIDE_QUEST = "side_quest"
    DAILY = "daily"
    EXPLORATION = "exploration"
    COLLECTION = "collection"
    COMBAT = "combat"
    SOCIAL = "social"
    MYSTERY = "mystery"

class QuestStatus(Enum):
    NOT_STARTED = "not_started"
    AVAILABLE = "available"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    ABANDONED = "abandoned"

class ObjectiveType(Enum):
    KILL = "kill"
    COLLECT = "collect"
    TALK_TO = "talk_to"
    VISIT = "visit"
    DELIVER = "deliver"
    LEARN_SKILL = "learn_skill"
    REACH_LEVEL = "reach_level"
    CUSTOM = "custom"

@dataclass
class QuestObjective:
    id: str
    objective_type: ObjectiveType
    description: str
    target: str
    current_progress: int = 0
    required_progress: int = 1
    optional: bool = False
    hidden: bool = False  # Hidden until discovered
    
    def is_complete(self) -> bool:
        return self.current_progress >= self.required_progress
    
    def get_progress_text(self) -> str:
        if self.hidden and self.current_progress == 0:
            return "???"
        return f"{self.current_progress}/{self.required_progress}"

@dataclass
class QuestReward:
    experience: int = 0
    items: List[str] = field(default_factory=list)
    skills: List[str] = field(default_factory=list)
    relationship_changes: Dict[str, int] = field(default_factory=dict)
    world_changes: List[str] = field(default_factory=list)
    unlock_quests: List[str] = field(default_factory=list)
    unlock_locations: List[str] = field(default_factory=list)

@dataclass
class QuestBranch:
    id: str
    name: str
    description: str
    condition: str  # Condition that triggers this branch
    objectives: List[QuestObjective] = field(default_factory=list)
    rewards: QuestReward = field(default_factory=QuestReward)
    next_quests: List[str] = field(default_factory=list)

@dataclass
class Quest:
    id: str
    name: str
    description: str
    quest_type: QuestType
    giver_npc: str
    location: str
    level_requirement: int = 1
    
    # Quest structure
    objectives: List[QuestObjective] = field(default_factory=list)
    branches: List[QuestBranch] = field(default_factory=list)
    current_branch: Optional[str] = None
    
    # Requirements and conditions
    prerequisite_quests: List[str] = field(default_factory=list)
    prerequisite_skills: List[str] = field(default_factory=list)
    prerequisite_relationships: Dict[str, int] = field(default_factory=dict)
    
    # Rewards and consequences
    rewards: QuestReward = field(default_factory=QuestReward)
    failure_consequences: Dict[str, Any] = field(default_factory=dict)
    
    # State tracking
    status: QuestStatus = QuestStatus.NOT_STARTED
    start_time: Optional[int] = None
    completion_time: Optional[int] = None
    failure_reason: Optional[str] = None
    
    # Narrative elements
    journal_entries: List[str] = field(default_factory=list)
    dialogue_flags: Dict[str, bool] = field(default_factory=dict)
    
    def is_available(self, character, memory_system) -> bool:
        """Check if quest is available to start"""
        if self.status != QuestStatus.NOT_STARTED:
            return False
        
        # Check level requirement
        if character.level < self.level_requirement:
            return False
        
        # Check prerequisite quests
        completed_quests = memory_system.long_term_memory.get("completed_quests", [])
        completed_quest_names = [q["name"] for q in completed_quests]
        
        for prereq in self.prerequisite_quests:
            if prereq not in completed_quest_names:
                return False
        
        # Check prerequisite skills
        for skill in self.prerequisite_skills:
            if skill not in character.abilities:
                return False
        
        # Check prerequisite relationships
        met_npcs = memory_system.long_term_memory.get("met_npcs", {})
        for npc_name, required_relationship in self.prerequisite_relationships.items():
            npc_data = met_npcs.get(npc_name, {})
            current_relationship = npc_data.get("relationship", 0)
            if current_relationship < required_relationship:
                return False
        
        return True
    
    def get_active_objectives(self) -> List[QuestObjective]:
        """Get currently active objectives"""
        if self.current_branch:
            # Get objectives from current branch
            for branch in self.branches:
                if branch.id == self.current_branch:
                    return [obj for obj in branch.objectives if not obj.is_complete()]
        
        # Get main quest objectives
        return [obj for obj in self.objectives if not obj.is_complete()]
    
    def get_progress_summary(self) -> str:
        """Get a summary of quest progress"""
        active_objectives = self.get_active_objectives()
        if not active_objectives:
            return "Quest completed!"
        
        summary_lines = []
        for obj in active_objectives:
            if not obj.hidden or obj.current_progress > 0:
                summary_lines.append(f"• {obj.description} ({obj.get_progress_text()})")
        
        return "\n".join(summary_lines) if summary_lines else "Continue your journey..."

class QuestSystem:
    """Manages all quests in the game"""
    
    def __init__(self):
        self.quest_database = self._initialize_quest_database()
        self.active_quests = {}  # quest_id -> Quest
        self.completed_quests = {}  # quest_id -> Quest
        self.failed_quests = {}  # quest_id -> Quest
        
    def _initialize_quest_database(self) -> Dict[str, Quest]:
        """Initialize all quests in the game"""
        quests = {}
        
        # Lost Traveler Quests
        quests["map_mysterious_forest"] = Quest(
            id="map_mysterious_forest",
            name="Map the Mysterious Forest",
            description="Help Marcus the Lost Traveler create a complete map of the Mysterious Forest to find a way out.",
            quest_type=QuestType.EXPLORATION,
            giver_npc="Lost Traveler",
            location="Mysterious Forest",
            level_requirement=1,
            objectives=[
                QuestObjective(
                    id="explore_forest_areas",
                    objective_type=ObjectiveType.VISIT,
                    description="Explore different areas of the Mysterious Forest",
                    target="forest_exploration",
                    required_progress=3
                ),
                QuestObjective(
                    id="find_landmarks",
                    objective_type=ObjectiveType.COLLECT,
                    description="Find and document forest landmarks",
                    target="forest_landmarks",
                    required_progress=2
                )
            ],
            rewards=QuestReward(
                experience=100,
                skills=["Navigation", "Forest Knowledge"],
                relationship_changes={"Lost Traveler": 15}
            ),
            branches=[
                QuestBranch(
                    id="help_escape",
                    name="Help Marcus Escape",
                    description="Guide Marcus to safety outside the forest",
                    condition="found_forest_exit",
                    objectives=[
                        QuestObjective(
                            id="escort_marcus",
                            objective_type=ObjectiveType.CUSTOM,
                            description="Safely escort Marcus to the forest exit",
                            target="escort_complete",
                            required_progress=1
                        )
                    ],
                    rewards=QuestReward(
                        experience=50,
                        items=["Traveler's Compass"],
                        relationship_changes={"Lost Traveler": 25}
                    ),
                    next_quests=["deliver_message_to_family"]
                ),
                QuestBranch(
                    id="stay_and_help",
                    name="Marcus Stays to Help",
                    description="Convince Marcus to stay and help other lost travelers",
                    condition="high_relationship_marcus",
                    objectives=[
                        QuestObjective(
                            id="establish_waystation",
                            objective_type=ObjectiveType.CUSTOM,
                            description="Help Marcus establish a waystation for lost travelers",
                            target="waystation_built",
                            required_progress=1
                        )
                    ],
                    rewards=QuestReward(
                        experience=75,
                        skills=["Leadership"],
                        world_changes=["Marcus becomes permanent forest guide"],
                        unlock_locations=["Traveler's Waystation"]
                    )
                )
            ]
        )
        
        quests["deliver_message_to_family"] = Quest(
            id="deliver_message_to_family",
            name="Deliver Message to Family",
            description="Deliver Marcus's message to his family in the nearby village.",
            quest_type=QuestType.SIDE_QUEST,
            giver_npc="Lost Traveler",
            location="Mysterious Forest",
            level_requirement=3,
            prerequisite_quests=["map_mysterious_forest"],
            objectives=[
                QuestObjective(
                    id="find_village",
                    objective_type=ObjectiveType.VISIT,
                    description="Find the village where Marcus's family lives",
                    target="marcus_village",
                    required_progress=1
                ),
                QuestObjective(
                    id="deliver_message",
                    objective_type=ObjectiveType.TALK_TO,
                    description="Deliver the message to Marcus's family",
                    target="Marcus's Family",
                    required_progress=1
                )
            ],
            rewards=QuestReward(
                experience=75,
                items=["Family Heirloom"],
                relationship_changes={"Lost Traveler": 10}
            )
        )
        
        # Dwarf Miner Quests
        quests["clear_blocked_tunnel"] = Quest(
            id="clear_blocked_tunnel",
            name="Clear the Blocked Tunnel",
            description="Help Thorek clear a tunnel that's been blocked by a cave-in.",
            quest_type=QuestType.COMBAT,
            giver_npc="Dwarf Miner",
            location="Crystal Caves",
            level_requirement=4,
            objectives=[
                QuestObjective(
                    id="defeat_cave_spiders",
                    objective_type=ObjectiveType.KILL,
                    description="Defeat the cave spiders that moved into the blocked tunnel",
                    target="Cave Spider",
                    required_progress=3
                ),
                QuestObjective(
                    id="clear_rubble",
                    objective_type=ObjectiveType.CUSTOM,
                    description="Help clear the rubble blocking the tunnel",
                    target="rubble_cleared",
                    required_progress=1
                )
            ],
            rewards=QuestReward(
                experience=120,
                items=["Dwarven Pickaxe", "Crystal Samples"],
                skills=["Mining"],
                relationship_changes={"Dwarf Miner": 20}
            )
        )
        
        # Elder Sprite Quests
        quests["restore_ancient_shrine"] = Quest(
            id="restore_ancient_shrine",
            name="Restore the Ancient Shrine",
            description="Help the Elder Sprite restore an ancient shrine deep in the forest.",
            quest_type=QuestType.MYSTERY,
            giver_npc="Elder Sprite",
            location="Mysterious Forest",
            level_requirement=5,
            prerequisite_skills=["Nature Magic"],
            prerequisite_relationships={"Elder Sprite": 25},
            objectives=[
                QuestObjective(
                    id="find_shrine_pieces",
                    objective_type=ObjectiveType.COLLECT,
                    description="Find the scattered pieces of the ancient shrine",
                    target="shrine_fragments",
                    required_progress=4
                ),
                QuestObjective(
                    id="purify_corruption",
                    objective_type=ObjectiveType.CUSTOM,
                    description="Use nature magic to purify the corruption around the shrine",
                    target="corruption_purified",
                    required_progress=1
                )
            ],
            rewards=QuestReward(
                experience=200,
                skills=["Ancient Magic", "Purification"],
                relationship_changes={"Elder Sprite": 30},
                world_changes=["Ancient shrine restored", "Forest spirits return"]
            )
        )
        
        # Fairy Queen Quests
        quests["protect_fairy_ring"] = Quest(
            id="protect_fairy_ring",
            name="Protect the Fairy Ring",
            description="Defend the sacred fairy ring from dark creatures that threaten it.",
            quest_type=QuestType.COMBAT,
            giver_npc="Fairy Queen",
            location="Moonlit Meadow",
            level_requirement=6,
            objectives=[
                QuestObjective(
                    id="investigate_disturbance",
                    objective_type=ObjectiveType.VISIT,
                    description="Investigate the source of the disturbance near the fairy ring",
                    target="disturbance_source",
                    required_progress=1
                ),
                QuestObjective(
                    id="defeat_shadow_creatures",
                    objective_type=ObjectiveType.KILL,
                    description="Defeat the shadow creatures threatening the fairy ring",
                    target="Shadow Creature",
                    required_progress=5
                )
            ],
            rewards=QuestReward(
                experience=150,
                skills=["Light Magic", "Fairy Blessing"],
                items=["Moonstone Pendant"],
                relationship_changes={"Fairy Queen": 25}
            ),
            branches=[
                QuestBranch(
                    id="purify_source",
                    name="Purify the Corruption Source",
                    description="Purify the source of corruption instead of just fighting symptoms",
                    condition="has_purification_magic",
                    objectives=[
                        QuestObjective(
                            id="purify_dark_crystal",
                            objective_type=ObjectiveType.CUSTOM,
                            description="Purify the corrupted crystal causing the disturbance",
                            target="crystal_purified",
                            required_progress=1
                        )
                    ],
                    rewards=QuestReward(
                        experience=100,
                        skills=["Master Purification"],
                        world_changes=["Corruption source eliminated permanently"]
                    )
                )
            ]
        )
        
        return quests
    
    def get_quest(self, quest_id: str) -> Optional[Quest]:
        """Get quest by ID from database"""
        return self.quest_database.get(quest_id)
    
    def get_available_quests(self, character, memory_system, location: str = None) -> List[Quest]:
        """Get all quests available to start"""
        available = []
        
        for quest in self.quest_database.values():
            if quest.is_available(character, memory_system):
                if location is None or quest.location == location:
                    available.append(quest)
        
        return available
    
    def start_quest(self, quest_id: str, character, memory_system) -> bool:
        """Start a quest"""
        quest = self.get_quest(quest_id)
        if not quest or not quest.is_available(character, memory_system):
            return False
        
        quest.status = QuestStatus.ACTIVE
        quest.start_time = memory_system.playtime if hasattr(memory_system, 'playtime') else 0
        self.active_quests[quest_id] = quest
        
        # Add initial journal entry
        quest.journal_entries.append(f"Started quest: {quest.name}")
        
        return True
    
    def update_quest_progress(self, quest_id: str, objective_id: str, progress: int = 1) -> bool:
        """Update progress on a quest objective"""
        if quest_id not in self.active_quests:
            return False
        
        quest = self.active_quests[quest_id]
        
        # Find and update objective
        for objective in quest.objectives:
            if objective.id == objective_id:
                objective.current_progress = min(
                    objective.current_progress + progress,
                    objective.required_progress
                )
                return True
        
        # Check branch objectives
        if quest.current_branch:
            for branch in quest.branches:
                if branch.id == quest.current_branch:
                    for objective in branch.objectives:
                        if objective.id == objective_id:
                            objective.current_progress = min(
                                objective.current_progress + progress,
                                objective.required_progress
                            )
                            return True
        
        return False
    
    def check_quest_completion(self, quest_id: str) -> bool:
        """Check if a quest is completed"""
        if quest_id not in self.active_quests:
            return False
        
        quest = self.active_quests[quest_id]
        
        # Check if all main objectives are complete
        main_complete = all(obj.is_complete() or obj.optional for obj in quest.objectives)
        
        # Check branch objectives if in a branch
        branch_complete = True
        if quest.current_branch:
            for branch in quest.branches:
                if branch.id == quest.current_branch:
                    branch_complete = all(obj.is_complete() or obj.optional for obj in branch.objectives)
                    break
        
        return main_complete and branch_complete
    
    def complete_quest(self, quest_id: str, character, memory_system) -> QuestReward:
        """Complete a quest and apply rewards"""
        if quest_id not in self.active_quests:
            return QuestReward()
        
        quest = self.active_quests[quest_id]
        quest.status = QuestStatus.COMPLETED
        quest.completion_time = memory_system.playtime if hasattr(memory_system, 'playtime') else 0
        
        # Move to completed quests
        self.completed_quests[quest_id] = quest
        del self.active_quests[quest_id]
        
        # Record in memory system
        memory_system.complete_quest(quest.name, "Successfully completed")
        
        # Get total rewards (main + branch if applicable)
        total_rewards = quest.rewards
        if quest.current_branch:
            for branch in quest.branches:
                if branch.id == quest.current_branch:
                    # Combine rewards
                    total_rewards.experience += branch.rewards.experience
                    total_rewards.items.extend(branch.rewards.items)
                    total_rewards.skills.extend(branch.rewards.skills)
                    for npc, change in branch.rewards.relationship_changes.items():
                        total_rewards.relationship_changes[npc] = total_rewards.relationship_changes.get(npc, 0) + change
                    total_rewards.world_changes.extend(branch.rewards.world_changes)
                    total_rewards.unlock_quests.extend(branch.rewards.unlock_quests)
                    total_rewards.unlock_locations.extend(branch.rewards.unlock_locations)
                    break
        
        return total_rewards
    
    def get_active_quests_summary(self) -> Dict[str, str]:
        """Get summary of all active quests"""
        summaries = {}
        for quest_id, quest in self.active_quests.items():
            summaries[quest.name] = quest.get_progress_summary()
        return summaries
    
    def trigger_quest_branch(self, quest_id: str, branch_condition: str) -> bool:
        """Trigger a quest branch based on condition"""
        if quest_id not in self.active_quests:
            return False
        
        quest = self.active_quests[quest_id]
        
        for branch in quest.branches:
            if branch.condition == branch_condition:
                quest.current_branch = branch.id
                quest.journal_entries.append(f"Quest branched: {branch.name}")
                return True
        
        return False
