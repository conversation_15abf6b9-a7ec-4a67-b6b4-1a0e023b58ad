"""
Reward Skill System for Me? Reincarnated?
Automatically grants skills based on story progression, achievements, and milestones.
"""
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
from .skill_system import Skill, SkillType, SkillRarity

class RewardCategory(Enum):
    STORY_PROGRESSION = "story_progression"
    QUEST_COMPLETION = "quest_completion"
    RELATIONSHIP_GROWTH = "relationship_growth"
    ACHIEVEMENT_BASED = "achievement_based"

@dataclass
class RewardCriteria:
    """Defines the criteria for earning a reward skill"""
    category: RewardCategory
    skill_name: str
    description: str
    criteria_type: str  # "counter", "milestone", "collection", "relationship"
    target_value: Any  # The value that must be reached
    tracking_key: str  # What to track (e.g., "combat_victories", "days_survived")
    additional_requirements: Dict[str, Any] = None  # Optional extra requirements

class RewardSkillSystem:
    """Manages automatic skill rewards based on player achievements and milestones"""

    def __init__(self):
        self.reward_criteria = self._initialize_reward_criteria()
        self.reward_skills = self._initialize_reward_skills()
        self.player_progress = self._initialize_progress_tracking()

    def _initialize_reward_criteria(self) -> Dict[str, RewardCriteria]:
        """Define all reward skill criteria"""
        criteria = {}

        # Story Progression Skills
        criteria["First Evolution"] = RewardCriteria(
            RewardCategory.STORY_PROGRESSION,
            "First Evolution",
            "Unlocked after evolving for the first time",
            "milestone",
            1,
            "evolution_count"
        )

        criteria["Survivor"] = RewardCriteria(
            RewardCategory.STORY_PROGRESSION,
            "Survivor",
            "Unlocked after surviving 10 days",
            "counter",
            10,
            "days_survived"
        )

        criteria["Veteran Survivor"] = RewardCriteria(
            RewardCategory.STORY_PROGRESSION,
            "Veteran Survivor",
            "Unlocked after surviving 30 days",
            "counter",
            30,
            "days_survived"
        )

        criteria["Apex Predator"] = RewardCriteria(
            RewardCategory.STORY_PROGRESSION,
            "Apex Predator",
            "Unlocked after reaching level 20",
            "counter",
            20,
            "character_level"
        )

        # Quest Completion Skills
        criteria["Monster Hunter"] = RewardCriteria(
            RewardCategory.QUEST_COMPLETION,
            "Monster Hunter",
            "Unlocked after defeating 5 different creature types",
            "collection",
            5,
            "unique_enemies_defeated"
        )

        criteria["Explorer"] = RewardCriteria(
            RewardCategory.QUEST_COMPLETION,
            "Explorer",
            "Unlocked after discovering 3 new areas",
            "collection",
            3,
            "discovered_locations"
        )

        criteria["Dungeon Delver"] = RewardCriteria(
            RewardCategory.QUEST_COMPLETION,
            "Dungeon Delver",
            "Unlocked after completing 5 quests",
            "counter",
            5,
            "completed_quests"
        )

        criteria["Champion"] = RewardCriteria(
            RewardCategory.QUEST_COMPLETION,
            "Champion",
            "Unlocked after winning 25 combat encounters",
            "counter",
            25,
            "combat_victories"
        )

        # Relationship Growth Skills
        criteria["Trusted Ally"] = RewardCriteria(
            RewardCategory.RELATIONSHIP_GROWTH,
            "Trusted Ally",
            "Unlocked after gaining high reputation with an NPC (75+)",
            "relationship",
            75,
            "max_npc_relationship"
        )

        criteria["Pack Leader"] = RewardCriteria(
            RewardCategory.RELATIONSHIP_GROWTH,
            "Pack Leader",
            "Unlocked after leading a group successfully",
            "milestone",
            1,
            "leadership_events",
            {"min_group_size": 3}
        )

        criteria["Diplomat"] = RewardCriteria(
            RewardCategory.RELATIONSHIP_GROWTH,
            "Diplomat",
            "Unlocked after maintaining positive relationships with 5 NPCs",
            "counter",
            5,
            "positive_relationships"
        )

        # Achievement-Based Skills
        criteria["Glutton"] = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Glutton",
            "Unlocked after eating 50 different food items",
            "collection",
            50,
            "unique_foods_eaten"
        )

        criteria["Hoarder"] = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Hoarder",
            "Unlocked after collecting 100 items",
            "counter",
            100,
            "total_items_collected"
        )

        criteria["Scholar"] = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Scholar",
            "Unlocked after learning 20 different skills",
            "counter",
            20,
            "total_skills_learned"
        )

        criteria["Fusion Master"] = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Fusion Master",
            "Unlocked after performing 10 skill fusions",
            "counter",
            10,
            "skill_fusions_performed"
        )

        criteria["Treasure Hunter"] = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Treasure Hunter",
            "Unlocked after finding 15 rare or legendary items",
            "counter",
            15,
            "rare_items_found"
        )

        return criteria

    def _initialize_reward_skills(self) -> Dict[str, Skill]:
        """Define all reward skills"""
        skills = {}

        # Story Progression Skills
        skills["First Evolution"] = Skill(
            "First Evolution",
            "Commemorates your first transformation. +10% evolution speed for future evolutions",
            SkillType.PASSIVE,
            SkillRarity.RARE,
            0, 0,
            ["evolution_speed_boost"],
            {}
        )

        skills["Survivor"] = Skill(
            "Survivor",
            "Hardy constitution from surviving harsh conditions. +15% resistance to environmental damage",
            SkillType.RESISTANCE,
            SkillRarity.BASIC,
            0, 0,
            ["environmental_resistance"],
            {}
        )

        skills["Veteran Survivor"] = Skill(
            "Veteran Survivor",
            "Exceptional endurance from long survival. +25% HP and +20% environmental resistance",
            SkillType.PASSIVE,
            SkillRarity.ADVANCED,
            0, 0,
            ["hp_boost", "environmental_resistance"],
            {}
        )

        skills["Apex Predator"] = Skill(
            "Apex Predator",
            "Dominance over lesser creatures. +20% damage against lower-level enemies",
            SkillType.COMBAT,
            SkillRarity.RARE,
            0, 0,
            ["level_damage_bonus"],
            {}
        )

        # Quest Completion Skills
        skills["Monster Hunter"] = Skill(
            "Monster Hunter",
            "Experience hunting diverse creatures. +15% critical hit chance against new enemy types",
            SkillType.COMBAT,
            SkillRarity.ADVANCED,
            0, 0,
            ["new_enemy_crit_bonus"],
            {}
        )

        skills["Explorer"] = Skill(
            "Explorer",
            "Keen sense for discovery. +25% chance to find hidden areas and secrets",
            SkillType.UTILITY,
            SkillRarity.ADVANCED,
            0, 0,
            ["discovery_bonus"],
            {}
        )

        skills["Dungeon Delver"] = Skill(
            "Dungeon Delver",
            "Expertise in dangerous places. +20% damage and defense in enclosed areas",
            SkillType.COMBAT,
            SkillRarity.ADVANCED,
            0, 0,
            ["dungeon_combat_bonus"],
            {}
        )

        skills["Champion"] = Skill(
            "Champion",
            "Proven warrior's prowess. +10% damage and +15% critical hit chance",
            SkillType.COMBAT,
            SkillRarity.RARE,
            0, 0,
            ["combat_mastery"],
            {}
        )

        # Relationship Growth Skills
        skills["Trusted Ally"] = Skill(
            "Trusted Ally",
            "Deep bonds with others. NPCs are 30% more likely to offer help and information",
            SkillType.UTILITY,
            SkillRarity.ADVANCED,
            0, 0,
            ["npc_favor_bonus"],
            {}
        )

        skills["Pack Leader"] = Skill(
            "Pack Leader",
            "Natural leadership abilities. +20% effectiveness when fighting alongside allies",
            SkillType.COMBAT,
            SkillRarity.RARE,
            0, 0,
            ["group_combat_bonus"],
            {}
        )

        skills["Diplomat"] = Skill(
            "Diplomat",
            "Skilled in negotiation. 25% chance to avoid combat through dialogue",
            SkillType.UTILITY,
            SkillRarity.ADVANCED,
            0, 0,
            ["diplomatic_immunity"],
            {}
        )

        # Achievement-Based Skills
        skills["Glutton"] = Skill(
            "Glutton",
            "Diverse palate grants knowledge. +20% chance to learn skills from consumed creatures",
            SkillType.UTILITY,
            SkillRarity.ADVANCED,
            0, 0,
            ["consumption_learning_bonus"],
            {}
        )

        skills["Hoarder"] = Skill(
            "Hoarder",
            "Obsessive collecting pays off. +30% chance to find additional loot",
            SkillType.UTILITY,
            SkillRarity.ADVANCED,
            0, 0,
            ["loot_bonus"],
            {}
        )

        skills["Scholar"] = Skill(
            "Scholar",
            "Vast knowledge accelerates learning. -25% requirements for learning new skills",
            SkillType.UTILITY,
            SkillRarity.RARE,
            0, 0,
            ["learning_speed_bonus"],
            {}
        )

        skills["Fusion Master"] = Skill(
            "Fusion Master",
            "Mastery of skill combination. Unlock advanced fusion recipes",
            SkillType.UTILITY,
            SkillRarity.LEGENDARY,
            0, 0,
            ["advanced_fusion_unlock"],
            {}
        )

        skills["Treasure Hunter"] = Skill(
            "Treasure Hunter",
            "Eye for valuable items. +50% chance to identify rare item properties",
            SkillType.UTILITY,
            SkillRarity.RARE,
            0, 0,
            ["item_identification_bonus"],
            {}
        )

        return skills

    def _initialize_progress_tracking(self) -> Dict[str, Any]:
        """Initialize progress tracking for all criteria"""
        return {
            "evolution_count": 0,
            "days_survived": 0,
            "character_level": 1,
            "unique_enemies_defeated": set(),
            "discovered_locations": set(),
            "completed_quests": 0,
            "combat_victories": 0,
            "max_npc_relationship": 0,
            "leadership_events": 0,
            "positive_relationships": 0,
            "unique_foods_eaten": set(),
            "total_items_collected": 0,
            "total_skills_learned": 0,
            "skill_fusions_performed": 0,
            "rare_items_found": 0,
            "earned_rewards": set()  # Track which rewards have been earned
        }

    def update_progress(self, tracking_key: str, value: Any, operation: str = "set") -> List[str]:
        """Update progress tracking and check for newly earned rewards"""
        newly_earned = []

        # Update the tracking value
        if operation == "set":
            self.player_progress[tracking_key] = value
        elif operation == "increment":
            if tracking_key not in self.player_progress:
                self.player_progress[tracking_key] = 0
            self.player_progress[tracking_key] += value
        elif operation == "add_to_set":
            if tracking_key not in self.player_progress:
                self.player_progress[tracking_key] = set()
            if isinstance(self.player_progress[tracking_key], set):
                self.player_progress[tracking_key].add(value)
        elif operation == "max":
            current = self.player_progress.get(tracking_key, 0)
            self.player_progress[tracking_key] = max(current, value)

        # Check all criteria for newly met conditions
        for criteria_name, criteria in self.reward_criteria.items():
            if criteria_name in self.player_progress["earned_rewards"]:
                continue  # Already earned

            if self._check_criteria_met(criteria):
                self.player_progress["earned_rewards"].add(criteria_name)
                newly_earned.append(criteria.skill_name)

        return newly_earned

    def _check_criteria_met(self, criteria: RewardCriteria) -> bool:
        """Check if specific criteria has been met"""
        tracking_value = self.player_progress.get(criteria.tracking_key)

        if tracking_value is None:
            return False

        if criteria.criteria_type == "counter":
            return tracking_value >= criteria.target_value
        elif criteria.criteria_type == "milestone":
            return tracking_value >= criteria.target_value
        elif criteria.criteria_type == "collection":
            if isinstance(tracking_value, set):
                return len(tracking_value) >= criteria.target_value
            else:
                return tracking_value >= criteria.target_value
        elif criteria.criteria_type == "relationship":
            return tracking_value >= criteria.target_value

        # Check additional requirements if they exist
        if criteria.additional_requirements:
            for req_key, req_value in criteria.additional_requirements.items():
                if self.player_progress.get(req_key, 0) < req_value:
                    return False

        return False

    def get_reward_skill(self, skill_name: str) -> Optional[Skill]:
        """Get a reward skill by name"""
        return self.reward_skills.get(skill_name)

    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a summary of current progress towards all rewards"""
        summary = {}

        for criteria_name, criteria in self.reward_criteria.items():
            if criteria_name in self.player_progress["earned_rewards"]:
                summary[criteria_name] = {
                    "status": "earned",
                    "skill_name": criteria.skill_name,
                    "description": criteria.description
                }
            else:
                current_value = self.player_progress.get(criteria.tracking_key, 0)
                if isinstance(current_value, set):
                    current_value = len(current_value)

                progress_percent = min(100, (current_value / criteria.target_value) * 100)

                summary[criteria_name] = {
                    "status": "in_progress",
                    "skill_name": criteria.skill_name,
                    "description": criteria.description,
                    "current": current_value,
                    "target": criteria.target_value,
                    "progress_percent": progress_percent
                }

        return summary

    def get_earned_rewards(self) -> List[str]:
        """Get list of all earned reward skill names"""
        return [self.reward_criteria[reward_name].skill_name
                for reward_name in self.player_progress["earned_rewards"]]

    def load_progress(self, progress_data: Dict[str, Any]):
        """Load progress from save data"""
        for key, value in progress_data.items():
            if key in ["unique_enemies_defeated", "discovered_locations",
                      "unique_foods_eaten", "earned_rewards"]:
                # Convert lists back to sets for set-based tracking
                if isinstance(value, list):
                    self.player_progress[key] = set(value)
                else:
                    self.player_progress[key] = value
            else:
                self.player_progress[key] = value

    def save_progress(self) -> Dict[str, Any]:
        """Save progress to dict for serialization"""
        save_data = {}
        for key, value in self.player_progress.items():
            if isinstance(value, set):
                # Convert sets to lists for JSON serialization
                save_data[key] = list(value)
            else:
                save_data[key] = value
        return save_data

    def get_fusion_combinations_with_rewards(self) -> Dict[str, Dict[str, Any]]:
        """Get fusion combinations that include reward skills"""
        fusion_combinations = {}

        # Advanced fusion combinations involving reward skills
        fusion_combinations["Ultimate Survivor"] = {
            "components": ["Survivor", "Veteran Survivor", "Apex Predator"],
            "result_skill": "Ultimate Survivor",
            "fusion_type": "legendary_reward"
        }

        fusion_combinations["Master Explorer"] = {
            "components": ["Explorer", "Treasure Hunter", "Scholar"],
            "result_skill": "Master Explorer",
            "fusion_type": "legendary_reward"
        }

        fusion_combinations["War Chief"] = {
            "components": ["Champion", "Pack Leader", "Trusted Ally"],
            "result_skill": "War Chief",
            "fusion_type": "legendary_reward"
        }

        fusion_combinations["Legendary Collector"] = {
            "components": ["Hoarder", "Glutton", "Fusion Master"],
            "result_skill": "Legendary Collector",
            "fusion_type": "legendary_reward"
        }

        return fusion_combinations

    def get_legendary_reward_skills(self) -> Dict[str, Skill]:
        """Get legendary skills created from reward skill fusions"""
        legendary_skills = {}

        legendary_skills["Ultimate Survivor"] = Skill(
            "Ultimate Survivor",
            "Legendary endurance and dominance. +50% HP, +30% environmental resistance, +25% damage vs lower-level enemies",
            SkillType.PASSIVE,
            SkillRarity.LEGENDARY,
            0, 0,
            ["legendary_survival_mastery"],
            {},
            ["Survivor", "Veteran Survivor", "Apex Predator"]
        )

        legendary_skills["Master Explorer"] = Skill(
            "Master Explorer",
            "Unparalleled discovery abilities. +50% discovery chance, +75% item identification, -50% learning requirements",
            SkillType.UTILITY,
            SkillRarity.LEGENDARY,
            0, 0,
            ["legendary_exploration_mastery"],
            {},
            ["Explorer", "Treasure Hunter", "Scholar"]
        )

        legendary_skills["War Chief"] = Skill(
            "War Chief",
            "Supreme combat leadership. +30% damage, +25% crit chance, +40% group combat bonus, diplomatic immunity",
            SkillType.COMBAT,
            SkillRarity.LEGENDARY,
            0, 0,
            ["legendary_leadership_mastery"],
            {},
            ["Champion", "Pack Leader", "Trusted Ally"]
        )

        legendary_skills["Legendary Collector"] = Skill(
            "Legendary Collector",
            "Master of acquisition and fusion. +75% loot bonus, +40% consumption learning, unlock all fusion recipes",
            SkillType.UTILITY,
            SkillRarity.LEGENDARY,
            0, 0,
            ["legendary_collection_mastery"],
            {},
            ["Hoarder", "Glutton", "Fusion Master"]
        )

        return legendary_skills
