"""
Save and load system for game persistence
"""
import json
import os
from datetime import datetime
from typing import Optional, Dict, Any
import config

class SaveSystem:
    def __init__(self):
        """Initialize the save system"""
        self.saves_dir = config.SAVES_DIR
        self.auto_save_file = self.saves_dir / "autosave.json"
        
    def save_game(self, game_state: Dict[str, Any], save_name: Optional[str] = None) -> bool:
        """
        Save the current game state
        
        Args:
            game_state: Complete game state dictionary
            save_name: Optional custom save name, uses timestamp if None
            
        Returns:
            True if save successful, False otherwise
        """
        try:
            if save_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_name = f"save_{timestamp}"
            
            # Add metadata
            save_data = {
                "metadata": {
                    "save_name": save_name,
                    "timestamp": datetime.now().isoformat(),
                    "game_version": config.GAME_VERSION,
                    "character_name": game_state.get("character", {}).get("name", "Unknown"),
                    "character_level": game_state.get("character", {}).get("level", 1),
                    "playtime": game_state.get("playtime", 0)
                },
                "game_state": game_state
            }
            
            save_file = self.saves_dir / f"{save_name}.json"
            with open(save_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"Game saved as: {save_name}")
            return True
            
        except Exception as e:
            print(f"Error saving game: {e}")
            return False
    
    def load_game(self, save_name: str) -> Optional[Dict[str, Any]]:
        """
        Load a saved game
        
        Args:
            save_name: Name of the save file (without .json extension)
            
        Returns:
            Game state dictionary if successful, None otherwise
        """
        try:
            save_file = self.saves_dir / f"{save_name}.json"
            if not save_file.exists():
                print(f"Save file not found: {save_name}")
                return None
            
            with open(save_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)
            
            # Validate save data
            if "game_state" not in save_data:
                print("Invalid save file format")
                return None
            
            print(f"Game loaded: {save_name}")
            return save_data["game_state"]
            
        except Exception as e:
            print(f"Error loading game: {e}")
            return None
    
    def auto_save(self, game_state: Dict[str, Any]) -> bool:
        """
        Perform an automatic save
        
        Args:
            game_state: Complete game state dictionary
            
        Returns:
            True if save successful, False otherwise
        """
        try:
            save_data = {
                "metadata": {
                    "save_type": "autosave",
                    "timestamp": datetime.now().isoformat(),
                    "game_version": config.GAME_VERSION,
                    "character_name": game_state.get("character", {}).get("name", "Unknown"),
                    "character_level": game_state.get("character", {}).get("level", 1)
                },
                "game_state": game_state
            }
            
            with open(self.auto_save_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error auto-saving: {e}")
            return False
    
    def load_auto_save(self) -> Optional[Dict[str, Any]]:
        """
        Load the automatic save file
        
        Returns:
            Game state dictionary if successful, None otherwise
        """
        if not self.auto_save_file.exists():
            return None
        
        try:
            with open(self.auto_save_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)
            
            if "game_state" not in save_data:
                return None
            
            return save_data["game_state"]
            
        except Exception as e:
            print(f"Error loading auto-save: {e}")
            return None
    
    def list_saves(self) -> list:
        """
        Get a list of all available save files
        
        Returns:
            List of dictionaries containing save file information
        """
        saves = []
        
        try:
            for save_file in self.saves_dir.glob("*.json"):
                if save_file.name == "autosave.json":
                    continue
                
                try:
                    with open(save_file, 'r', encoding='utf-8') as f:
                        save_data = json.load(f)
                    
                    metadata = save_data.get("metadata", {})
                    saves.append({
                        "filename": save_file.stem,
                        "display_name": metadata.get("save_name", save_file.stem),
                        "timestamp": metadata.get("timestamp", "Unknown"),
                        "character_name": metadata.get("character_name", "Unknown"),
                        "character_level": metadata.get("character_level", 1),
                        "playtime": metadata.get("playtime", 0)
                    })
                    
                except Exception as e:
                    print(f"Error reading save file {save_file}: {e}")
                    continue
            
            # Sort by timestamp (newest first)
            saves.sort(key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            print(f"Error listing saves: {e}")
        
        return saves
    
    def delete_save(self, save_name: str) -> bool:
        """
        Delete a save file
        
        Args:
            save_name: Name of the save file to delete
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            save_file = self.saves_dir / f"{save_name}.json"
            if save_file.exists():
                save_file.unlink()
                print(f"Save deleted: {save_name}")
                return True
            else:
                print(f"Save file not found: {save_name}")
                return False
                
        except Exception as e:
            print(f"Error deleting save: {e}")
            return False
