"""
Skill and Fusion System for Me? Reincarnated?
"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class SkillType(Enum):
    COMBAT = "combat"
    MAGIC = "magic"
    UTILITY = "utility"
    RESISTANCE = "resistance"
    PASSIVE = "passive"
    REWARD = "reward"  # New type for reward skills

class SkillRarity(Enum):
    BASIC = "basic"
    ADVANCED = "advanced"
    RARE = "rare"
    LEGENDARY = "legendary"
    FUSED = "fused"

@dataclass
class Skill:
    """Represents a skill or ability"""
    name: str
    description: str
    skill_type: SkillType
    rarity: SkillRarity
    mp_cost: int
    power: int
    effects: List[str]
    requirements: Dict[str, Any]
    fusion_components: List[str] = None  # For fused skills

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "skill_type": self.skill_type.value,
            "rarity": self.rarity.value,
            "mp_cost": self.mp_cost,
            "power": self.power,
            "effects": self.effects,
            "requirements": self.requirements,
            "fusion_components": self.fusion_components or []
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Skill':
        return cls(
            name=data["name"],
            description=data["description"],
            skill_type=SkillType(data["skill_type"]),
            rarity=SkillRarity(data["rarity"]),
            mp_cost=data["mp_cost"],
            power=data["power"],
            effects=data["effects"],
            requirements=data["requirements"],
            fusion_components=data.get("fusion_components", [])
        )

class SkillFusionSystem:
    """Manages skill combinations and fusion logic"""

    def __init__(self):
        self.fusion_recipes = self._initialize_fusion_recipes()
        self.skill_database = self._initialize_skill_database()
        self._add_reward_skills_to_database()

    def _initialize_fusion_recipes(self) -> Dict[str, Dict[str, Any]]:
        """Define all possible skill fusion combinations"""
        return {
            # Elemental Fusions
            "Temperature Regulation": {
                "components": ["Heat Resistance", "Cold Resistance"],
                "result_skill": "Temperature Regulation",
                "fusion_type": "elemental"
            },
            "Steam Mastery": {
                "components": ["Fire Magic", "Water Magic"],
                "result_skill": "Steam Mastery",
                "fusion_type": "elemental"
            },
            "Dust Storm": {
                "components": ["Earth Magic", "Wind Magic"],
                "result_skill": "Dust Storm",
                "fusion_type": "elemental"
            },

            # Combat Fusions
            "Venomous Web Trap": {
                "components": ["Web Spin", "Poison Bite"],
                "result_skill": "Venomous Web Trap",
                "fusion_type": "combat"
            },
            "Corrosive Absorption": {
                "components": ["Absorb", "Acid Resistance"],
                "result_skill": "Corrosive Absorption",
                "fusion_type": "combat"
            },
            "Coordinated Strike": {
                "components": ["Pack Tactics", "Tool Use"],
                "result_skill": "Coordinated Strike",
                "fusion_type": "combat"
            },

            # Utility Fusions
            "Shadow Step": {
                "components": ["Stealth", "Speed Boost"],
                "result_skill": "Shadow Step",
                "fusion_type": "utility"
            },
            "Battle Trance": {
                "components": ["Regeneration", "Meditation"],
                "result_skill": "Battle Trance",
                "fusion_type": "utility"
            },
            "Arcane Insight": {
                "components": ["Detect Magic", "Analyze"],
                "result_skill": "Arcane Insight",
                "fusion_type": "utility"
            },

            # Evolution-Specific Fusions
            "Perfect Copy": {
                "components": ["Absorb", "Mimic"],
                "result_skill": "Perfect Copy",
                "fusion_type": "evolution",
                "creature_requirement": "Slime"
            },
            "Dimensional Web": {
                "components": ["Web Spin", "Phase"],
                "result_skill": "Dimensional Web",
                "fusion_type": "evolution",
                "creature_requirement": "Spider"
            },
            "Tactical Genius": {
                "components": ["Leadership", "Cunning"],
                "result_skill": "Tactical Genius",
                "fusion_type": "evolution",
                "creature_requirement": "Goblin"
            },

            # New Creature-Specific Fusions
            "Radiant Burst": {
                "components": ["Light Manipulation", "Magic Sense"],
                "result_skill": "Radiant Burst",
                "fusion_type": "evolution",
                "creature_requirement": "Wisp"
            },
            "Rat's Shadow Step": {
                "components": ["Keen Senses", "Stealth"],
                "result_skill": "Rat's Shadow Step",
                "fusion_type": "evolution",
                "creature_requirement": "Rat"
            },
            "Toxic Cloud": {
                "components": ["Spore Release", "Natural Healing"],
                "result_skill": "Toxic Cloud",
                "fusion_type": "evolution",
                "creature_requirement": "Mushroom"
            },

            # Beta Enhancement: New Fusion Combinations
            # Beginner Tier Fusions
            "Enhanced Combat": {
                "components": ["Basic Strike", "Combat Awareness"],
                "result_skill": "Enhanced Combat",
                "fusion_type": "beginner"
            },
            "Improved Stealth": {
                "components": ["Basic Stealth", "Quiet Movement"],
                "result_skill": "Improved Stealth",
                "fusion_type": "beginner"
            },
            "Social Grace": {
                "components": ["Basic Communication", "Social Awareness"],
                "result_skill": "Social Grace",
                "fusion_type": "beginner"
            },

            # Advanced Tier Fusions
            "Master Combatant": {
                "components": ["Combat Mastery", "Battle Instinct", "Warrior's Focus"],
                "result_skill": "Master Combatant",
                "fusion_type": "advanced"
            },
            "Shadow Master": {
                "components": ["Stealth", "Silent Movement", "Shadow Affinity"],
                "result_skill": "Shadow Master",
                "fusion_type": "advanced"
            },
            "Elemental Immunity": {
                "components": ["Fire Immunity", "Ice Immunity", "Toxin Immunity"],
                "result_skill": "Elemental Immunity",
                "fusion_type": "advanced"
            },
            "Nature's Champion": {
                "components": ["Nature Affinity", "Plant Knowledge", "Forest Navigation"],
                "result_skill": "Nature's Champion",
                "fusion_type": "advanced"
            },
            "Ultimate Diplomat": {
                "components": ["Diplomacy", "Social Insight", "Persuasion"],
                "result_skill": "Ultimate Diplomat",
                "fusion_type": "advanced"
            },

            # Master Tier Fusions (Specialization Path Skills)
            "Berserker's Wrath": {
                "components": ["Bloodlust", "Last Stand", "Death Defiance"],
                "result_skill": "Berserker's Wrath",
                "fusion_type": "specialization"
            },
            "Tactical Supremacy": {
                "components": ["Tactical Strike", "Combat Veteran", "Leadership"],
                "result_skill": "Tactical Supremacy",
                "fusion_type": "specialization"
            },
            "Guardian's Shield": {
                "components": ["Survival Instinct", "Self Care", "Regeneration"],
                "result_skill": "Guardian's Shield",
                "fusion_type": "specialization"
            },
            "Shadow Lord": {
                "components": ["Shadow Master", "Invisibility", "Ambush"],
                "result_skill": "Shadow Lord",
                "fusion_type": "specialization"
            },
            "Elemental Sage": {
                "components": ["Elemental Immunity", "Ancient Knowledge", "Investigation"],
                "result_skill": "Elemental Sage",
                "fusion_type": "specialization"
            },
            "Wild Spirit": {
                "components": ["Nature's Champion", "Survival Instinct", "Treasure Hunter"],
                "result_skill": "Wild Spirit",
                "fusion_type": "specialization"
            },

            # Circumstantial Environmental Fusions
            "Magical Metabolism": {
                "components": ["Magical Digestion", "Mana Absorption"],
                "result_skill": "Magical Metabolism",
                "fusion_type": "environmental"
            },
            "Nature's Child": {
                "components": ["Nature's Blessing", "Foraging", "Fruit Identification"],
                "result_skill": "Nature's Child",
                "fusion_type": "environmental"
            },
            "Elemental Resistance": {
                "components": ["Heat Resistance", "Cold Resistance", "Storm Resistance"],
                "result_skill": "Elemental Resistance",
                "fusion_type": "environmental"
            },
            "Elemental Affinity": {
                "components": ["Fire Affinity", "Ice Affinity", "Lightning Affinity"],
                "result_skill": "Elemental Affinity",
                "fusion_type": "environmental"
            },
            "Mystic Scholar": {
                "components": ["Ancient Languages", "Forbidden Knowledge", "Scholarly Mind"],
                "result_skill": "Mystic Scholar",
                "fusion_type": "knowledge"
            },
            "Crystal Sage": {
                "components": ["Crystal Attunement", "Energy Channeling", "Gem Sight"],
                "result_skill": "Crystal Sage",
                "fusion_type": "mystical"
            },
            "Vampiric Lord": {
                "components": ["Blood Drain", "Vampiric Healing", "Life Force Absorption"],
                "result_skill": "Vampiric Lord",
                "fusion_type": "dark"
            },
            "Dream Master": {
                "components": ["Dream Walking", "Magical Dreams", "Restful Sleep"],
                "result_skill": "Dream Master",
                "fusion_type": "mystical"
            },
            "Enlightened Mind": {
                "components": ["Inner Peace", "Mental Clarity", "Focus"],
                "result_skill": "Enlightened Mind",
                "fusion_type": "mental"
            },
            "Divine Oracle": {
                "components": ["Divine Connection", "Sacred Meditation", "Spiritual Insight"],
                "result_skill": "Divine Oracle",
                "fusion_type": "spiritual"
            }
        }

    def _initialize_skill_database(self) -> Dict[str, Skill]:
        """Initialize the complete skill database"""
        skills = {}

        # Basic Skills
        basic_skills = [
            Skill("Absorb", "Absorb properties from defeated enemies", SkillType.UTILITY, SkillRarity.BASIC, 5, 0, ["absorb_ability"], {}),
            Skill("Acid Resistance", "Resistance to acid damage", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["acid_immunity"], {}),
            Skill("Web Spin", "Create webs to trap enemies", SkillType.UTILITY, SkillRarity.BASIC, 8, 15, ["trap", "immobilize"], {}),
            Skill("Poison Bite", "Venomous attack that causes poison", SkillType.COMBAT, SkillRarity.BASIC, 6, 12, ["poison", "damage"], {}),
            Skill("Tool Use", "Enhanced effectiveness with tools and weapons", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["tool_mastery"], {}),
            Skill("Pack Tactics", "Increased effectiveness when fighting alongside allies", SkillType.COMBAT, SkillRarity.BASIC, 0, 0, ["group_bonus"], {}),
            Skill("Heat Resistance", "Resistance to fire and heat damage", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["fire_immunity"], {}),
            Skill("Cold Resistance", "Resistance to ice and cold damage", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["ice_immunity"], {}),
            Skill("Stealth", "Become harder to detect", SkillType.UTILITY, SkillRarity.BASIC, 10, 0, ["invisibility"], {}),
            Skill("Speed Boost", "Temporarily increase movement speed", SkillType.UTILITY, SkillRarity.BASIC, 8, 0, ["speed_increase"], {}),
        ]

        # Add missing basic skills for fusions
        additional_basic_skills = [
            Skill("Fire Magic", "Basic fire magic abilities", SkillType.MAGIC, SkillRarity.BASIC, 10, 15, ["fire_damage"], {}),
            Skill("Water Magic", "Basic water magic abilities", SkillType.MAGIC, SkillRarity.BASIC, 10, 15, ["water_damage"], {}),
            Skill("Earth Magic", "Basic earth magic abilities", SkillType.MAGIC, SkillRarity.BASIC, 10, 15, ["earth_damage"], {}),
            Skill("Wind Magic", "Basic wind magic abilities", SkillType.MAGIC, SkillRarity.BASIC, 10, 15, ["wind_damage"], {}),
            Skill("Mimic", "Copy the appearance of other creatures", SkillType.UTILITY, SkillRarity.BASIC, 15, 0, ["mimic_ability"], {}),
            Skill("Regeneration", "Slowly heal over time", SkillType.PASSIVE, SkillRarity.BASIC, 0, 0, ["heal_over_time"], {}),
            Skill("Meditation", "Restore MP through focused concentration", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["mp_restore"], {}),
            Skill("Detect Magic", "Sense magical auras and enchantments", SkillType.UTILITY, SkillRarity.BASIC, 5, 0, ["magic_detection"], {}),
            Skill("Analyze", "Study and understand objects or creatures", SkillType.UTILITY, SkillRarity.BASIC, 8, 0, ["analysis"], {}),
            Skill("Phase", "Briefly become incorporeal", SkillType.UTILITY, SkillRarity.ADVANCED, 20, 0, ["phase_shift"], {}),
            Skill("Leadership", "Inspire and command others", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["leadership_bonus"], {}),
            Skill("Cunning", "Enhanced tactical thinking", SkillType.PASSIVE, SkillRarity.BASIC, 0, 0, ["tactical_bonus"], {}),

            # New creature-specific abilities
            Skill("Light Manipulation", "Control and shape light energy", SkillType.MAGIC, SkillRarity.BASIC, 8, 15, ["light_damage", "illumination"], {}),
            Skill("Magic Sense", "Detect magical energies and auras", SkillType.PASSIVE, SkillRarity.BASIC, 0, 0, ["magic_detection"], {}),
            Skill("Keen Senses", "Enhanced hearing, smell, and awareness", SkillType.PASSIVE, SkillRarity.BASIC, 0, 0, ["detection_bonus"], {}),
            Skill("Scavenge", "Find useful items in unlikely places", SkillType.UTILITY, SkillRarity.BASIC, 5, 0, ["item_finding"], {}),
            Skill("Spore Release", "Release various types of spores", SkillType.MAGIC, SkillRarity.BASIC, 10, 12, ["area_effect"], {}),
            Skill("Natural Healing", "Accelerated healing and regeneration", SkillType.UTILITY, SkillRarity.BASIC, 12, 0, ["healing_boost"], {})
        ]

        # Fused Skills
        fused_skills = [
            Skill("Temperature Regulation", "Complete immunity to temperature effects", SkillType.RESISTANCE, SkillRarity.FUSED, 0, 0, ["temperature_immunity", "environmental_adaptation"], {}, ["Heat Resistance", "Cold Resistance"]),
            Skill("Steam Mastery", "Control scalding steam for attacks and concealment", SkillType.MAGIC, SkillRarity.FUSED, 18, 30, ["steam_damage", "concealment"], {}, ["Fire Magic", "Water Magic"]),
            Skill("Dust Storm", "Create blinding dust storms", SkillType.MAGIC, SkillRarity.FUSED, 20, 25, ["area_damage", "blindness"], {}, ["Earth Magic", "Wind Magic"]),
            Skill("Venomous Web Trap", "Poisonous webs that deal damage over time", SkillType.COMBAT, SkillRarity.FUSED, 15, 25, ["poison_trap", "area_damage", "immobilize"], {}, ["Web Spin", "Poison Bite"]),
            Skill("Corrosive Absorption", "Absorb and reflect acid damage", SkillType.COMBAT, SkillRarity.FUSED, 12, 20, ["acid_reflect", "absorb_damage"], {}, ["Absorb", "Acid Resistance"]),
            Skill("Shadow Step", "Teleport short distances through shadows", SkillType.UTILITY, SkillRarity.FUSED, 20, 0, ["teleport", "stealth_bonus"], {}, ["Stealth", "Speed Boost"]),
            Skill("Coordinated Strike", "Enhanced group combat effectiveness", SkillType.COMBAT, SkillRarity.FUSED, 15, 30, ["group_attack", "tactical_bonus"], {}, ["Pack Tactics", "Tool Use"]),
            Skill("Battle Trance", "Heal while fighting", SkillType.UTILITY, SkillRarity.FUSED, 25, 0, ["combat_healing"], {}, ["Regeneration", "Meditation"]),
            Skill("Arcane Insight", "Identify magical properties and weaknesses", SkillType.UTILITY, SkillRarity.FUSED, 15, 0, ["magic_analysis", "weakness_detection"], {}, ["Detect Magic", "Analyze"]),
            Skill("Perfect Copy", "Temporarily gain copied creature's abilities", SkillType.UTILITY, SkillRarity.FUSED, 30, 0, ["perfect_mimic", "ability_copy"], {}, ["Absorb", "Mimic"]),
            Skill("Dimensional Web", "Trap enemies in pocket dimensions", SkillType.COMBAT, SkillRarity.FUSED, 35, 40, ["dimensional_trap", "phase_damage"], {}, ["Web Spin", "Phase"]),
            Skill("Tactical Genius", "Predict and counter enemy moves", SkillType.UTILITY, SkillRarity.FUSED, 20, 0, ["prediction", "counter_attack"], {}, ["Leadership", "Cunning"]),

            # New creature-specific fused skills
            Skill("Radiant Burst", "Explosive burst of pure light energy", SkillType.MAGIC, SkillRarity.FUSED, 25, 40, ["massive_light_damage", "area_blind"], {}, ["Light Manipulation", "Magic Sense"]),
            Skill("Rat's Shadow Step", "Instantly move through shadows undetected", SkillType.UTILITY, SkillRarity.FUSED, 15, 0, ["teleport", "stealth_bonus"], {}, ["Keen Senses", "Stealth"]),
            Skill("Toxic Cloud", "Create a cloud of healing or harmful spores", SkillType.MAGIC, SkillRarity.FUSED, 20, 25, ["area_poison", "area_heal"], {}, ["Spore Release", "Natural Healing"]),

            # Beta Enhancement: New Fused Skills
            # Beginner Tier Fused Skills
            Skill("Enhanced Combat", "Improved combat fundamentals (+35% damage, +25% accuracy)", SkillType.COMBAT, SkillRarity.FUSED, 12, 0, ["enhanced_combat"], {}, ["Basic Strike", "Combat Awareness"]),
            Skill("Improved Stealth", "Better stealth capabilities (+60% stealth effectiveness)", SkillType.UTILITY, SkillRarity.FUSED, 8, 0, ["improved_stealth"], {}, ["Basic Stealth", "Quiet Movement"]),
            Skill("Social Grace", "Natural social abilities (+50% social success)", SkillType.UTILITY, SkillRarity.FUSED, 0, 0, ["social_grace"], {}, ["Basic Communication", "Social Awareness"]),

            # Advanced Tier Fused Skills
            Skill("Master Combatant", "Supreme warrior (+60% damage, +40% accuracy, +30% crit, +25% speed)", SkillType.COMBAT, SkillRarity.FUSED, 25, 0, ["master_combat"], {}, ["Combat Mastery", "Battle Instinct", "Warrior's Focus"]),
            Skill("Shadow Master", "Lord of shadows (+100% stealth, shadow teleportation, darkness control)", SkillType.UTILITY, SkillRarity.FUSED, 30, 20, ["shadow_mastery"], {}, ["Stealth", "Silent Movement", "Shadow Affinity"]),
            Skill("Elemental Immunity", "Immunity to all elemental damage and environmental effects", SkillType.RESISTANCE, SkillRarity.FUSED, 0, 0, ["elemental_immunity"], {}, ["Fire Immunity", "Ice Immunity", "Toxin Immunity"]),
            Skill("Nature's Champion", "Guardian of the natural world (nature magic, plant control, animal communication)", SkillType.UTILITY, SkillRarity.FUSED, 20, 25, ["nature_champion"], {}, ["Nature Affinity", "Plant Knowledge", "Forest Navigation"]),
            Skill("Ultimate Diplomat", "Master of all social interaction (+150% social success, mind influence)", SkillType.UTILITY, SkillRarity.FUSED, 15, 0, ["ultimate_diplomacy"], {}, ["Diplomacy", "Social Insight", "Persuasion"]),

            # Specialization Path Fused Skills
            Skill("Berserker's Wrath", "Unstoppable rage incarnate (damage immunity while raging, fear aura, berserker transformation)", SkillType.COMBAT, SkillRarity.FUSED, 40, 0, ["berserker_wrath"], {}, ["Bloodlust", "Last Stand", "Death Defiance"]),
            Skill("Tactical Supremacy", "Master strategist and commander (predict all enemy actions, army command, tactical time dilation)", SkillType.UTILITY, SkillRarity.FUSED, 35, 0, ["tactical_supremacy"], {}, ["Tactical Strike", "Combat Veteran", "Leadership"]),
            Skill("Guardian's Shield", "Unbreakable protector (damage redirection to self, ally protection aura, resurrection)", SkillType.UTILITY, SkillRarity.FUSED, 30, 0, ["guardian_shield"], {}, ["Survival Instinct", "Self Care", "Regeneration"]),
            Skill("Shadow Lord", "Master of darkness and assassination (shadow realm access, instant death attacks, shadow army)", SkillType.COMBAT, SkillRarity.FUSED, 45, 30, ["shadow_lord"], {}, ["Shadow Master", "Invisibility", "Ambush"]),
            Skill("Elemental Sage", "Master of all elements (elemental magic mastery, weather control, elemental transformation)", SkillType.MAGIC, SkillRarity.FUSED, 50, 40, ["elemental_sage"], {}, ["Elemental Immunity", "Ancient Knowledge", "Investigation"]),
            Skill("Wild Spirit", "One with the primal world (nature transformation, elemental summoning, world shaping)", SkillType.UTILITY, SkillRarity.FUSED, 40, 35, ["wild_spirit"], {}, ["Nature's Champion", "Survival Instinct", "Treasure Hunter"]),

            # Circumstantial Environmental Fused Skills
            Skill("Magical Metabolism", "Perfect absorption and conversion of magical energy from food", SkillType.MAGIC, SkillRarity.FUSED, 0, 0, ["magical_sustenance", "mana_conversion"], {}, ["Magical Digestion", "Mana Absorption"]),
            Skill("Nature's Child", "Perfect harmony with the natural world", SkillType.UTILITY, SkillRarity.FUSED, 10, 0, ["nature_mastery", "plant_communication"], {}, ["Nature's Blessing", "Foraging", "Fruit Identification"]),
            Skill("Elemental Resistance", "Complete immunity to all elemental environmental damage", SkillType.RESISTANCE, SkillRarity.FUSED, 0, 0, ["all_elemental_immunity"], {}, ["Heat Resistance", "Cold Resistance", "Storm Resistance"]),
            Skill("Elemental Affinity", "Master control over fire, ice, and lightning magic", SkillType.MAGIC, SkillRarity.FUSED, 20, 35, ["tri_elemental_mastery"], {}, ["Fire Affinity", "Ice Affinity", "Lightning Affinity"]),
            Skill("Mystic Scholar", "Master of all ancient knowledge and forbidden secrets", SkillType.UTILITY, SkillRarity.FUSED, 0, 0, ["ultimate_knowledge"], {}, ["Ancient Languages", "Forbidden Knowledge", "Scholarly Mind"]),
            Skill("Crystal Sage", "Perfect attunement with all crystalline magical energies", SkillType.MAGIC, SkillRarity.FUSED, 15, 0, ["crystal_mastery"], {}, ["Crystal Attunement", "Energy Channeling", "Gem Sight"]),
            Skill("Vampiric Lord", "Master of blood magic and life force manipulation", SkillType.COMBAT, SkillRarity.FUSED, 25, 30, ["vampiric_mastery"], {}, ["Blood Drain", "Vampiric Healing", "Life Force Absorption"]),
            Skill("Dream Master", "Complete control over dreams and sleep realms", SkillType.MAGIC, SkillRarity.FUSED, 30, 0, ["dream_realm_mastery"], {}, ["Dream Walking", "Magical Dreams", "Restful Sleep"]),
            Skill("Enlightened Mind", "Perfect mental clarity and emotional control", SkillType.UTILITY, SkillRarity.FUSED, 0, 0, ["mental_perfection"], {}, ["Inner Peace", "Mental Clarity", "Focus"]),
            Skill("Divine Oracle", "Direct connection to divine wisdom and spiritual power", SkillType.MAGIC, SkillRarity.FUSED, 25, 0, ["divine_mastery"], {}, ["Divine Connection", "Sacred Meditation", "Spiritual Insight"])
        ]

        # Add all skills to database
        for skill in basic_skills + additional_basic_skills + fused_skills:
            skills[skill.name] = skill

        return skills

    def check_fusion_possibilities(self, character_abilities: List[str]) -> List[str]:
        """Check what fusions are possible with current abilities"""
        possible_fusions = []

        for fusion_name, recipe in self.fusion_recipes.items():
            components = recipe["components"]

            # Check if all components are available
            if all(component in character_abilities for component in components):
                # Check creature requirement if exists
                creature_req = recipe.get("creature_requirement")
                if creature_req is None:  # No creature requirement
                    possible_fusions.append(fusion_name)
                # Note: creature requirement check would need character reference

        return possible_fusions

    def perform_fusion(self, character_abilities: List[str], fusion_name: str, creature_type: str = None) -> Tuple[bool, str, Optional[Skill]]:
        """Attempt to perform a skill fusion"""
        if fusion_name not in self.fusion_recipes:
            return False, "Unknown fusion recipe", None

        recipe = self.fusion_recipes[fusion_name]
        components = recipe["components"]

        # Check if all components are available
        missing_components = [comp for comp in components if comp not in character_abilities]
        if missing_components:
            return False, f"Missing components: {', '.join(missing_components)}", None

        # Check creature requirement
        creature_req = recipe.get("creature_requirement")
        if creature_req and creature_type and creature_req.lower() not in creature_type.lower():
            return False, f"This fusion requires {creature_req} creature type", None

        # Get the fused skill
        fused_skill = self.skill_database.get(recipe["result_skill"])
        if not fused_skill:
            return False, "Fusion skill not found in database", None

        return True, f"Successfully fused {' + '.join(components)} into {fusion_name}!", fused_skill

    def get_skill_info(self, skill_name: str) -> Optional[Skill]:
        """Get detailed information about a skill"""
        return self.skill_database.get(skill_name)

    def get_fusion_hints(self, character_abilities: List[str]) -> List[str]:
        """Get hints about possible fusions based on current abilities"""
        hints = []

        for fusion_name, recipe in self.fusion_recipes.items():
            components = recipe["components"]
            owned_components = [comp for comp in components if comp in character_abilities]

            if len(owned_components) == len(components) - 1:  # Missing only one component
                missing = [comp for comp in components if comp not in character_abilities][0]
                hints.append(f"Learn '{missing}' to unlock '{fusion_name}' fusion")

        return hints

    def get_all_basic_skills(self) -> List[Skill]:
        """Get all basic (non-fused) skills"""
        return [skill for skill in self.skill_database.values() if skill.rarity != SkillRarity.FUSED]

    def _add_reward_skills_to_database(self):
        """Add reward skills to the skill database"""
        from .reward_skill_system import RewardSkillSystem

        # Create temporary reward system to get skills
        reward_system = RewardSkillSystem()

        # Add all reward skills to the main database
        for skill_name, skill in reward_system.reward_skills.items():
            self.skill_database[skill_name] = skill

        # Add legendary reward skills
        for skill_name, skill in reward_system.get_legendary_reward_skills().items():
            self.skill_database[skill_name] = skill

        # Add reward skill fusion recipes
        reward_fusions = reward_system.get_fusion_combinations_with_rewards()
        self.fusion_recipes.update(reward_fusions)

    def get_reward_skills(self) -> List[Skill]:
        """Get all reward skills"""
        return [skill for skill in self.skill_database.values()
                if skill.skill_type == SkillType.REWARD or
                any(effect.startswith("legendary_") for effect in skill.effects)]

    def get_skills_by_type(self, skill_type: SkillType) -> List[Skill]:
        """Get all skills of a specific type"""
        return [skill for skill in self.skill_database.values() if skill.skill_type == skill_type]
