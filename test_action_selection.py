"""
Test script for the enhanced action selection system
"""
import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.game_engine import GameEngine
from game.character import Character
from game.memory_system import MemorySystem
from game.action_selection_system import ActionSelectionSystem

async def test_action_selection():
    """Test the action selection system"""
    print("Testing Enhanced Action Selection System...")
    
    try:
        # Create game engine
        game_engine = GameEngine()
        
        # Create a test character
        character = Character("TestPlayer", ["brave", "curious"], "Explorer")
        character.set_creature_type("Slime")
        character.level = 2
        character.stats["hp"] = 25
        character.stats["max_hp"] = 30
        character.abilities = ["Absorb", "Bounce", "Acid Spit"]
        
        # Set up game state
        game_engine.character = character
        game_engine.current_location = "Mysterious Forest"
        game_engine.state = game_engine.state.PLAYING
        
        # Test action generation
        print("\n1. Testing action generation...")
        game_state = game_engine.get_game_state()
        action_options = game_engine.action_system.generate_action_options(
            game_state, game_engine.memory, game_engine.location_system, character
        )
        
        print(f"Generated {len(action_options)} action options:")
        for i, action in enumerate(action_options, 1):
            risk_indicator = ""
            if action.risk_level == "medium":
                risk_indicator = " ⚠️"
            elif action.risk_level == "high":
                risk_indicator = " ⚠️⚠️"
            print(f"  {i}. {action.name}{risk_indicator} - {action.description}")
        
        # Test action parsing
        print("\n2. Testing action parsing...")
        test_inputs = ["1", "2", "explore the area", "use absorb", "invalid action"]
        
        for test_input in test_inputs:
            selected_action, input_type = game_engine.action_system.parse_player_input(
                test_input, action_options
            )
            if selected_action:
                print(f"  Input '{test_input}' -> Selected: {selected_action.name} (type: {input_type})")
            else:
                print(f"  Input '{test_input}' -> Custom action (type: {input_type})")
        
        # Test anti-repetition
        print("\n3. Testing anti-repetition system...")
        for i in range(5):
            game_engine.memory.record_player_action("explore", "Mysterious Forest")
        
        # Check if action is now considered repetitive
        is_repetitive = game_engine.memory.is_action_repetitive("explore", "Mysterious Forest")
        print(f"  'explore' action is repetitive: {is_repetitive}")
        
        # Test action cooldown
        print("\n4. Testing action cooldowns...")
        game_engine.memory.set_action_cooldown("rest", 3)
        print(f"  'rest' action on cooldown: {game_engine.memory.is_action_on_cooldown('rest')}")
        
        game_engine.memory.update_cooldowns()
        game_engine.memory.update_cooldowns()
        game_engine.memory.update_cooldowns()
        print(f"  After 3 turns, 'rest' action on cooldown: {game_engine.memory.is_action_on_cooldown('rest')}")
        
        # Test full game action processing
        print("\n5. Testing full action processing...")
        game_engine.current_action_options = action_options
        
        # Test with a numbered selection
        response = await game_engine.process_game_action("1")
        print(f"  Response to action '1': {response[:100]}...")
        
        print("\n✅ Action selection system test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_memory_anti_repetition():
    """Test the memory system's anti-repetition features"""
    print("\nTesting Memory Anti-Repetition Features...")
    
    memory = MemorySystem()
    
    # Test action recording
    print("1. Recording actions...")
    actions = ["explore", "look around", "explore", "search", "explore"]
    for action in actions:
        memory.record_player_action(action, "Forest")
    
    print(f"   Action history: {[a['action'] for a in memory.action_history]}")
    
    # Test repetition detection
    print("2. Testing repetition detection...")
    print(f"   'explore' is repetitive: {memory.is_action_repetitive('explore', 'Forest')}")
    print(f"   'search' is repetitive: {memory.is_action_repetitive('search', 'Forest')}")
    
    # Test response pattern tracking
    print("3. Testing response pattern tracking...")
    responses = [
        "You see a beautiful forest clearing.",
        "You hear birds singing in the trees.",
        "Suddenly, a wolf appears!",
        "Nothing happens as you wait."
    ]
    
    for response in responses:
        memory.record_ai_response_pattern(response)
    
    print(f"   Recorded {len(memory.response_patterns)} response patterns")
    
    # Test context generation
    print("4. Testing context generation...")
    context = memory.get_action_suggestions_context("Forest")
    print(f"   Recent actions: {context['recent_actions']}")
    print(f"   Overused actions: {context['overused_actions']}")
    
    print("✅ Memory anti-repetition test completed!")

if __name__ == "__main__":
    print("🎮 Enhanced Action Selection System Test")
    print("=" * 50)
    
    # Test memory system first
    test_memory_anti_repetition()
    
    # Test full action selection system
    asyncio.run(test_action_selection())
    
    print("\n🎉 All tests completed!")
