#!/usr/bin/env python3
"""
Test script to verify our fixes for portrait generation and skill learning
"""
import sys
import os
import asyncio

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.imagen_client import ImagenClient
from game.skill_learning_system import SkillLearningSystem
from game.character import Character

async def test_portrait_generation():
    """Test the fixed portrait generation"""
    print("Testing portrait generation...")
    
    try:
        client = ImagenClient()
        
        # Test character data
        character_data = {
            'name': 'TestRat',
            'creature_type': 'Rat',
            'traits': ['small', 'agile'],
            'occupation': 'Software Engineer',
            'level': 1
        }
        
        print("Attempting to generate portrait...")
        result = await client.generate_character_portrait(character_data, "base")
        
        if result:
            print("✓ Portrait generation successful!")
            print(f"Generated {len(result)} bytes of image data")
        else:
            print("✗ Portrait generation failed - no image data returned")
            
    except Exception as e:
        print(f"✗ Portrait generation failed with error: {e}")
        import traceback
        traceback.print_exc()

def test_skill_learning():
    """Test the fixed skill learning logic"""
    print("\nTesting skill learning...")
    
    try:
        learning_system = SkillLearningSystem()
        
        # Test with a rat character (should NOT learn weapon familiarity easily)
        rat_character = Character("TestRat", ["small", "agile"], "Software Engineer")
        rat_character.set_creature_type("Rat")
        rat_character.level = 1
        rat_character.combat_victories = 0
        
        print("Testing rat combat actions...")
        
        # Test basic combat - should learn basic combat skills but NOT weapon familiarity
        learned_skills = learning_system.analyze_action_for_learning(
            "attack the enemy", rat_character, "Forest"
        )
        print(f"Rat combat action learned: {learned_skills}")
        
        # Test weapon combat - should NOT learn weapon familiarity for rat
        learned_skills = learning_system.analyze_action_for_learning(
            "attack with sword", rat_character, "Forest"
        )
        print(f"Rat weapon combat learned: {learned_skills}")
        
        # Test with a goblin character (should be able to learn weapon familiarity)
        goblin_character = Character("TestGoblin", ["cunning", "tool_use"], "Warrior")
        goblin_character.set_creature_type("Goblin")
        goblin_character.level = 1
        goblin_character.combat_victories = 0
        
        print("\nTesting goblin combat actions...")
        
        # Reset learning system for clean test
        learning_system = SkillLearningSystem()
        
        # Test basic combat
        learned_skills = learning_system.analyze_action_for_learning(
            "attack the enemy", goblin_character, "Forest"
        )
        print(f"Goblin combat action learned: {learned_skills}")
        
        # Test weapon combat - should be able to learn weapon familiarity
        for i in range(5):  # Try multiple times to trigger learning
            learned_skills = learning_system.analyze_action_for_learning(
                "attack with sword", goblin_character, "Forest"
            )
            if learned_skills:
                print(f"Goblin weapon combat learned (attempt {i+1}): {learned_skills}")
                break
        
        print("✓ Skill learning logic test completed")
        
    except Exception as e:
        print(f"✗ Skill learning test failed with error: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all tests"""
    print("=== Testing Fixes ===")
    
    # Test portrait generation
    await test_portrait_generation()
    
    # Test skill learning
    test_skill_learning()
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(main())
