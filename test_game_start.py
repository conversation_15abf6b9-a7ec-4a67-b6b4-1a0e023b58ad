#!/usr/bin/env python3
"""
Test script to verify the game can start and create a character
"""
import sys
import os
import asyncio

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.game_engine import GameEngine
from game.skill_learning_system import SkillLearningSystem
from game.character import Character

async def test_character_creation():
    """Test character creation and skill learning"""
    print("Testing character creation...")

    try:
        game_engine = GameEngine()

        # Start new game
        print("\n=== Starting New Game ===")
        start_response = await game_engine.start_new_game()
        print("Game started successfully!")

        # Simulate character creation process
        print("\n=== Creating Rat Character ===")

        # Choose backstory (1 = Software Engineer)
        backstory_response = await game_engine.process_character_creation("1")
        print("Backstory selected")

        # Choose name
        name_response = await game_engine.process_character_creation("TestRat")
        print("Name set")

        # Choose traits
        traits_response = await game_engine.process_character_creation("curious, analytical, adaptable")
        print("Traits set")

        # Choose occupation
        occupation_response = await game_engine.process_character_creation("Software Engineer")
        print("Occupation set")

        # Choose creature (6 = Rat)
        creature_response = await game_engine.process_character_creation("6")
        print("Creature selected")

        print(f"Created character: {game_engine.character.name}")
        print(f"Creature type: {game_engine.character.creature_type}")
        print(f"Occupation: {game_engine.character.occupation}")
        print(f"Starting abilities: {game_engine.character.abilities}")
        print(f"Starting stats: {game_engine.character.stats}")

        # Test skill learning with this character
        learning_system = SkillLearningSystem()

        print("\n=== Testing Skill Learning ===")

        # Test basic combat
        learned_skills = learning_system.analyze_action_for_learning(
            "attack the enemy", game_engine.character, "Forest"
        )
        print(f"First combat action learned: {learned_skills}")

        # Test weapon combat (should not learn weapon familiarity)
        learned_skills = learning_system.analyze_action_for_learning(
            "attack with sword", game_engine.character, "Forest"
        )
        print(f"Weapon combat learned: {learned_skills}")

        # Test exploration
        learned_skills = learning_system.analyze_action_for_learning(
            "explore the forest", game_engine.character, "Forest"
        )
        print(f"Exploration learned: {learned_skills}")

        print("✓ Character creation and skill learning test successful!")

    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

async def test_goblin_creation():
    """Test goblin creation for comparison"""
    print("\n=== Creating Goblin Character ===")

    try:
        game_engine = GameEngine()

        # Start new game
        await game_engine.start_new_game()

        # Simulate character creation process for goblin
        await game_engine.process_character_creation("1")  # Backstory
        await game_engine.process_character_creation("TestGoblin")  # Name
        await game_engine.process_character_creation("cunning, tool_use, clever")  # Traits
        await game_engine.process_character_creation("Software Engineer")  # Occupation
        await game_engine.process_character_creation("4")  # Goblin

        print(f"Created character: {game_engine.character.name}")
        print(f"Creature type: {game_engine.character.creature_type}")
        print(f"Occupation: {game_engine.character.occupation}")
        print(f"Starting abilities: {game_engine.character.abilities}")

        # Test skill learning
        learning_system = SkillLearningSystem()

        # Test weapon combat (should be able to learn weapon skills)
        for i in range(5):
            learned_skills = learning_system.analyze_action_for_learning(
                "attack with sword", game_engine.character, "Forest"
            )
            if learned_skills:
                print(f"Goblin weapon combat learned (attempt {i+1}): {learned_skills}")
                break

        print("✓ Goblin creation test successful!")

    except Exception as e:
        print(f"✗ Goblin test failed with error: {e}")
        import traceback
        traceback.print_exc()

async def main():
    print("=== Game Start Test ===")
    await test_character_creation()
    await test_goblin_creation()
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(main())
