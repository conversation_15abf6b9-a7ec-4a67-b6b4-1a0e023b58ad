"""
Unit tests for the action selection system
"""
import pytest
from game.action_selection_system import ActionSelectionSystem, ActionOption, ActionCategory
from game.memory_system import MemorySystem
from game.character import Character
from game.location_system import LocationSystem

class TestActionSelectionSystem:
    def test_action_option_creation(self):
        """Test ActionOption dataclass creation"""
        action = ActionOption(
            id="test_action",
            name="Test Action",
            description="A test action",
            category=ActionCategory.EXPLORATION,
            requirements=["test_req"]
        )

        assert action.id == "test_action"
        assert action.name == "Test Action"
        assert action.category == ActionCategory.EXPLORATION
        assert action.requirements == ["test_req"]
        assert action.hidden_info == False  # Default value
        assert action.cooldown == 0  # Default value
        assert action.risk_level == "low"  # Default value

    def test_action_system_initialization(self):
        """Test ActionSelectionSystem initialization"""
        system = ActionSelectionSystem()

        assert len(system.base_actions) > 0
        assert "explore_area" in system.base_actions
        assert "check_inventory" in system.base_actions
        assert len(system.contextual_generators) > 0

    def test_parse_player_input_numbered(self):
        """Test parsing numbered player input"""
        system = ActionSelectionSystem()
        actions = [
            ActionOption("action1", "Action 1", "First action", ActionCategory.EXPLORATION, []),
            ActionOption("action2", "Action 2", "Second action", ActionCategory.COMBAT, [])
        ]

        # Test valid number
        selected, input_type = system.parse_player_input("1", actions)
        assert selected == actions[0]
        assert input_type == "selection"

        # Test valid number (second option)
        selected, input_type = system.parse_player_input("2", actions)
        assert selected == actions[1]
        assert input_type == "selection"

        # Test invalid number
        selected, input_type = system.parse_player_input("5", actions)
        assert selected is None
        assert input_type == "custom"

    def test_parse_player_input_text(self):
        """Test parsing text player input"""
        system = ActionSelectionSystem()
        actions = [
            ActionOption("explore_area", "Explore the area", "Look around", ActionCategory.EXPLORATION, []),
            ActionOption("use_ability", "Use Absorb", "Use your ability", ActionCategory.SKILL, [])
        ]

        # Test exact match
        selected, input_type = system.parse_player_input("Explore the area", actions)
        assert selected == actions[0]
        assert input_type == "selection"

        # Test partial match
        selected, input_type = system.parse_player_input("use absorb", actions)
        assert selected == actions[1]
        assert input_type == "selection"

        # Test custom action
        selected, input_type = system.parse_player_input("dance wildly", actions)
        assert selected is None
        assert input_type == "custom"

    def test_action_generation_with_character(self):
        """Test action generation based on character abilities"""
        system = ActionSelectionSystem()
        character = Character("Test", ["brave"], "Fighter")
        character.set_creature_type("Slime")
        character.abilities = ["Absorb", "Bounce"]

        memory = MemorySystem()
        location_system = LocationSystem()

        game_state = {
            "current_location": "Mysterious Forest",
            "character": character.to_dict(),
            "world_state": {"time": "morning", "weather": "clear"}
        }

        actions = system.generate_action_options(game_state, memory, location_system, character)

        # Should have base actions plus character-specific actions
        assert len(actions) > 0

        # Check for ability-based actions (more flexible matching)
        ability_actions = [a for a in actions if any(ability.lower() in a.name.lower() for ability in character.abilities)]

        # Also test that character actions are generated correctly
        character_actions = system._generate_character_actions(game_state, memory, location_system, character)
        assert len(character_actions) >= 2  # Should have Use Absorb and Use Bounce

        # At least one ability action should make it to the final selection
        assert len(ability_actions) > 0

    def test_action_filtering_repetitive(self):
        """Test filtering of repetitive actions"""
        system = ActionSelectionSystem()
        memory = MemorySystem()

        # Record repetitive actions
        for _ in range(4):
            memory.record_player_action("explore", "Forest")

        # Create test actions
        actions = [
            ActionOption("explore", "Explore", "Look around", ActionCategory.EXPLORATION, []),
            ActionOption("rest", "Rest", "Take a break", ActionCategory.SPECIAL, [])
        ]

        # Test filtering
        repetition_context = memory.get_action_suggestions_context("Forest")
        filtered = system._filter_repetitive_actions(actions, repetition_context, "Forest", memory)

        # Explore should be filtered out due to repetition
        explore_actions = [a for a in filtered if a.id == "explore"]
        assert len(explore_actions) == 0

        # Rest should remain
        rest_actions = [a for a in filtered if a.id == "rest"]
        assert len(rest_actions) == 1

    def test_action_scoring_and_selection(self):
        """Test action scoring and diverse selection"""
        system = ActionSelectionSystem()

        actions = [
            ActionOption("explore1", "Explore 1", "First exploration", ActionCategory.EXPLORATION, []),
            ActionOption("explore2", "Explore 2", "Second exploration", ActionCategory.EXPLORATION, []),
            ActionOption("combat1", "Combat 1", "First combat", ActionCategory.COMBAT, []),
            ActionOption("skill1", "Skill 1", "First skill", ActionCategory.SKILL, []),
            ActionOption("social1", "Social 1", "First social", ActionCategory.SOCIAL, [])
        ]

        # Score actions
        game_state = {"character": {"stats": {"hp": 30, "max_hp": 30}}}
        memory = MemorySystem()
        scored = system._score_actions(actions, game_state, memory)

        assert len(scored) == len(actions)

        # Select diverse actions
        selected = system._select_diverse_actions(scored, 3)

        # Should prioritize different categories
        categories = [action.category for action in selected]
        assert len(set(categories)) >= 2  # At least 2 different categories

class TestMemoryAntiRepetition:
    def test_action_recording(self):
        """Test action recording functionality"""
        memory = MemorySystem()

        memory.record_player_action("explore", "Forest")
        memory.record_player_action("search", "Forest")

        assert len(memory.action_history) == 2
        assert memory.action_history[0]["action"] == "explore"
        assert memory.action_history[1]["action"] == "search"

    def test_repetition_detection(self):
        """Test repetition detection"""
        memory = MemorySystem()

        # Record same action multiple times
        for _ in range(4):
            memory.record_player_action("explore", "Forest")

        # Should be detected as repetitive
        assert memory.is_action_repetitive("explore", "Forest", threshold=3)

        # Different action should not be repetitive
        assert not memory.is_action_repetitive("search", "Forest", threshold=3)

    def test_action_cooldowns(self):
        """Test action cooldown system"""
        memory = MemorySystem()

        # Set cooldown
        memory.set_action_cooldown("rest", 3)
        assert memory.is_action_on_cooldown("rest")

        # Update cooldowns
        memory.update_cooldowns()
        assert memory.is_action_on_cooldown("rest")

        memory.update_cooldowns()
        memory.update_cooldowns()
        assert not memory.is_action_on_cooldown("rest")

    def test_response_pattern_tracking(self):
        """Test AI response pattern tracking"""
        memory = MemorySystem()

        responses = [
            "You see a beautiful forest.",
            "You hear strange sounds.",
            "Suddenly, something happens!",
            "Nothing happens."
        ]

        for response in responses:
            memory.record_ai_response_pattern(response)

        assert len(memory.response_patterns) == 4

        # Check pattern detection
        patterns = memory.response_patterns
        sight_patterns = [p for p in patterns if "descriptive_sight" in p["patterns"]]
        sound_patterns = [p for p in patterns if "descriptive_sound" in p["patterns"]]
        sudden_patterns = [p for p in patterns if "sudden_event" in p["patterns"]]

        assert len(sight_patterns) == 1
        assert len(sound_patterns) == 1
        assert len(sudden_patterns) == 1

    def test_context_generation(self):
        """Test action suggestion context generation"""
        memory = MemorySystem()

        # Record some actions
        actions = ["explore", "search", "explore", "fight", "explore"]
        for action in actions:
            memory.record_player_action(action, "Forest")

        context = memory.get_action_suggestions_context("Forest")

        assert "recent_actions" in context
        assert "location_action_counts" in context
        assert "overused_actions" in context
        assert "response_patterns" in context

        # Check overused actions detection
        assert "explore" in context["overused_actions"]
        assert "search" not in context["overused_actions"]

    def test_memory_serialization_with_anti_repetition(self):
        """Test memory serialization includes anti-repetition data"""
        memory = MemorySystem()

        # Add some anti-repetition data
        memory.record_player_action("explore", "Forest")
        memory.set_action_cooldown("rest", 2)
        memory.record_ai_response_pattern("You see something interesting.")

        # Serialize
        memory_dict = memory.to_dict()

        assert "action_history" in memory_dict
        assert "action_cooldowns" in memory_dict
        assert "response_patterns" in memory_dict
        assert "location_action_counts" in memory_dict

        # Deserialize
        new_memory = MemorySystem.from_dict(memory_dict)

        assert len(new_memory.action_history) == 1
        assert "rest" in new_memory.action_cooldowns
        assert len(new_memory.response_patterns) == 1
        assert "Forest" in new_memory.location_action_counts
