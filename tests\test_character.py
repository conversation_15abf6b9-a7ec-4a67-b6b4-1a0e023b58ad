"""
Tests for the Character class
"""
import pytest
from game.character import Character

class TestCharacter:
    def test_character_initialization(self):
        """Test character initialization"""
        char = Character("Test<PERSON><PERSON>", ["brave", "smart"], "programmer")

        assert char.name == "TestHero"
        assert char.traits == ["brave", "smart"]
        assert char.occupation == "programmer"
        assert char.level == 1
        assert char.experience == 0
        assert char.creature_type == ""

    def test_set_creature_type(self):
        """Test setting creature type"""
        char = Character()
        char.set_creature_type("Slime")

        assert char.creature_type == "Slime"
        assert char.stats["hp"] == 50
        assert char.stats["max_hp"] == 50
        assert char.stats["mp"] == 30
        assert char.stats["max_mp"] == 30
        assert "Absorb" in char.abilities
        assert "Acid Resistance" in char.abilities

    def test_gain_experience_and_level_up(self):
        """Test experience gain and leveling up"""
        char = Character()
        char.set_creature_type("Slime")
        initial_hp = char.stats["max_hp"]

        # Gain enough experience to level up
        char.gain_experience(100)

        assert char.level == 2
        assert char.experience == 0  # Should be 0 since exactly 100 exp needed
        assert char.stats["max_hp"] > initial_hp
        assert char.stats["hp"] == char.stats["max_hp"]  # Should heal to full

    def test_experience_overflow(self):
        """Test experience overflow carries over to next level"""
        char = Character()
        char.set_creature_type("Slime")

        # Gain more experience than needed for one level
        char.gain_experience(150)  # Level 1 needs 100, so 50 should carry over

        assert char.level == 2
        assert char.experience == 50  # Should carry over excess

        # Test multiple level ups
        char.gain_experience(250)  # Level 2 needs 200, so should go to level 3 with 100 left

        assert char.level == 3
        assert char.experience == 100  # Should carry over excess

    def test_take_damage(self):
        """Test taking damage"""
        char = Character()
        char.set_creature_type("Slime")
        initial_hp = char.stats["hp"]

        # Take some damage
        alive = char.take_damage(10)

        assert alive is True
        assert char.stats["hp"] == initial_hp - 10

        # Take fatal damage
        alive = char.take_damage(1000)

        assert alive is False
        assert char.stats["hp"] == 0

    def test_heal(self):
        """Test healing"""
        char = Character()
        char.set_creature_type("Slime")
        max_hp = char.stats["max_hp"]

        # Take damage then heal
        char.take_damage(20)
        char.heal(10)

        assert char.stats["hp"] == max_hp - 10

        # Heal beyond max
        char.heal(50)

        assert char.stats["hp"] == max_hp

    def test_use_mp(self):
        """Test MP usage"""
        char = Character()
        char.set_creature_type("Slime")
        initial_mp = char.stats["mp"]

        # Use some MP
        success = char.use_mp(10)

        assert success is True
        assert char.stats["mp"] == initial_mp - 10

        # Try to use more MP than available
        success = char.use_mp(1000)

        assert success is False
        assert char.stats["mp"] == initial_mp - 10  # Should remain unchanged

    def test_learn_ability(self):
        """Test learning new abilities"""
        char = Character()
        char.set_creature_type("Slime")
        initial_abilities = len(char.abilities)

        # Learn new ability
        char.learn_ability("Fire Resistance")

        assert len(char.abilities) == initial_abilities + 1
        assert "Fire Resistance" in char.abilities

        # Try to learn same ability again
        char.learn_ability("Fire Resistance")

        assert len(char.abilities) == initial_abilities + 1  # Should not duplicate

    def test_to_dict_and_from_dict(self):
        """Test serialization and deserialization"""
        char = Character("TestHero", ["brave"], "programmer")
        char.set_creature_type("Spider")
        char.gain_experience(50)
        char.learn_ability("Web Mastery")

        # Convert to dict
        char_dict = char.to_dict()

        # Create new character from dict
        new_char = Character.from_dict(char_dict)

        assert new_char.name == char.name
        assert new_char.traits == char.traits
        assert new_char.occupation == char.occupation
        assert new_char.creature_type == char.creature_type
        assert new_char.level == char.level
        assert new_char.experience == char.experience
        assert new_char.stats == char.stats
        assert new_char.abilities == char.abilities

    def test_get_status_summary(self):
        """Test status summary generation"""
        char = Character("TestHero", ["brave"], "programmer")
        char.set_creature_type("Goblin")

        status = char.get_status_summary()

        assert "TestHero" in status
        assert "Goblin" in status
        assert "Level: 1" in status
        assert "HP:" in status
        assert "MP:" in status
