"""
Test suite for the new circumstantial skills system
"""
import unittest
from unittest.mock import Mock
from game.skill_learning_system import SkillLearningSystem


class TestCircumstantialSkills(unittest.TestCase):
    """Test the new circumstantial skills system"""

    def setUp(self):
        """Set up test fixtures"""
        self.learning_system = SkillLearningSystem()
        self.skill_system = self.learning_system.skill_system

        # Create a mock character
        self.character = Mock()
        self.character.level = 1
        self.character.creature_type = "Slime"
        self.character.abilities = []
        self.character.combat_victories = 0
        self.character.stats = {"hp": 50, "max_hp": 50, "mp": 30, "max_mp": 30}

    def test_eating_glowing_mushroom_skill_learning(self):
        """Test that eating glowing mushrooms triggers appropriate skill learning"""
        # Test eating glowing mushrooms
        learned_skills = self.learning_system.analyze_action_for_learning(
            "I eat the glowing mushroom",
            self.character,
            "Mystical Forest"
        )

        # Should learn a skill from eating glowing mushrooms
        self.assertTrue(len(learned_skills) > 0)

        # Check that the action was recorded
        self.assertIn("eat_glowing_mushroom", self.learning_system.action_counters)

    def test_drinking_magical_water_skill_learning(self):
        """Test that drinking from magical springs triggers skill learning"""
        learned_skills = self.learning_system.analyze_action_for_learning(
            "I drink from the magical spring",
            self.character,
            "Sacred Grove"
        )

        # Should learn a skill from drinking magical water
        self.assertTrue(len(learned_skills) > 0)

        # Check that the action was recorded
        self.assertIn("drink_magical_water", self.learning_system.action_counters)

    def test_touching_ancient_artifact_skill_learning(self):
        """Test that touching ancient artifacts triggers skill learning"""
        # Debug: Check if the action is being detected
        action = "I carefully touch the ancient artifact"
        action_lower = action.lower()

        # Check if touch keyword is detected
        touch_detected = any(word in action_lower for word in ["touch", "feel", "caress", "stroke"])
        self.assertTrue(touch_detected, "Touch keyword should be detected")

        # Check if ancient keyword is detected
        ancient_detected = any(word in action_lower for word in ["artifact", "relic", "ancient", "mysterious object"])
        self.assertTrue(ancient_detected, "Ancient keyword should be detected")

        # Perform the action twice since it's an ADVANCED tier skill
        learned_skills = self.learning_system.analyze_action_for_learning(
            action,
            self.character,
            "Ancient Ruins"
        )

        # Check that the action was recorded (this should happen even if no skill is learned)
        self.assertIn("touch_artifact", self.learning_system.action_counters)

        # Perform the action again to meet the requirement (ADVANCED tier with tutorial boost = 2 repetitions)
        learned_skills = self.learning_system.analyze_action_for_learning(
            action,
            self.character,
            "Ancient Ruins"
        )

        # Should learn a skill from touching artifacts (second time should trigger learning)
        self.assertTrue(len(learned_skills) > 0, f"Should learn skills, but got: {learned_skills}")

    def test_mushroom_spore_release(self):
        """Test Mushroom creature spore release"""
        # Test Mushroom creature spore release (ADVANCED tier - needs 2 repetitions with tutorial boost)
        self.character.creature_type = "Mushroom"
        action = "I release spore clouds into the air"
        for _ in range(2):
            learned_skills = self.learning_system.analyze_action_for_learning(
                action,
                self.character,
                "Forest Clearing"
            )

        # Should learn spore-related skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("release_spores", self.learning_system.action_counters)

    def test_wisp_light_manipulation(self):
        """Test Wisp creature light manipulation"""
        # Test Wisp creature light manipulation (ADVANCED tier - needs 2 repetitions with tutorial boost)
        self.character.creature_type = "Wisp"
        action = "I glow brightly to illuminate the area"
        for _ in range(2):  # Fresh tutorial boost for this test
            learned_skills = self.learning_system.analyze_action_for_learning(
                action,
                self.character,
                "Dark Cave"
            )

        # Should learn light-related skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("manipulate_light", self.learning_system.action_counters)

    def test_weather_exposure_skill_learning(self):
        """Test that weather exposure triggers environmental skills"""
        # Test storm exposure
        learned_skills = self.learning_system.analyze_action_for_learning(
            "I stand in the lightning storm",
            self.character,
            "Mountain Peak"
        )

        # Should learn storm-related skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("experience_storm", self.learning_system.action_counters)

        # Test cold exposure
        learned_skills = self.learning_system.analyze_action_for_learning(
            "I endure the freezing blizzard",
            self.character,
            "Frozen Wasteland"
        )

        # Should learn cold-related skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("endure_cold", self.learning_system.action_counters)

    def test_meditation_skill_learning(self):
        """Test meditation skill learning in different locations"""
        # Test sacred meditation (ADVANCED tier - needs 2 repetitions with tutorial boost)
        action = "I meditate in the temple"
        for _ in range(2):
            learned_skills = self.learning_system.analyze_action_for_learning(
                action,
                self.character,
                "Sacred Temple"
            )

        # Should learn sacred meditation skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("meditate_sacred", self.learning_system.action_counters)

        # Test general meditation (BASIC tier - needs 1 repetition with tutorial boost)
        learned_skills = self.learning_system.analyze_action_for_learning(
            "I focus and meditate",
            self.character,
            "Forest Clearing"
        )

        # Should learn general meditation skills
        self.assertTrue(len(learned_skills) > 0)
        self.assertIn("meditate_general", self.learning_system.action_counters)

    def test_circumstantial_skills_in_database(self):
        """Test that new circumstantial skills are properly added to the database"""
        # Check that new environmental skills exist
        environmental_skills = [
            "Bioluminescence", "Toxin Resistance", "Magical Digestion",
            "Mana Absorption", "Nature's Blessing", "Purification"
        ]

        for skill_name in environmental_skills:
            skill = self.skill_system.get_skill_info(skill_name)
            self.assertIsNotNone(skill, f"Skill {skill_name} should exist in database")

        # Check creature-specific skills
        creature_skills = [
            "Spore Mastery", "Light Control", "Destructive Bite",
            "Tunneling", "Ethereal Form"
        ]

        for skill_name in creature_skills:
            skill = self.skill_system.get_skill_info(skill_name)
            self.assertIsNotNone(skill, f"Skill {skill_name} should exist in database")

    def test_new_fusion_recipes(self):
        """Test that new fusion recipes work correctly"""
        # Test Magical Metabolism fusion
        test_abilities = ["Magical Digestion", "Mana Absorption"]
        possible_fusions = self.skill_system.check_fusion_possibilities(test_abilities)
        self.assertIn("Magical Metabolism", possible_fusions)

        # Test Elemental Affinity fusion
        test_abilities = ["Fire Affinity", "Ice Affinity", "Lightning Affinity"]
        possible_fusions = self.skill_system.check_fusion_possibilities(test_abilities)
        self.assertIn("Elemental Affinity", possible_fusions)

        # Test Crystal Sage fusion
        test_abilities = ["Crystal Attunement", "Energy Channeling", "Gem Sight"]
        possible_fusions = self.skill_system.check_fusion_possibilities(test_abilities)
        self.assertIn("Crystal Sage", possible_fusions)

    def test_progressive_difficulty_scaling(self):
        """Test that skill learning requirements scale with progression"""
        # Simulate learning several skills in a category
        self.learning_system.skills_learned_by_category["environmental"] = 3

        # Calculate requirements for a new environmental skill
        context = {
            "character_level": 5,
            "creature_type": "Slime",
            "current_abilities": [],
            "combat_victories": 0
        }

        from game.skill_learning_system import SkillTier
        required_reps = self.learning_system._calculate_required_repetitions(
            "environmental", SkillTier.BASIC, context
        )

        # Should require more repetitions due to progression
        self.assertGreater(required_reps, 1)


if __name__ == '__main__':
    unittest.main()
