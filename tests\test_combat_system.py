"""
Tests for the Combat System
"""
import pytest
from game.combat_system import CombatSystem, Enemy, CombatAction
from game.character import Character

class TestCombatSystem:
    def test_combat_system_initialization(self):
        """Test combat system initialization"""
        combat_system = CombatSystem()
        
        assert combat_system.skill_system is not None
        assert len(combat_system.enemy_database) > 0
        assert combat_system.active_combat is None
        assert "Forest Wolf" in combat_system.enemy_database
    
    def test_enemy_creation(self):
        """Test enemy creation and properties"""
        enemy = Enemy(
            name="Test Enemy",
            level=5,
            hp=50, max_hp=50,
            mp=20, max_mp=20,
            attack=10, defense=5, speed=7,
            abilities=["Test Attack"],
            resistances=["Fire"],
            weaknesses=["Ice"],
            exp_reward=25,
            loot_table=[{"item": "Test Item", "chance": 0.5}]
        )
        
        assert enemy.name == "Test Enemy"
        assert enemy.level == 5
        assert enemy.hp == 50
        assert "Fire" in enemy.resistances
        assert "Ice" in enemy.weaknesses
    
    def test_enemy_serialization(self):
        """Test enemy to_dict method"""
        enemy = Enemy(
            name="Serialization Test",
            level=3,
            hp=30, max_hp=30,
            mp=10, max_mp=10,
            attack=8, defense=4, speed=6,
            abilities=["Bite"],
            resistances=[],
            weaknesses=["Light"],
            exp_reward=20,
            loot_table=[]
        )
        
        enemy_dict = enemy.to_dict()
        
        assert enemy_dict["name"] == "Serialization Test"
        assert enemy_dict["level"] == 3
        assert enemy_dict["hp"] == 30
        assert enemy_dict["weaknesses"] == ["Light"]
    
    def test_combat_initialization(self):
        """Test starting a combat encounter"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        
        result = combat_system.start_combat(character, "Forest Wolf")
        
        assert result["success"] is True
        assert "Combat started" in result["message"]
        assert combat_system.active_combat is not None
        assert combat_system.active_combat["character"] == character
        assert combat_system.active_combat["enemy"].name == "Forest Wolf"
    
    def test_combat_initialization_invalid_enemy(self):
        """Test starting combat with invalid enemy"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        
        result = combat_system.start_combat(character, "Nonexistent Enemy")
        
        assert result["success"] is False
        assert "Unknown enemy" in result["message"]
        assert combat_system.active_combat is None
    
    def test_available_actions(self):
        """Test getting available combat actions"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        character.learn_ability("Tool Use")
        
        actions = combat_system.get_available_actions(character)
        
        assert len(actions) >= 4  # attack, defend, item, flee minimum
        action_names = [action["action"] for action in actions]
        assert "attack" in action_names
        assert "defend" in action_names
        assert "item" in action_names
        assert "flee" in action_names
    
    def test_damage_calculation(self):
        """Test damage calculation with randomness"""
        combat_system = CombatSystem()
        
        # Test multiple damage calculations to check randomness
        damages = []
        for _ in range(10):
            damage = combat_system._calculate_damage(10, 5)
            damages.append(damage)
        
        # All damages should be at least 1
        assert all(damage >= 1 for damage in damages)
        
        # There should be some variation (not all the same)
        assert len(set(damages)) > 1 or damages[0] >= 4  # Either varied or reasonable base
    
    def test_character_attack_action(self):
        """Test character attack action"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        enemy_initial_hp = combat_system.active_combat["enemy"].hp
        
        # Execute attack
        result = combat_system.execute_character_action("attack")
        
        assert result["success"] is True
        assert len(result["messages"]) > 0
        assert "attack" in result["messages"][0].lower()
        
        # Enemy should have taken damage
        enemy_current_hp = combat_system.active_combat["enemy"].hp
        assert enemy_current_hp < enemy_initial_hp
    
    def test_character_defend_action(self):
        """Test character defend action"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        initial_mp = character.stats["mp"]
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        
        # Execute defend
        result = combat_system.execute_character_action("defend")
        
        assert result["success"] is True
        assert "defensive stance" in result["messages"][0]
        
        # Should gain some MP
        assert character.stats["mp"] >= initial_mp
    
    def test_character_flee_action(self):
        """Test character flee action"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        character.stats["speed"] = 20  # High speed for better flee chance
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        
        # Execute flee (may need multiple attempts due to randomness)
        flee_attempted = False
        for _ in range(10):  # Try up to 10 times
            result = combat_system.execute_character_action("flee")
            flee_attempted = True
            
            if result.get("combat_ended"):
                assert result["victory"] is False
                assert combat_system.active_combat is None
                break
        
        assert flee_attempted
    
    def test_skill_usage_in_combat(self):
        """Test using skills in combat"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Spider")
        character.learn_ability("Poison Bite")
        character.stats["mp"] = 20  # Ensure enough MP
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        initial_mp = character.stats["mp"]
        
        # Use skill
        result = combat_system.execute_character_action("skill", "Poison Bite")
        
        assert result["success"] is True
        assert character.stats["mp"] < initial_mp  # MP should be consumed
    
    def test_combat_victory_handling(self):
        """Test combat victory and rewards"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        character.stats["attack"] = 100  # High attack to ensure victory
        initial_exp = character.experience
        initial_inventory_size = len(character.inventory)
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        
        # Attack until victory
        result = None
        for _ in range(10):  # Safety limit
            result = combat_system.execute_character_action("attack")
            if result.get("combat_ended"):
                break
        
        assert result is not None
        assert result.get("combat_ended") is True
        assert result.get("victory") is True
        assert combat_system.active_combat is None
        
        # Check rewards
        assert "rewards" in result
        if "experience" in result["rewards"]:
            assert character.experience > initial_exp
        if "loot" in result["rewards"]:
            assert len(character.inventory) > initial_inventory_size
    
    def test_combat_status_tracking(self):
        """Test combat status information"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        
        # No combat initially
        status = combat_system.get_combat_status()
        assert status is None
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        status = combat_system.get_combat_status()
        
        assert status is not None
        assert "character" in status
        assert "enemy" in status
        assert "turn" in status
        assert status["character"]["hp"] > 0
        assert status["enemy"]["name"] == "Forest Wolf"
    
    def test_enemy_ai_behavior(self):
        """Test enemy AI turn execution"""
        combat_system = CombatSystem()
        character = Character("Test Hero", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        initial_hp = character.stats["hp"]
        
        # Start combat
        combat_system.start_combat(character, "Forest Wolf")
        
        # Execute enemy turn
        result = combat_system._execute_enemy_turn()
        
        assert len(result["messages"]) > 0
        # Character should likely take damage (though not guaranteed due to defense)
        # Just check that the enemy did something
        assert any("attack" in msg.lower() or "use" in msg.lower() for msg in result["messages"])
    
    def test_multiple_enemy_types(self):
        """Test different enemy types have different properties"""
        combat_system = CombatSystem()
        
        # Test different enemies exist and have different stats
        wolf = combat_system.enemy_database["Forest Wolf"]
        sprite = combat_system.enemy_database["Forest Sprite"]
        
        assert wolf.name != sprite.name
        assert wolf.ai_behavior != sprite.ai_behavior
        assert wolf.level != sprite.level or wolf.hp != sprite.hp  # Should be different in some way
