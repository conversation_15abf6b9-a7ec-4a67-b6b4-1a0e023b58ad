"""
Tests for the Dynamic Difficulty Adjustment System
"""
import pytest
from game.difficulty_system import DifficultyAdjustmentSystem, DifficultyLevel, PerformanceMetrics

class TestDifficultyAdjustmentSystem:
    def test_difficulty_system_initialization(self):
        """Test difficulty system initialization"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        assert difficulty_system.current_difficulty == DifficultyLevel.NORMAL
        assert len(difficulty_system.combat_results) == 0
        assert difficulty_system.deaths == 0
        assert difficulty_system.actions_taken == 0
        assert len(difficulty_system.locations_discovered) == 0
    
    def test_combat_result_recording(self):
        """Test recording combat results"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Record some combat results
        difficulty_system.record_combat_result(True, 5)
        difficulty_system.record_combat_result(False, 8)
        difficulty_system.record_combat_result(True, 3)
        
        assert len(difficulty_system.combat_results) == 3
        assert difficulty_system.combat_results[0] == (True, 5)
        assert difficulty_system.combat_results[1] == (False, 8)
        assert difficulty_system.combat_results[2] == (True, 3)
    
    def test_combat_result_limit(self):
        """Test that combat results are limited to recent entries"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Record more than 20 combat results
        for i in range(25):
            difficulty_system.record_combat_result(True, i)
        
        # Should only keep the last 20
        assert len(difficulty_system.combat_results) == 20
        assert difficulty_system.combat_results[0] == (True, 5)  # First 5 should be removed
        assert difficulty_system.combat_results[-1] == (True, 24)
    
    def test_action_and_skill_tracking(self):
        """Test action and skill usage tracking"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Record actions and skills
        for _ in range(10):
            difficulty_system.record_action()
        
        for _ in range(3):
            difficulty_system.record_skill_usage()
        
        assert difficulty_system.actions_taken == 10
        assert difficulty_system.skills_used == 3
    
    def test_death_recording(self):
        """Test death recording"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        difficulty_system.record_death()
        difficulty_system.record_death()
        
        assert difficulty_system.deaths == 2
    
    def test_location_discovery(self):
        """Test location discovery tracking"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        difficulty_system.record_location_discovery("Forest")
        difficulty_system.record_location_discovery("Cave")
        difficulty_system.record_location_discovery("Forest")  # Duplicate
        
        assert len(difficulty_system.locations_discovered) == 2
        assert "Forest" in difficulty_system.locations_discovered
        assert "Cave" in difficulty_system.locations_discovered
    
    def test_quest_completion_tracking(self):
        """Test quest completion tracking"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        difficulty_system.record_quest_completion(True)
        difficulty_system.record_quest_completion(False)
        difficulty_system.record_quest_completion(True)
        
        assert difficulty_system.quests_attempted == 3
        assert difficulty_system.quests_completed == 2
    
    def test_performance_metrics_calculation(self):
        """Test performance metrics calculation"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Set up some data
        difficulty_system.record_combat_result(True, 5)
        difficulty_system.record_combat_result(True, 3)
        difficulty_system.record_combat_result(False, 8)
        
        for _ in range(20):
            difficulty_system.record_action()
        
        difficulty_system.record_skill_usage()
        difficulty_system.record_skill_usage()
        
        difficulty_system.record_death()
        
        difficulty_system.record_location_discovery("Forest")
        difficulty_system.record_location_discovery("Cave")
        
        difficulty_system.record_quest_completion(True)
        difficulty_system.record_quest_completion(True)
        difficulty_system.record_quest_completion(False)
        
        metrics = difficulty_system.calculate_performance_metrics()
        
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.combat_win_rate == 2/3  # 2 wins out of 3 combats
        assert metrics.average_combat_duration == (5 + 3 + 8) / 3
        assert metrics.death_count == 1
        assert metrics.skill_usage_frequency == 2/20  # 2 skills out of 20 actions
        assert metrics.exploration_thoroughness == 2/7  # 2 locations out of 7 total
        assert metrics.quest_completion_rate == 2/3  # 2 successes out of 3 attempts
    
    def test_difficulty_adjustment_insufficient_data(self):
        """Test that difficulty doesn't adjust with insufficient data"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Record only a few actions (less than 20)
        for _ in range(10):
            difficulty_system.record_action()
        
        should_adjust, new_difficulty = difficulty_system.should_adjust_difficulty()
        
        assert should_adjust is False
        assert new_difficulty == DifficultyLevel.NORMAL
    
    def test_difficulty_adjustment_too_easy(self):
        """Test difficulty adjustment when game is too easy"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Set up data indicating game is too easy
        for _ in range(25):
            difficulty_system.record_action()
        
        # Very high win rate
        for _ in range(20):
            difficulty_system.record_combat_result(True, 3)
        
        # No deaths
        # Low skill usage (indicating not challenged)
        
        should_adjust, new_difficulty = difficulty_system.should_adjust_difficulty()
        
        assert should_adjust is True
        assert new_difficulty == DifficultyLevel.HARD
    
    def test_difficulty_adjustment_too_hard(self):
        """Test difficulty adjustment when game is too hard"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Set up data indicating game is too hard
        for _ in range(50):
            difficulty_system.record_action()
        
        # Very low win rate
        for _ in range(20):
            difficulty_system.record_combat_result(False, 10)
        
        # Many deaths
        for _ in range(10):
            difficulty_system.record_death()
        
        should_adjust, new_difficulty = difficulty_system.should_adjust_difficulty()
        
        assert should_adjust is True
        assert new_difficulty == DifficultyLevel.EASY
    
    def test_difficulty_modifiers(self):
        """Test difficulty modifier retrieval"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Test normal difficulty modifiers
        modifiers = difficulty_system.get_difficulty_modifiers()
        
        assert modifiers["enemy_health"] == 1.0
        assert modifiers["enemy_damage"] == 1.0
        assert modifiers["exp_multiplier"] == 1.0
        assert modifiers["loot_chance"] == 1.0
        assert modifiers["skill_mp_cost"] == 1.0
        
        # Change to easy difficulty
        difficulty_system.current_difficulty = DifficultyLevel.EASY
        modifiers = difficulty_system.get_difficulty_modifiers()
        
        assert modifiers["enemy_health"] < 1.0  # Easier enemies
        assert modifiers["enemy_damage"] < 1.0
        assert modifiers["exp_multiplier"] > 1.0  # More rewards
    
    def test_combat_modifier_application(self):
        """Test application of combat modifiers"""
        difficulty_system = DifficultyAdjustmentSystem()
        difficulty_system.current_difficulty = DifficultyLevel.EASY
        
        enemy_stats = {
            "hp": 100,
            "max_hp": 100,
            "attack": 20
        }
        
        modified_stats = difficulty_system.apply_combat_modifiers(enemy_stats)
        
        # Should be easier (lower stats)
        assert modified_stats["hp"] < enemy_stats["hp"]
        assert modified_stats["attack"] < enemy_stats["attack"]
        assert modified_stats["max_hp"] == modified_stats["hp"]
    
    def test_reward_modifier_application(self):
        """Test application of reward modifiers"""
        difficulty_system = DifficultyAdjustmentSystem()
        difficulty_system.current_difficulty = DifficultyLevel.HARD
        
        base_exp = 100
        base_loot_chance = 0.5
        
        modified_exp, modified_loot_chance = difficulty_system.apply_reward_modifiers(base_exp, base_loot_chance)
        
        # Hard difficulty should give less rewards
        assert modified_exp < base_exp
        assert modified_loot_chance < base_loot_chance
    
    def test_skill_modifier_application(self):
        """Test application of skill MP cost modifiers"""
        difficulty_system = DifficultyAdjustmentSystem()
        difficulty_system.current_difficulty = DifficultyLevel.VERY_EASY
        
        base_mp_cost = 20
        modified_mp_cost = difficulty_system.apply_skill_modifiers(base_mp_cost)
        
        # Very easy should reduce MP costs
        assert modified_mp_cost < base_mp_cost
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Test with insufficient data
        summary = difficulty_system.get_performance_summary()
        assert "Not enough data" in summary
        
        # Add sufficient data
        for _ in range(20):
            difficulty_system.record_action()
        
        difficulty_system.record_combat_result(True, 5)
        
        summary = difficulty_system.get_performance_summary()
        assert "Performance Summary:" in summary
        assert "Combat Win Rate:" in summary
        assert "Current Difficulty:" in summary
    
    def test_serialization(self):
        """Test difficulty system serialization"""
        difficulty_system = DifficultyAdjustmentSystem()
        
        # Add some data
        difficulty_system.current_difficulty = DifficultyLevel.HARD
        difficulty_system.record_combat_result(True, 5)
        difficulty_system.record_death()
        difficulty_system.record_action()
        difficulty_system.record_location_discovery("Forest")
        
        # Convert to dict
        data = difficulty_system.to_dict()
        
        # Create new system from dict
        new_system = DifficultyAdjustmentSystem.from_dict(data)
        
        assert new_system.current_difficulty == DifficultyLevel.HARD
        assert len(new_system.combat_results) == 1
        assert new_system.deaths == 1
        assert new_system.actions_taken == 1
        assert "Forest" in new_system.locations_discovered
