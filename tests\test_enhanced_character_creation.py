"""
Tests for enhanced character creation system with lore integration and expanded creatures
"""
import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock

from game.game_engine import <PERSON><PERSON><PERSON><PERSON>, GameState
from game.lore_system import LoreSystem
from game.character import Character
from data.creatures_database import CREATURES_DATABASE, get_creature_info
import config


class TestLoreSystem:
    """Test the lore system functionality"""
    
    def test_lore_system_initialization(self):
        """Test that lore system initializes correctly"""
        lore_system = LoreSystem()
        
        assert len(lore_system.backstory_scenarios) > 0
        assert lore_system.world_lore is not None
        assert "world_name" in lore_system.world_lore
        
    def test_get_random_backstory(self):
        """Test getting a random backstory"""
        lore_system = LoreSystem()
        backstory = lore_system.get_random_backstory()
        
        assert backstory is not None
        assert hasattr(backstory, 'name')
        assert hasattr(backstory, 'death_scene')
        assert hasattr(backstory, 'transition_scene')
        assert hasattr(backstory, 'reincarnation_explanation')
        
    def test_get_backstory_by_name(self):
        """Test getting a specific backstory by name"""
        lore_system = LoreSystem()
        backstory = lore_system.get_backstory_by_name("Overwork Death")
        
        assert backstory is not None
        assert backstory.name == "Overwork Death"
        
    def test_get_all_backstory_names(self):
        """Test getting all backstory names"""
        lore_system = LoreSystem()
        names = lore_system.get_all_backstory_names()
        
        assert len(names) > 0
        assert "Overwork Death" in names
        assert "Heroic Sacrifice" in names


class TestExpandedCreatures:
    """Test the expanded creature database"""
    
    def test_creatures_database_structure(self):
        """Test that creatures database has correct structure"""
        assert "Slime" in CREATURES_DATABASE
        assert "Spider" in CREATURES_DATABASE
        assert "Goblin" in CREATURES_DATABASE
        assert "Wisp" in CREATURES_DATABASE
        assert "Rat" in CREATURES_DATABASE
        assert "Mushroom" in CREATURES_DATABASE
        
    def test_creature_info_completeness(self):
        """Test that each creature has complete information"""
        for creature_name, creature_data in CREATURES_DATABASE.items():
            assert "name" in creature_data
            assert "description" in creature_data
            assert "detailed_description" in creature_data
            assert "base_stats" in creature_data
            assert "abilities" in creature_data
            assert "evolution_paths" in creature_data
            assert "lore" in creature_data
            assert "gameplay_style" in creature_data
            
    def test_get_creature_info(self):
        """Test getting creature information"""
        wisp_info = get_creature_info("Wisp")
        assert wisp_info["name"] == "Wisp"
        assert "magical energy" in wisp_info["description"]
        
    def test_new_creatures_in_config(self):
        """Test that new creatures are added to config"""
        creature_names = [creature["name"] for creature in config.STARTING_CREATURES]
        assert "Wisp" in creature_names
        assert "Rat" in creature_names
        assert "Mushroom" in creature_names


class TestEnhancedCharacterCreation:
    """Test the enhanced character creation flow"""
    
    @pytest.fixture
    def game_engine(self):
        """Create a game engine for testing"""
        engine = GameEngine()
        engine.gemini_client.generate_character_creation_response = AsyncMock(return_value="Test response")
        engine.gemini_client.generate_text = AsyncMock(return_value="Test opening scene")
        return engine
    
    @pytest.mark.asyncio
    async def test_start_new_game_shows_backstory(self, game_engine):
        """Test that starting a new game shows backstory selection"""
        response = await game_engine.start_new_game()
        
        assert game_engine.state == GameState.CHARACTER_CREATION
        assert game_engine.creation_stage == "backstory"
        assert "Welcome to your final moments" in response
        assert "Choose your backstory" in response
        
    @pytest.mark.asyncio
    async def test_backstory_selection_numeric(self, game_engine):
        """Test backstory selection with numeric input"""
        await game_engine.start_new_game()
        response = await game_engine.process_character_creation("1")
        
        assert game_engine.selected_backstory is not None
        assert game_engine.creation_stage == "name"
        assert "What would you like to be called" in response
        
    @pytest.mark.asyncio
    async def test_backstory_selection_random(self, game_engine):
        """Test backstory selection with 'random' input"""
        await game_engine.start_new_game()
        response = await game_engine.process_character_creation("random")
        
        assert game_engine.selected_backstory is not None
        assert game_engine.creation_stage == "name"
        
    @pytest.mark.asyncio
    async def test_backstory_selection_invalid(self, game_engine):
        """Test backstory selection with invalid input"""
        await game_engine.start_new_game()
        response = await game_engine.process_character_creation("999")
        
        assert game_engine.selected_backstory is None
        assert game_engine.creation_stage == "backstory"
        assert "Please enter a number" in response
        
    @pytest.mark.asyncio
    async def test_enhanced_creature_selection(self, game_engine):
        """Test the enhanced creature selection display"""
        await game_engine.start_new_game()
        await game_engine.process_character_creation("1")  # Select backstory
        await game_engine.process_character_creation("Test Name")  # Name
        await game_engine.process_character_creation("brave, curious")  # Traits
        response = await game_engine.process_character_creation("programmer")  # Occupation
        
        assert game_engine.creation_stage == "creature"
        assert "choose your new form" in response.lower()
        assert "Stats:" in response
        assert "Starting Abilities:" in response
        assert "Evolution Paths:" in response
        
    @pytest.mark.asyncio
    async def test_creature_selection_numeric(self, game_engine):
        """Test creature selection with numeric input"""
        # Setup character creation to creature stage
        await game_engine.start_new_game()
        await game_engine.process_character_creation("1")
        await game_engine.process_character_creation("Test Name")
        await game_engine.process_character_creation("brave")
        await game_engine.process_character_creation("programmer")
        
        # Select first creature (Slime)
        response = await game_engine.process_character_creation("1")
        
        assert game_engine.character.creature_type == "Slime"
        assert game_engine.state == GameState.PLAYING
        assert "transformation is complete" in response
        
    @pytest.mark.asyncio
    async def test_creature_selection_by_name(self, game_engine):
        """Test creature selection by name"""
        # Setup character creation to creature stage
        await game_engine.start_new_game()
        await game_engine.process_character_creation("1")
        await game_engine.process_character_creation("Test Name")
        await game_engine.process_character_creation("brave")
        await game_engine.process_character_creation("programmer")
        
        # Select creature by name
        response = await game_engine.process_character_creation("Wisp")
        
        assert game_engine.character.creature_type == "Wisp"
        assert game_engine.state == GameState.PLAYING
        
    @pytest.mark.asyncio
    async def test_new_creature_stats(self, game_engine):
        """Test that new creatures have correct stats"""
        # Setup and select Wisp
        await game_engine.start_new_game()
        await game_engine.process_character_creation("1")
        await game_engine.process_character_creation("Test Name")
        await game_engine.process_character_creation("brave")
        await game_engine.process_character_creation("programmer")
        await game_engine.process_character_creation("Wisp")
        
        # Check Wisp stats
        assert game_engine.character.stats["hp"] == 25
        assert game_engine.character.stats["mp"] == 60
        assert "Light Manipulation" in game_engine.character.abilities
        assert "Magic Sense" in game_engine.character.abilities
        
    @pytest.mark.asyncio
    async def test_creature_selection_invalid(self, game_engine):
        """Test invalid creature selection"""
        # Setup character creation to creature stage
        await game_engine.start_new_game()
        await game_engine.process_character_creation("1")
        await game_engine.process_character_creation("Test Name")
        await game_engine.process_character_creation("brave")
        await game_engine.process_character_creation("programmer")
        
        # Try invalid selection
        response = await game_engine.process_character_creation("999")
        
        assert game_engine.creation_stage == "creature"
        assert "I didn't understand" in response
        assert f"1-{len(config.STARTING_CREATURES)}" in response


class TestSkillFusionIntegration:
    """Test that new creatures work with skill fusion system"""
    
    def test_new_creature_fusion_recipes(self):
        """Test that new creatures have fusion recipes"""
        from game.skill_system import SkillFusionSystem
        
        skill_system = SkillFusionSystem()
        
        # Check for Wisp fusion
        assert "Radiant Burst" in skill_system.fusion_recipes
        wisp_fusion = skill_system.fusion_recipes["Radiant Burst"]
        assert wisp_fusion["creature_requirement"] == "Wisp"
        
        # Check for Rat fusion
        assert "Rat's Shadow Step" in skill_system.fusion_recipes
        rat_fusion = skill_system.fusion_recipes["Rat's Shadow Step"]
        assert rat_fusion["creature_requirement"] == "Rat"
        
        # Check for Mushroom fusion
        assert "Toxic Cloud" in skill_system.fusion_recipes
        mushroom_fusion = skill_system.fusion_recipes["Toxic Cloud"]
        assert mushroom_fusion["creature_requirement"] == "Mushroom"
        
    def test_new_creature_skills_in_database(self):
        """Test that new creature skills are in the skill database"""
        from game.skill_system import SkillFusionSystem
        
        skill_system = SkillFusionSystem()
        
        # Check for new basic skills
        assert "Light Manipulation" in skill_system.skill_database
        assert "Magic Sense" in skill_system.skill_database
        assert "Keen Senses" in skill_system.skill_database
        assert "Scavenge" in skill_system.skill_database
        assert "Spore Release" in skill_system.skill_database
        assert "Natural Healing" in skill_system.skill_database
        
        # Check for new fused skills
        assert "Radiant Burst" in skill_system.skill_database
        assert "Rat's Shadow Step" in skill_system.skill_database
        assert "Toxic Cloud" in skill_system.skill_database


class TestEvolutionSystemIntegration:
    """Test that new creatures work with evolution system"""
    
    def test_new_creature_evolution_paths(self):
        """Test that new creatures have evolution paths"""
        from game.evolution_system import EvolutionSystem
        
        evolution_system = EvolutionSystem()
        
        # Check Wisp evolutions
        assert "Wisp" in evolution_system.evolution_paths
        wisp_paths = evolution_system.evolution_paths["Wisp"]
        assert len(wisp_paths) >= 2
        evolution_names = [path.name for path in wisp_paths]
        assert "Elemental Wisp" in evolution_names
        assert "Guardian Spirit" in evolution_names
        
        # Check Rat evolutions
        assert "Rat" in evolution_system.evolution_paths
        rat_paths = evolution_system.evolution_paths["Rat"]
        assert len(rat_paths) >= 2
        
        # Check Mushroom evolutions
        assert "Mushroom" in evolution_system.evolution_paths
        mushroom_paths = evolution_system.evolution_paths["Mushroom"]
        assert len(mushroom_paths) >= 2
