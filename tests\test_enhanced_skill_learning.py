"""
Tests for the Enhanced Skill Learning System - Beta Version
"""
import pytest
from game.skill_learning_system import SkillLearningSystem, SkillTier, SpecializationPath
from game.character import Character

class TestEnhancedSkillLearning:
    def test_tiered_learning_system(self):
        """Test the tiered learning system with progressive difficulty"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 1,
            "creature_type": "Spider",
            "current_abilities": [],
            "combat_victories": 0
        }
        
        # Test beginner skill learning (should be very easy)
        learned_skills = learning_system.record_action("first_combat", context)
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Basic Strike", "Combat Awareness"]
        
        # Update context with learned skill
        context["current_abilities"] = learned_skills
        learning_system.skills_learned_by_category["combat"] = 1
        
        # Test that subsequent skills in same category require more repetitions
        for _ in range(2):  # Should need more repetitions now
            learned_skills = learning_system.record_action("combat_action", context)
        
        # Should learn another skill after more repetitions
        assert len(learned_skills) > 0
    
    def test_tutorial_boost(self):
        """Test tutorial boost for first 5 skills"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 1,
            "creature_type": "Goblin",
            "current_abilities": [],
            "combat_victories": 0
        }
        
        # Tutorial boost should be active
        assert learning_system.tutorial_boost_remaining == 5
        
        # Learn first skill with tutorial boost
        learned_skills = learning_system.record_action("first_combat", context)
        assert len(learned_skills) > 0
        assert learning_system.tutorial_boost_remaining == 4
        
        # Continue learning skills with boost
        context["current_abilities"] = learned_skills
        for i in range(4):
            learned_skills = learning_system.record_action("first_exploration", context)
            if learned_skills:
                context["current_abilities"].extend(learned_skills)
        
        # Tutorial boost should be depleted
        assert learning_system.tutorial_boost_remaining == 0
    
    def test_specialization_paths(self):
        """Test specialization path locking system"""
        learning_system = SkillLearningSystem()
        
        # Initially no paths should be locked
        assert len(learning_system.locked_paths) == 0
        assert len(learning_system.chosen_specializations) == 0
        
        # Simulate learning a specialization skill
        learning_system._check_specialization_locks("Berserker's Wrath")
        
        # Should have chosen berserker path and locked tactical path
        assert SpecializationPath.BERSERKER in learning_system.chosen_specializations
        assert SpecializationPath.TACTICAL in learning_system.locked_paths
        
        # Test that locked skills can't be learned
        assert learning_system._is_skill_locked_by_specialization("Tactical Genius") == True
        assert learning_system._is_skill_locked_by_specialization("Berserker's Wrath") == False
    
    def test_skill_prerequisites(self):
        """Test skill prerequisite system"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 5,
            "creature_type": "Spider",
            "current_abilities": ["Combat Mastery"],  # Has one prerequisite
            "combat_victories": 10
        }
        
        # Should not be able to learn Combat Veteran without Battle Instinct
        available_skills = learning_system._filter_learnable_skills_enhanced(
            ["Combat Veteran"], context
        )
        assert "Combat Veteran" not in available_skills
        
        # Add the missing prerequisite
        context["current_abilities"].append("Battle Instinct")
        available_skills = learning_system._filter_learnable_skills_enhanced(
            ["Combat Veteran"], context
        )
        assert "Combat Veteran" in available_skills
    
    def test_progressive_scaling(self):
        """Test progressive difficulty scaling"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 1,
            "creature_type": "Slime",
            "current_abilities": [],
            "combat_victories": 0
        }
        
        # First skill in category should be easy
        required_1 = learning_system._calculate_required_repetitions(
            "combat", SkillTier.BASIC, context
        )
        
        # Simulate learning a skill in this category
        learning_system.skills_learned_by_category["combat"] = 1
        
        # Second skill should require more repetitions
        required_2 = learning_system._calculate_required_repetitions(
            "combat", SkillTier.BASIC, context
        )
        
        assert required_2 > required_1
        
        # Higher level should also increase requirements
        context["character_level"] = 5
        required_3 = learning_system._calculate_required_repetitions(
            "combat", SkillTier.BASIC, context
        )
        
        assert required_3 > required_2
    
    def test_enhanced_skill_hints(self):
        """Test enhanced skill learning hints"""
        learning_system = SkillLearningSystem()
        character = Character("Test", ["brave"], "warrior")
        character.set_creature_type("Spider")
        
        # Add some progress toward learning skills
        learning_system.action_counters["combat_action"] = 1
        learning_system.action_counters["explore_forest"] = 2
        
        hints = learning_system.get_skill_learning_hints(character, "Mysterious Forest")
        
        # Should have hints about skills close to being learned
        assert len(hints) > 0
        
        # Should show tutorial boost if active
        if learning_system.tutorial_boost_remaining > 0:
            tutorial_hints = [hint for hint in hints if "Tutorial Boost" in hint]
            assert len(tutorial_hints) > 0
    
    def test_prerequisite_hints(self):
        """Test prerequisite hints for advanced skills"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 3,
            "creature_type": "Goblin",
            "current_abilities": ["Combat Mastery"],  # Missing Battle Instinct for Combat Veteran
            "combat_victories": 5
        }
        
        hints = learning_system._get_prerequisite_hints(context)
        
        # Should suggest learning Battle Instinct to unlock Combat Veteran
        battle_instinct_hints = [hint for hint in hints if "Battle Instinct" in hint and "Combat Veteran" in hint]
        assert len(battle_instinct_hints) > 0
    
    def test_specialization_status(self):
        """Test specialization status reporting"""
        learning_system = SkillLearningSystem()
        
        # Add some specialization data
        learning_system.chosen_specializations.add(SpecializationPath.SHADOW)
        learning_system.locked_paths.add(SpecializationPath.GUARDIAN)
        learning_system.skills_learned_by_category["combat"] = 3
        learning_system.tutorial_boost_remaining = 2
        
        status = learning_system.get_specialization_status()
        
        assert "shadow" in status["chosen_paths"]
        assert "guardian" in status["locked_paths"]
        assert status["skills_by_category"]["combat"] == 3
        assert status["tutorial_boost_remaining"] == 2
        assert len(status["available_paths"]) == len(SpecializationPath) - 1  # All except locked
    
    def test_can_learn_skill(self):
        """Test skill learning eligibility checking"""
        learning_system = SkillLearningSystem()
        character = Character("Test", ["brave"], "warrior")
        character.set_creature_type("Spider")
        character.abilities = ["Combat Mastery", "Stealth"]
        
        # Should not be able to learn already known skills
        assert learning_system.can_learn_skill("Combat Mastery", character) == False
        
        # Should be able to learn new skills without prerequisites
        assert learning_system.can_learn_skill("Battle Instinct", character) == True
        
        # Should not be able to learn skills with missing prerequisites
        assert learning_system.can_learn_skill("Combat Veteran", character) == False
        
        # Add prerequisite and test again
        character.abilities.append("Battle Instinct")
        assert learning_system.can_learn_skill("Combat Veteran", character) == True
    
    def test_serialization_enhanced(self):
        """Test enhanced serialization with all new data"""
        learning_system = SkillLearningSystem()
        
        # Set up complex state
        learning_system.action_counters = {"combat_action": 3, "explore_forest": 2}
        learning_system.skills_learned_by_category = {"combat": 2, "exploration": 1}
        learning_system.tutorial_boost_remaining = 3
        learning_system.specialization_points = 5
        learning_system.chosen_specializations.add(SpecializationPath.BERSERKER)
        learning_system.locked_paths.add(SpecializationPath.TACTICAL)
        
        # Serialize
        data = learning_system.to_dict()
        
        # Verify all data is present
        assert data["action_counters"]["combat_action"] == 3
        assert data["skills_learned_by_category"]["combat"] == 2
        assert data["tutorial_boost_remaining"] == 3
        assert data["specialization_points"] == 5
        assert "berserker" in data["chosen_specializations"]
        assert "tactical" in data["locked_paths"]
        
        # Deserialize
        new_system = SkillLearningSystem.from_dict(data)
        
        # Verify all data is restored
        assert new_system.action_counters["combat_action"] == 3
        assert new_system.skills_learned_by_category["combat"] == 2
        assert new_system.tutorial_boost_remaining == 3
        assert new_system.specialization_points == 5
        assert SpecializationPath.BERSERKER in new_system.chosen_specializations
        assert SpecializationPath.TACTICAL in new_system.locked_paths
    
    def test_beginner_skill_prioritization(self):
        """Test that beginner skills are prioritized during tutorial"""
        learning_system = SkillLearningSystem()
        
        # Should prioritize beginner skills when tutorial boost is active
        available_skills = ["Combat Mastery", "Basic Strike", "Battle Instinct"]
        context = {"current_abilities": []}
        
        selected = learning_system._select_skill_to_learn(available_skills, context)
        assert selected == "Basic Strike"  # Should pick the beginner skill
        
        # After tutorial boost is depleted, should pick randomly
        learning_system.tutorial_boost_remaining = 0
        selected = learning_system._select_skill_to_learn(available_skills, context)
        assert selected in available_skills  # Should pick any available skill
