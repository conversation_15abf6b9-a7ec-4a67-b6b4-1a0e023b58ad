"""
Tests for the MemorySystem class
"""
import pytest
from game.memory_system import MemorySystem

class TestMemorySystem:
    def test_memory_initialization(self):
        """Test memory system initialization"""
        memory = MemorySystem()
        
        assert len(memory.short_term_memory) == 0
        assert len(memory.long_term_memory["important_events"]) == 0
        assert len(memory.long_term_memory["discovered_locations"]) == 0
        assert len(memory.long_term_memory["met_npcs"]) == 0
        assert memory.current_context["location"] == ""
    
    def test_short_term_memory(self):
        """Test short-term memory functionality"""
        memory = MemorySystem()
        
        # Add events
        memory.add_short_term_event("Event 1")
        memory.add_short_term_event("Event 2")
        
        assert len(memory.short_term_memory) == 2
        assert "Event 1" in memory.short_term_memory
        assert "Event 2" in memory.short_term_memory
        
        # Test memory limit (should be 15 by default)
        for i in range(20):
            memory.add_short_term_event(f"Event {i + 3}")
        
        assert len(memory.short_term_memory) == 15  # Should be capped at max
        assert "Event 1" not in memory.short_term_memory  # Should be evicted
    
    def test_important_events(self):
        """Test important event storage"""
        memory = MemorySystem()
        
        memory.add_important_event("Character reincarnated", "reincarnation")
        memory.add_important_event("First evolution", "evolution")
        
        events = memory.long_term_memory["important_events"]
        assert len(events) == 2
        assert events[0]["event"] == "Character reincarnated"
        assert events[0]["category"] == "reincarnation"
        assert events[1]["event"] == "First evolution"
        assert events[1]["category"] == "evolution"
    
    def test_location_discovery(self):
        """Test location discovery and tracking"""
        memory = MemorySystem()
        
        # Discover new location
        memory.discover_location("Forest Clearing", "A peaceful clearing in the woods")
        
        locations = memory.long_term_memory["discovered_locations"]
        assert len(locations) == 1
        assert locations[0]["name"] == "Forest Clearing"
        assert locations[0]["times_visited"] == 1
        
        # Visit same location again
        memory.discover_location("Forest Clearing", "A peaceful clearing in the woods")
        
        assert len(locations) == 1  # Should not duplicate
        assert locations[0]["times_visited"] == 2  # Should increment visit count
    
    def test_npc_interactions(self):
        """Test NPC meeting and relationship tracking"""
        memory = MemorySystem()
        
        # Meet new NPC
        memory.meet_npc("Goblin Chief", "A gruff but fair leader", 10)
        
        npcs = memory.long_term_memory["met_npcs"]
        assert "Goblin Chief" in npcs
        assert npcs["Goblin Chief"]["relationship"] == 10
        assert npcs["Goblin Chief"]["first_impression"] == "A gruff but fair leader"
        
        # Update relationship
        memory.update_npc_relationship("Goblin Chief", "Helped with quest", 15)
        
        assert npcs["Goblin Chief"]["relationship"] == 25
        assert len(npcs["Goblin Chief"]["interactions"]) == 1
        
        # Test relationship bounds
        memory.update_npc_relationship("Goblin Chief", "Major betrayal", -200)
        
        assert npcs["Goblin Chief"]["relationship"] == -100  # Should be clamped
    
    def test_quest_completion(self):
        """Test quest completion tracking"""
        memory = MemorySystem()
        
        memory.complete_quest("Find the Lost Gem", "Successfully retrieved the gem")
        memory.complete_quest("Defeat the Spider", "Spider defeated in combat")
        
        quests = memory.long_term_memory["completed_quests"]
        assert len(quests) == 2
        assert quests[0]["name"] == "Find the Lost Gem"
        assert quests[1]["name"] == "Defeat the Spider"
        assert quests[0]["completion_order"] == 0
        assert quests[1]["completion_order"] == 1
    
    def test_world_changes(self):
        """Test world change tracking"""
        memory = MemorySystem()
        
        memory.record_world_change("Forest fire destroyed eastern woods", "major")
        memory.record_world_change("New merchant arrived in town", "minor")
        
        changes = memory.long_term_memory["world_changes"]
        assert len(changes) == 2
        assert changes[0]["impact"] == "major"
        assert changes[1]["impact"] == "minor"
    
    def test_lore_learning(self):
        """Test lore acquisition"""
        memory = MemorySystem()
        
        memory.learn_lore("Dragons once ruled this land", "Ancient Tome")
        memory.learn_lore("The crystal caves hold great power", "Village Elder")
        
        lore = memory.long_term_memory["learned_lore"]
        assert len(lore) == 2
        assert lore[0]["source"] == "Ancient Tome"
        assert lore[1]["source"] == "Village Elder"
    
    def test_current_context_updates(self):
        """Test current context management"""
        memory = MemorySystem()
        
        memory.update_current_context(
            location="Dark Cave",
            active_npcs=["Bat Swarm"],
            immediate_threats=["Falling rocks"]
        )
        
        context = memory.current_context
        assert context["location"] == "Dark Cave"
        assert "Bat Swarm" in context["active_npcs"]
        assert "Falling rocks" in context["immediate_threats"]
    
    def test_memory_summaries(self):
        """Test memory summary generation"""
        memory = MemorySystem()
        
        # Add some events
        memory.add_short_term_event("Entered forest")
        memory.add_short_term_event("Met a friendly sprite")
        memory.add_short_term_event("Found a magical berry")
        
        summary = memory.get_recent_memory_summary(2)
        assert "Met a friendly sprite" in summary
        assert "Found a magical berry" in summary
        assert "Entered forest" not in summary  # Should only get last 2
    
    def test_location_context(self):
        """Test location context retrieval"""
        memory = MemorySystem()
        
        memory.discover_location("Mystic Grove", "A grove filled with magical energy")
        
        context = memory.get_location_context("Mystic Grove")
        assert "Mystic Grove" in context
        assert "1 time" in context
        assert "magical energy" in context
        
        # Test unknown location
        context = memory.get_location_context("Unknown Place")
        assert "haven't been" in context
    
    def test_npc_context(self):
        """Test NPC context retrieval"""
        memory = MemorySystem()
        
        memory.meet_npc("Wise Owl", "An ancient owl with deep knowledge", 30)
        
        context = memory.get_npc_context("Wise Owl")
        assert "Wise Owl" in context
        assert "friendly" in context  # Should describe relationship
        assert "ancient owl" in context
        
        # Test unknown NPC
        context = memory.get_npc_context("Unknown NPC")
        assert "haven't met" in context
    
    def test_serialization(self):
        """Test memory system serialization"""
        memory = MemorySystem()
        
        # Add some data
        memory.add_short_term_event("Test event")
        memory.add_important_event("Test important event")
        memory.discover_location("Test Location", "A test location")
        memory.meet_npc("Test NPC", "A test character")
        
        # Convert to dict
        memory_dict = memory.to_dict()
        
        # Create new memory from dict
        new_memory = MemorySystem.from_dict(memory_dict)
        
        assert len(new_memory.short_term_memory) == 1
        assert len(new_memory.long_term_memory["important_events"]) == 1
        assert len(new_memory.long_term_memory["discovered_locations"]) == 1
        assert len(new_memory.long_term_memory["met_npcs"]) == 1
