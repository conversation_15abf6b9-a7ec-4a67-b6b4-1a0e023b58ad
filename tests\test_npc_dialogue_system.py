"""
Unit tests for NPC and dialogue systems
"""
import pytest
from game.npc_system import NPCSystem, NPCData, NPCPersonality, NPCRole, RelationshipLevel
from game.dialogue_system import DialogueSystem, DialogueNode, DialogueChoice, DialogueCondition, DialogueConditionType, DialogueNodeType
from game.memory_system import MemorySystem
from game.character import Character

class TestNPCSystem:
    def test_npc_system_initialization(self):
        """Test NPC system initializes with NPCs"""
        npc_system = NPCSystem()
        
        assert len(npc_system.npcs) > 0
        assert "Elder Sprite" in npc_system.npcs
        assert "Lost Traveler" in npc_system.npcs
        assert "Dwarf Miner" in npc_system.npcs
        
    def test_get_npc(self):
        """Test getting NPC data"""
        npc_system = NPCSystem()
        
        elder_sprite = npc_system.get_npc("Elder Sprite")
        assert elder_sprite is not None
        assert elder_sprite.name == "Elder Sprite"
        assert elder_sprite.personality == NPCPersonality.WISE
        assert elder_sprite.role == NPCRole.TEACHER
        assert elder_sprite.location == "Mysterious Forest"
        
        # Test non-existent NPC
        unknown_npc = npc_system.get_npc("Unknown NPC")
        assert unknown_npc is None
    
    def test_get_npcs_in_location(self):
        """Test getting NPCs by location"""
        npc_system = NPCSystem()
        
        forest_npcs = npc_system.get_npcs_in_location("Mysterious Forest")
        assert len(forest_npcs) == 2
        npc_names = [npc.name for npc in forest_npcs]
        assert "Elder Sprite" in npc_names
        assert "Lost Traveler" in npc_names
        
        cave_npcs = npc_system.get_npcs_in_location("Crystal Caves")
        assert len(cave_npcs) == 2
        cave_npc_names = [npc.name for npc in cave_npcs]
        assert "Dwarf Miner" in cave_npc_names
        assert "Crystal Sage" in cave_npc_names
    
    def test_npc_availability(self):
        """Test NPC availability checking"""
        npc_system = NPCSystem()
        character = Character("Test", ["brave"], "Fighter")
        character.level = 5
        memory = MemorySystem()
        
        # Basic availability (should be true for most NPCs)
        assert npc_system.is_npc_available("Elder Sprite", character, memory)
        assert npc_system.is_npc_available("Lost Traveler", character, memory)
    
    def test_relationship_levels(self):
        """Test relationship level calculation"""
        npc_system = NPCSystem()
        
        assert npc_system.get_relationship_level(-75) == RelationshipLevel.ENEMY
        assert npc_system.get_relationship_level(-30) == RelationshipLevel.HOSTILE
        assert npc_system.get_relationship_level(-10) == RelationshipLevel.UNFRIENDLY
        assert npc_system.get_relationship_level(10) == RelationshipLevel.NEUTRAL
        assert npc_system.get_relationship_level(30) == RelationshipLevel.FRIENDLY
        assert npc_system.get_relationship_level(60) == RelationshipLevel.CLOSE
        assert npc_system.get_relationship_level(90) == RelationshipLevel.DEVOTED
    
    def test_relationship_change_calculation(self):
        """Test relationship change calculation"""
        npc_system = NPCSystem()
        
        # Test with Elder Sprite (likes respect for nature)
        change = npc_system.calculate_relationship_change(
            "Elder Sprite", "help protect the forest", {"helped_npc": True}
        )
        assert change > 0  # Should be positive
        
        # Test negative action
        change = npc_system.calculate_relationship_change(
            "Elder Sprite", "destroy trees", {"harmed_npc": True}
        )
        assert change < 0  # Should be negative

class TestDialogueSystem:
    def test_dialogue_system_initialization(self):
        """Test dialogue system initializes with dialogue trees"""
        dialogue_system = DialogueSystem()
        
        assert len(dialogue_system.dialogue_trees) > 0
        assert "Elder Sprite" in dialogue_system.dialogue_trees
        assert "Lost Traveler" in dialogue_system.dialogue_trees
        assert "Dwarf Miner" in dialogue_system.dialogue_trees
    
    def test_dialogue_tree_structure(self):
        """Test dialogue tree structure"""
        dialogue_system = DialogueSystem()
        
        elder_tree = dialogue_system.dialogue_trees["Elder Sprite"]
        assert elder_tree.npc_name == "Elder Sprite"
        assert elder_tree.root_node_id == "greeting"
        assert "greeting" in elder_tree.nodes
        
        greeting_node = elder_tree.nodes["greeting"]
        assert greeting_node.node_type == DialogueNodeType.GREETING
        assert len(greeting_node.choices) > 0
        assert greeting_node.npc_text != ""
    
    def test_start_conversation(self):
        """Test starting a conversation"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start conversation with Elder Sprite
        npc_response, choices = dialogue_system.start_conversation(
            "player", "Elder Sprite", character, memory
        )
        
        assert npc_response != ""
        assert len(choices) > 0
        assert "player" in dialogue_system.active_conversations
        
        # Check conversation state
        npc_name, node_id = dialogue_system.active_conversations["player"]
        assert npc_name == "Elder Sprite"
        assert node_id == "greeting"
    
    def test_dialogue_choice_processing(self):
        """Test processing dialogue choices"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start conversation
        dialogue_system.start_conversation("player", "Elder Sprite", character, memory)
        
        # Process first choice
        npc_response, new_choices, consequences = dialogue_system.process_dialogue_choice(
            "player", 0, character, memory
        )
        
        assert npc_response != ""
        assert isinstance(consequences, dict)
        
        # Check if conversation state updated
        if "player" in dialogue_system.active_conversations:
            npc_name, node_id = dialogue_system.active_conversations["player"]
            assert npc_name == "Elder Sprite"
            assert node_id != "greeting"  # Should have moved to a different node
    
    def test_dialogue_conditions(self):
        """Test dialogue condition evaluation"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        character.level = 10
        character.abilities = ["Nature Magic", "Healing"]
        memory = MemorySystem()
        
        # Test level condition
        level_condition = DialogueCondition(DialogueConditionType.LEVEL, "", 5, ">=")
        assert dialogue_system._evaluate_condition(level_condition, character, memory)
        
        level_condition_fail = DialogueCondition(DialogueConditionType.LEVEL, "", 15, ">=")
        assert not dialogue_system._evaluate_condition(level_condition_fail, character, memory)
        
        # Test skill condition
        skill_condition = DialogueCondition(DialogueConditionType.SKILL_KNOWN, "Nature Magic", True, "==")
        assert dialogue_system._evaluate_condition(skill_condition, character, memory)
        
        skill_condition_fail = DialogueCondition(DialogueConditionType.SKILL_KNOWN, "Fire Magic", True, "==")
        assert not dialogue_system._evaluate_condition(skill_condition_fail, character, memory)
    
    def test_relationship_conditions(self):
        """Test relationship-based dialogue conditions"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Set up NPC relationship
        memory.meet_npc("Elder Sprite", "A wise forest spirit", 25)
        
        # Test relationship condition
        rel_condition = DialogueCondition(DialogueConditionType.RELATIONSHIP, "Elder Sprite", 20, ">=")
        assert dialogue_system._evaluate_condition(rel_condition, character, memory)
        
        rel_condition_fail = DialogueCondition(DialogueConditionType.RELATIONSHIP, "Elder Sprite", 50, ">=")
        assert not dialogue_system._evaluate_condition(rel_condition_fail, character, memory)
    
    def test_dialogue_consequences(self):
        """Test dialogue choice consequences"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Create a test choice with consequences
        test_choice = DialogueChoice(
            text="Test choice",
            next_node_id="test_node",
            relationship_change=5,
            consequences={"learn_skill": "Test Skill", "gain_experience": 100}
        )
        
        # Test that consequences are returned
        assert test_choice.relationship_change == 5
        assert test_choice.consequences["learn_skill"] == "Test Skill"
        assert test_choice.consequences["gain_experience"] == 100
    
    def test_conversation_ending(self):
        """Test ending conversations"""
        dialogue_system = DialogueSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start conversation
        dialogue_system.start_conversation("player", "Elder Sprite", character, memory)
        assert "player" in dialogue_system.active_conversations
        
        # End conversation by going to end_conversation node
        dialogue_system.active_conversations["player"] = ("Elder Sprite", "farewell")
        
        # Process choice that leads to end
        npc_response, choices, consequences = dialogue_system.process_dialogue_choice(
            "player", 0, character, memory
        )
        
        # Conversation should end if farewell leads to end_conversation
        # (This depends on the specific dialogue tree structure)

class TestNPCDialogueIntegration:
    def test_npc_data_matches_dialogue_trees(self):
        """Test that NPC data matches available dialogue trees"""
        npc_system = NPCSystem()
        dialogue_system = DialogueSystem()
        
        # Check that every NPC has a dialogue tree
        for npc_name in npc_system.npcs.keys():
            assert npc_name in dialogue_system.dialogue_trees, f"No dialogue tree for {npc_name}"
        
        # Check that every dialogue tree has corresponding NPC data
        for tree_name in dialogue_system.dialogue_trees.keys():
            assert tree_name in npc_system.npcs, f"No NPC data for dialogue tree {tree_name}"
    
    def test_npc_personality_consistency(self):
        """Test that NPC personalities are consistent with dialogue"""
        npc_system = NPCSystem()
        dialogue_system = DialogueSystem()
        
        # Test Elder Sprite (wise personality)
        elder_sprite = npc_system.get_npc("Elder Sprite")
        elder_tree = dialogue_system.dialogue_trees["Elder Sprite"]
        
        assert elder_sprite.personality == NPCPersonality.WISE
        
        # Check that greeting reflects wise personality
        greeting_node = elder_tree.nodes["greeting"]
        greeting_text = greeting_node.npc_text.lower()
        
        # Wise characters should use words like "wisdom", "young one", etc.
        wise_indicators = ["wisdom", "young one", "ancient", "whisper"]
        assert any(indicator in greeting_text for indicator in wise_indicators)
    
    def test_location_consistency(self):
        """Test that NPCs are in correct locations"""
        npc_system = NPCSystem()
        
        # Test that forest NPCs are in forest
        forest_npcs = npc_system.get_npcs_in_location("Mysterious Forest")
        for npc in forest_npcs:
            assert npc.location == "Mysterious Forest"
        
        # Test that cave NPCs are in caves
        cave_npcs = npc_system.get_npcs_in_location("Crystal Caves")
        for npc in cave_npcs:
            assert npc.location == "Crystal Caves"
    
    def test_role_based_services(self):
        """Test that NPC roles match their available services"""
        npc_system = NPCSystem()
        
        # Teachers should offer teaching services
        elder_sprite = npc_system.get_npc("Elder Sprite")
        assert elder_sprite.role == NPCRole.TEACHER
        assert any("training" in service.lower() or "guidance" in service.lower() 
                  for service in elder_sprite.services)
        
        # Craftsmen should offer crafting services
        dwarf_miner = npc_system.get_npc("Dwarf Miner")
        assert dwarf_miner.role == NPCRole.CRAFTSMAN
        assert any("craft" in service.lower() or "tool" in service.lower() 
                  for service in dwarf_miner.services)
