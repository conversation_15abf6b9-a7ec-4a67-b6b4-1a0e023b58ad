"""
Integration tests for portrait system with game engine
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from game.game_engine import GameEngine
from game.character import Character
import config

class TestPortraitIntegration:
    """Test portrait system integration with game engine"""

    def setup_method(self):
        """Set up test fixtures"""
        self.game_engine = GameEngine()

    def test_game_engine_has_portrait_system(self):
        """Test that game engine initializes with portrait system"""
        assert hasattr(self.game_engine, 'portrait_system')
        assert self.game_engine.portrait_system is not None

    @pytest.mark.asyncio
    async def test_get_character_portrait_method(self):
        """Test the get_character_portrait method in game engine"""
        # Create a character first
        self.game_engine.character.name = "TestCharacter"
        self.game_engine.character.set_creature_type("Slime")
        
        with patch.object(self.game_engine.portrait_system, 'get_character_portrait') as mock_get:
            mock_get.return_value = Mock()
            
            result = await self.game_engine.get_character_portrait()
            
            assert result is not None
            mock_get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_character_portrait_no_character(self):
        """Test get_character_portrait when no character exists"""
        # No character name set
        result = await self.game_engine.get_character_portrait()
        assert result is None

    @patch('config.ENABLE_PORTRAIT_GENERATION', False)
    @pytest.mark.asyncio
    async def test_get_character_portrait_disabled(self):
        """Test get_character_portrait when generation is disabled"""
        self.game_engine.character.name = "TestCharacter"
        result = await self.game_engine.get_character_portrait()
        assert result is None

    def test_portrait_system_info(self):
        """Test getting portrait system information"""
        info = self.game_engine.get_portrait_system_info()
        
        assert 'cached_portraits' in info
        assert 'queue_length' in info
        assert 'is_generating' in info
        assert 'cache_enabled' in info
        assert 'generation_enabled' in info

    @patch('config.ENABLE_PORTRAIT_GENERATION', True)
    def test_character_creation_triggers_portrait_generation(self):
        """Test that character creation triggers portrait generation"""
        with patch.object(self.game_engine.portrait_system, 'preload_character_portraits') as mock_preload:
            # Simulate character creation completion
            self.game_engine.character.name = "TestSlime"
            self.game_engine.character.set_creature_type("Slime")
            
            # Manually trigger the portrait generation that would happen in character creation
            if config.ENABLE_PORTRAIT_GENERATION:
                character_data = self.game_engine.character.to_dict()
                self.game_engine.portrait_system.preload_character_portraits(character_data)
            
            mock_preload.assert_called_once()

    @patch('config.ENABLE_PORTRAIT_GENERATION', True)
    def test_evolution_triggers_portrait_generation(self):
        """Test that evolution triggers portrait generation"""
        with patch.object(self.game_engine.portrait_system, 'queue_portrait_generation') as mock_queue:
            # Set up character
            self.game_engine.character.name = "TestSlime"
            self.game_engine.character.set_creature_type("Slime")
            
            # Manually trigger the portrait generation that would happen during evolution
            if config.ENABLE_PORTRAIT_GENERATION:
                character_data = self.game_engine.character.to_dict()
                self.game_engine.portrait_system.queue_portrait_generation(character_data, "evolved", priority=1)
            
            mock_queue.assert_called_once()

    def test_portrait_system_with_different_creatures(self):
        """Test portrait system works with all creature types"""
        creature_types = ["Slime", "Spider", "Goblin", "Wisp", "Rat", "Mushroom"]
        
        for creature_type in creature_types:
            # Create character data
            character_data = {
                'name': f'Test{creature_type}',
                'creature_type': creature_type,
                'traits': ['Brave', 'Curious'],
                'occupation': 'Adventurer',
                'level': 1
            }
            
            # Test that portrait system can handle this creature type
            evolution_paths = self.game_engine.portrait_system._get_evolution_paths(creature_type)
            assert isinstance(evolution_paths, list)
            
            # Test appearance generation
            appearance = self.game_engine.portrait_system.imagen_client._get_creature_appearance(
                creature_type, "base"
            )
            assert isinstance(appearance, str)
            assert len(appearance) > 0

    def test_portrait_caching_integration(self):
        """Test that portrait caching works correctly"""
        portrait_system = self.game_engine.portrait_system
        
        # Test cache operations
        initial_cache_size = len(portrait_system.portrait_cache)
        
        # Add something to cache
        portrait_system.portrait_cache['test_key'] = Mock()
        assert len(portrait_system.portrait_cache) == initial_cache_size + 1
        
        # Test cache info
        cache_info = portrait_system.get_cache_info()
        assert cache_info['cached_portraits'] == initial_cache_size + 1
        
        # Test cache clearing
        portrait_system.clear_cache()
        assert len(portrait_system.portrait_cache) == 0

    def test_portrait_queue_integration(self):
        """Test that portrait generation queue works correctly"""
        portrait_system = self.game_engine.portrait_system
        
        # Test queue operations
        initial_queue_size = len(portrait_system.generation_queue)
        
        character_data = {
            'name': 'QueueTest',
            'creature_type': 'Slime',
            'traits': ['Brave'],
            'occupation': 'Tester',
            'level': 1
        }
        
        # Queue a portrait
        portrait_system.queue_portrait_generation(character_data, "base", priority=1)
        assert len(portrait_system.generation_queue) == initial_queue_size + 1
        
        # Test queue info
        cache_info = portrait_system.get_cache_info()
        assert cache_info['queue_length'] == initial_queue_size + 1

    @pytest.mark.asyncio
    async def test_full_character_creation_flow(self):
        """Test the complete character creation flow with portraits"""
        game_engine = GameEngine()
        
        # Mock the portrait system to avoid actual API calls
        with patch.object(game_engine.portrait_system, 'preload_character_portraits') as mock_preload:
            with patch.object(game_engine.gemini_client, 'generate_text') as mock_generate:
                mock_generate.return_value = "Welcome to your new life as a Slime!"
                
                # Simulate character creation steps
                game_engine.creation_stage = "creature"
                
                # Process creature selection
                response = await game_engine.process_character_creation("1")  # Choose Slime
                
                # Verify character was created
                assert game_engine.character.creature_type == "Slime"
                assert game_engine.state.value == "playing"
                
                # Verify portrait generation was triggered if enabled
                if config.ENABLE_PORTRAIT_GENERATION:
                    mock_preload.assert_called_once()

if __name__ == "__main__":
    pytest.main([__file__])
