"""
Tests for the Portrait System
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from game.portrait_system import PortraitSystem
from game.character import Character
import config

class TestPortraitSystem:
    """Test cases for the portrait system"""

    def setup_method(self):
        """Set up test fixtures"""
        self.portrait_system = PortraitSystem()
        self.test_character_data = {
            'name': 'TestSlime',
            'creature_type': 'Slime',
            'level': 1,
            'traits': ['Brave', 'Curious'],
            'occupation': 'Adventurer'
        }

    def test_portrait_system_initialization(self):
        """Test that portrait system initializes correctly"""
        assert self.portrait_system is not None
        assert self.portrait_system.imagen_client is not None
        assert self.portrait_system.portrait_cache == {}
        assert self.portrait_system.generation_queue == []
        assert not self.portrait_system.is_generating

    def test_get_evolution_paths(self):
        """Test getting evolution paths for different creatures"""
        slime_paths = self.portrait_system._get_evolution_paths("Slime")
        assert "Elemental Slime" in slime_paths
        assert "King Slime" in slime_paths
        assert "Mimic Slime" in slime_paths

        spider_paths = self.portrait_system._get_evolution_paths("Spider")
        assert "Arachne" in spider_paths
        assert "Widow Spider" in spider_paths
        assert "Phase Spider" in spider_paths

        # Test unknown creature
        unknown_paths = self.portrait_system._get_evolution_paths("Unknown")
        assert unknown_paths == []

    def test_cache_operations(self):
        """Test portrait cache operations"""
        # Test cache info
        cache_info = self.portrait_system.get_cache_info()
        assert 'cached_portraits' in cache_info
        assert 'queue_length' in cache_info
        assert 'is_generating' in cache_info
        assert 'cache_enabled' in cache_info
        assert 'generation_enabled' in cache_info

        # Test cache clearing
        self.portrait_system.portrait_cache['test'] = 'value'
        assert len(self.portrait_system.portrait_cache) == 1
        
        self.portrait_system.clear_cache()
        assert len(self.portrait_system.portrait_cache) == 0

    def test_queue_portrait_generation(self):
        """Test queuing portrait generation requests"""
        # Queue a portrait
        self.portrait_system.queue_portrait_generation(
            self.test_character_data, "base", priority=1
        )
        
        assert len(self.portrait_system.generation_queue) == 1
        assert self.portrait_system.generation_queue[0]['priority'] == 1

        # Queue another with higher priority
        self.portrait_system.queue_portrait_generation(
            self.test_character_data, "evolved", priority=0
        )
        
        assert len(self.portrait_system.generation_queue) == 2
        # Higher priority (lower number) should be first
        assert self.portrait_system.generation_queue[0]['priority'] == 0
        assert self.portrait_system.generation_queue[1]['priority'] == 1

    def test_preload_character_portraits(self):
        """Test preloading character portraits"""
        self.portrait_system.preload_character_portraits(self.test_character_data)
        
        # Should queue base portrait and evolution portraits
        assert len(self.portrait_system.generation_queue) > 1
        
        # Base portrait should have higher priority
        base_request = next(
            (req for req in self.portrait_system.generation_queue 
             if req['evolution_stage'] == 'base'), None
        )
        assert base_request is not None
        assert base_request['priority'] == 1

    @patch('config.ENABLE_PORTRAIT_GENERATION', False)
    def test_disabled_portrait_generation(self):
        """Test behavior when portrait generation is disabled"""
        result = asyncio.run(
            self.portrait_system.get_character_portrait(self.test_character_data)
        )
        assert result is None

    @patch('config.ENABLE_PORTRAIT_GENERATION', False)
    def test_disabled_preload(self):
        """Test preload when portrait generation is disabled"""
        self.portrait_system.preload_character_portraits(self.test_character_data)
        # Should not queue anything when disabled
        assert len(self.portrait_system.generation_queue) == 0

    @pytest.mark.asyncio
    async def test_get_character_portrait_with_cache(self):
        """Test getting character portrait with caching"""
        with patch.object(self.portrait_system.imagen_client, 'load_portrait') as mock_load:
            # Mock a cached portrait
            mock_image = Mock()
            mock_load.return_value = mock_image
            
            result = await self.portrait_system.get_character_portrait(
                self.test_character_data, "base"
            )
            
            assert result == mock_image
            mock_load.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_evolution_portraits(self):
        """Test generating portraits for evolution paths"""
        evolution_paths = ["Elemental Slime", "King Slime"]
        
        with patch.object(self.portrait_system, 'get_character_portrait') as mock_get:
            mock_get.return_value = Mock()
            
            result = await self.portrait_system.generate_evolution_portraits(
                self.test_character_data, evolution_paths
            )
            
            assert len(result) == 2
            assert "Elemental Slime" in result
            assert "King Slime" in result
            assert mock_get.call_count == 2

    def test_build_backstory_prompt(self):
        """Test building backstory prompts"""
        prompt = self.portrait_system._build_backstory_prompt("The Overworked Salaryman")
        assert "office worker" in prompt.lower()
        assert "dramatic lighting" in prompt.lower()
        
        # Test unknown scenario
        unknown_prompt = self.portrait_system._build_backstory_prompt("Unknown Scenario")
        assert "dramatic scene" in unknown_prompt.lower()

    @pytest.mark.asyncio
    async def test_backstory_illustration_disabled(self):
        """Test backstory illustration when generation is disabled"""
        with patch('config.ENABLE_PORTRAIT_GENERATION', False):
            result = await self.portrait_system.generate_backstory_illustration("Test Scenario")
            assert result is None

    def test_portrait_system_integration_with_character(self):
        """Test portrait system integration with character data"""
        # Create a test character
        character = Character()
        character.name = "TestCharacter"
        character.set_creature_type("Slime")
        character.traits = ["Brave", "Intelligent"]
        character.occupation = "Scholar"
        
        character_data = character.to_dict()
        
        # Test that character data is properly formatted for portrait generation
        assert 'name' in character_data
        assert 'creature_type' in character_data
        assert 'traits' in character_data
        assert 'occupation' in character_data
        
        # Test preloading with real character data
        self.portrait_system.preload_character_portraits(character_data)
        assert len(self.portrait_system.generation_queue) > 0

if __name__ == "__main__":
    pytest.main([__file__])
