"""
Unit tests for the quest system
"""
import pytest
from game.quest_system import QuestSys<PERSON>, Quest, QuestObjective, QuestReward, QuestBranch, QuestType, QuestStatus, ObjectiveType
from game.memory_system import MemorySystem
from game.character import Character

class TestQuestObjective:
    def test_objective_creation(self):
        """Test quest objective creation"""
        objective = QuestObjective(
            id="test_obj",
            objective_type=ObjectiveType.COLLECT,
            description="Collect 5 items",
            target="test_item",
            required_progress=5
        )
        
        assert objective.id == "test_obj"
        assert objective.objective_type == ObjectiveType.COLLECT
        assert objective.current_progress == 0
        assert objective.required_progress == 5
        assert not objective.is_complete()
        
    def test_objective_progress(self):
        """Test objective progress tracking"""
        objective = QuestObjective(
            id="test_obj",
            objective_type=ObjectiveType.KILL,
            description="Defeat 3 enemies",
            target="enemy",
            required_progress=3
        )
        
        # Test progress updates
        objective.current_progress = 1
        assert not objective.is_complete()
        assert objective.get_progress_text() == "1/3"
        
        objective.current_progress = 3
        assert objective.is_complete()
        assert objective.get_progress_text() == "3/3"
        
    def test_hidden_objective(self):
        """Test hidden objective behavior"""
        objective = QuestObjective(
            id="hidden_obj",
            objective_type=ObjectiveType.CUSTOM,
            description="Secret objective",
            target="secret",
            hidden=True
        )
        
        # Hidden objectives show ??? when no progress
        assert objective.get_progress_text() == "???"
        
        # Once progress is made, they show normally
        objective.current_progress = 1
        assert objective.get_progress_text() == "1/1"

class TestQuestReward:
    def test_reward_creation(self):
        """Test quest reward creation"""
        reward = QuestReward(
            experience=100,
            items=["Sword", "Shield"],
            skills=["Combat"],
            relationship_changes={"NPC1": 10}
        )
        
        assert reward.experience == 100
        assert "Sword" in reward.items
        assert "Combat" in reward.skills
        assert reward.relationship_changes["NPC1"] == 10

class TestQuest:
    def test_quest_creation(self):
        """Test quest creation"""
        quest = Quest(
            id="test_quest",
            name="Test Quest",
            description="A test quest",
            quest_type=QuestType.SIDE_QUEST,
            giver_npc="Test NPC",
            location="Test Location"
        )
        
        assert quest.id == "test_quest"
        assert quest.name == "Test Quest"
        assert quest.quest_type == QuestType.SIDE_QUEST
        assert quest.status == QuestStatus.NOT_STARTED
        
    def test_quest_availability(self):
        """Test quest availability checking"""
        quest = Quest(
            id="test_quest",
            name="Test Quest",
            description="A test quest",
            quest_type=QuestType.SIDE_QUEST,
            giver_npc="Test NPC",
            location="Test Location",
            level_requirement=5,
            prerequisite_skills=["Magic"],
            prerequisite_relationships={"NPC1": 20}
        )
        
        character = Character("Test", ["brave"], "Fighter")
        character.level = 3  # Below requirement
        memory = MemorySystem()
        
        # Should not be available due to level
        assert not quest.is_available(character, memory)
        
        # Meet level requirement
        character.level = 5
        assert not quest.is_available(character, memory)  # Still missing skill
        
        # Add required skill
        character.abilities.append("Magic")
        assert not quest.is_available(character, memory)  # Still missing relationship
        
        # Add required relationship
        memory.meet_npc("NPC1", "Test NPC", 25)
        assert quest.is_available(character, memory)  # Now available
        
    def test_quest_objectives(self):
        """Test quest objective management"""
        quest = Quest(
            id="test_quest",
            name="Test Quest",
            description="A test quest",
            quest_type=QuestType.EXPLORATION,
            giver_npc="Test NPC",
            location="Test Location",
            objectives=[
                QuestObjective("obj1", ObjectiveType.VISIT, "Visit location", "place1"),
                QuestObjective("obj2", ObjectiveType.COLLECT, "Collect items", "item1", required_progress=3),
                QuestObjective("obj3", ObjectiveType.TALK_TO, "Talk to NPC", "npc1", optional=True)
            ]
        )
        
        # Test active objectives
        active = quest.get_active_objectives()
        assert len(active) == 3
        
        # Complete first objective
        quest.objectives[0].current_progress = 1
        active = quest.get_active_objectives()
        assert len(active) == 2  # One less active
        
        # Test progress summary
        summary = quest.get_progress_summary()
        assert "Collect items" in summary
        assert "Talk to NPC" in summary

class TestQuestBranch:
    def test_quest_branching(self):
        """Test quest branching functionality"""
        branch = QuestBranch(
            id="test_branch",
            name="Test Branch",
            description="A test branch",
            condition="test_condition",
            objectives=[
                QuestObjective("branch_obj", ObjectiveType.CUSTOM, "Branch objective", "target")
            ]
        )
        
        quest = Quest(
            id="branching_quest",
            name="Branching Quest",
            description="A quest with branches",
            quest_type=QuestType.MAIN_STORY,
            giver_npc="Test NPC",
            location="Test Location",
            branches=[branch]
        )
        
        # Initially no branch active
        assert quest.current_branch is None
        
        # Activate branch
        quest.current_branch = "test_branch"
        assert quest.current_branch == "test_branch"
        
        # Test branch objectives
        active = quest.get_active_objectives()
        assert len(active) == 1
        assert active[0].id == "branch_obj"

class TestQuestSystem:
    def test_quest_system_initialization(self):
        """Test quest system initialization"""
        quest_system = QuestSystem()
        
        assert len(quest_system.quest_database) > 0
        assert "map_mysterious_forest" in quest_system.quest_database
        assert "clear_blocked_tunnel" in quest_system.quest_database
        
    def test_get_quest(self):
        """Test getting quest by ID"""
        quest_system = QuestSystem()
        
        quest = quest_system.get_quest("map_mysterious_forest")
        assert quest is not None
        assert quest.name == "Map the Mysterious Forest"
        assert quest.giver_npc == "Lost Traveler"
        
        # Test non-existent quest
        unknown_quest = quest_system.get_quest("unknown_quest")
        assert unknown_quest is None
        
    def test_get_available_quests(self):
        """Test getting available quests"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        character.level = 1
        memory = MemorySystem()
        
        # Get available quests
        available = quest_system.get_available_quests(character, memory)
        assert len(available) > 0
        
        # Check that low-level quests are available
        quest_names = [q.name for q in available]
        assert "Map the Mysterious Forest" in quest_names
        
    def test_start_quest(self):
        """Test starting a quest"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        character.level = 1
        memory = MemorySystem()
        
        # Start a quest
        success = quest_system.start_quest("map_mysterious_forest", character, memory)
        assert success
        
        # Check quest is now active
        assert "map_mysterious_forest" in quest_system.active_quests
        quest = quest_system.active_quests["map_mysterious_forest"]
        assert quest.status == QuestStatus.ACTIVE
        
        # Try to start same quest again (should fail)
        success = quest_system.start_quest("map_mysterious_forest", character, memory)
        assert not success
        
    def test_quest_progress_update(self):
        """Test updating quest progress"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start quest
        quest_system.start_quest("map_mysterious_forest", character, memory)
        
        # Update progress
        success = quest_system.update_quest_progress("map_mysterious_forest", "explore_forest_areas", 1)
        assert success
        
        # Check progress was updated
        quest = quest_system.active_quests["map_mysterious_forest"]
        objective = next(obj for obj in quest.objectives if obj.id == "explore_forest_areas")
        assert objective.current_progress == 1
        
    def test_quest_completion(self):
        """Test quest completion"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start quest
        quest_system.start_quest("map_mysterious_forest", character, memory)
        quest = quest_system.active_quests["map_mysterious_forest"]
        
        # Complete all objectives
        for objective in quest.objectives:
            objective.current_progress = objective.required_progress
        
        # Check if quest is complete
        is_complete = quest_system.check_quest_completion("map_mysterious_forest")
        assert is_complete
        
        # Complete the quest
        rewards = quest_system.complete_quest("map_mysterious_forest", character, memory)
        assert rewards.experience > 0
        assert len(rewards.skills) > 0
        
        # Check quest moved to completed
        assert "map_mysterious_forest" not in quest_system.active_quests
        assert "map_mysterious_forest" in quest_system.completed_quests
        
    def test_quest_branching_system(self):
        """Test quest branching in the system"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # Start a quest with branches
        quest_system.start_quest("map_mysterious_forest", character, memory)
        
        # Trigger a branch
        success = quest_system.trigger_quest_branch("map_mysterious_forest", "found_forest_exit")
        assert success
        
        quest = quest_system.active_quests["map_mysterious_forest"]
        assert quest.current_branch == "help_escape"
        
    def test_quest_summaries(self):
        """Test quest summary generation"""
        quest_system = QuestSystem()
        character = Character("Test", ["brave"], "Fighter")
        memory = MemorySystem()
        
        # No active quests initially
        summaries = quest_system.get_active_quests_summary()
        assert len(summaries) == 0
        
        # Start a quest
        quest_system.start_quest("map_mysterious_forest", character, memory)
        
        # Get summaries
        summaries = quest_system.get_active_quests_summary()
        assert len(summaries) == 1
        assert "Map the Mysterious Forest" in summaries
        
        # Check summary content
        summary = summaries["Map the Mysterious Forest"]
        assert "Explore different areas" in summary
        assert "Find and document" in summary

class TestQuestIntegration:
    def test_quest_npc_integration(self):
        """Test that quests are properly linked to NPCs"""
        quest_system = QuestSystem()
        
        # Check that quest givers exist
        forest_quest = quest_system.get_quest("map_mysterious_forest")
        assert forest_quest.giver_npc == "Lost Traveler"
        
        cave_quest = quest_system.get_quest("clear_blocked_tunnel")
        assert cave_quest.giver_npc == "Dwarf Miner"
        
    def test_quest_location_consistency(self):
        """Test that quests are in appropriate locations"""
        quest_system = QuestSystem()
        
        forest_quest = quest_system.get_quest("map_mysterious_forest")
        assert forest_quest.location == "Mysterious Forest"
        
        cave_quest = quest_system.get_quest("clear_blocked_tunnel")
        assert cave_quest.location == "Crystal Caves"
        
    def test_quest_progression_logic(self):
        """Test quest progression and prerequisites"""
        quest_system = QuestSystem()
        
        # Check prerequisite chain
        delivery_quest = quest_system.get_quest("deliver_message_to_family")
        assert "map_mysterious_forest" in delivery_quest.prerequisite_quests
        
        # Check level requirements are reasonable
        for quest in quest_system.quest_database.values():
            assert quest.level_requirement >= 1
            assert quest.level_requirement <= 10  # Reasonable for beta
