"""
Tests for the restructured lore integration system
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from game.lore_system import LoreSystem, BackstoryScenario
from game.character import Character
from game.game_engine import GameEngine


class TestBackstoryScenario:
    """Test the enhanced BackstoryScenario dataclass"""

    def test_backstory_scenario_creation(self):
        """Test creating a backstory scenario with all new fields"""
        scenario = BackstoryScenario(
            name="Test Death",
            description="Test description",
            death_scene="Test death scene",
            transition_scene="Test transition",
            reincarnation_explanation="Test explanation",
            suggested_traits=["brave", "smart"],
            suggested_occupations=["warrior", "scholar"],
            recommended_creatures=["Slime", "Goblin"],
            stat_bonuses={"hp": 5, "mp": 3},
            personality_keywords=["heroic", "intelligent"],
            thematic_elements=["courage", "wisdom"]
        )

        assert scenario.name == "Test Death"
        assert scenario.recommended_creatures == ["Slime", "Goblin"]
        assert scenario.stat_bonuses == {"hp": 5, "mp": 3}
        assert scenario.personality_keywords == ["heroic", "intelligent"]
        assert scenario.thematic_elements == ["courage", "wisdom"]


class TestLoreSystem:
    """Test the enhanced LoreSystem functionality"""

    def setup_method(self):
        """Setup for each test"""
        self.lore_system = LoreSystem()

    def test_enhanced_backstory_scenarios(self):
        """Test that all backstory scenarios have the new fields"""
        for scenario in self.lore_system.backstory_scenarios:
            assert hasattr(scenario, 'recommended_creatures')
            assert hasattr(scenario, 'stat_bonuses')
            assert hasattr(scenario, 'personality_keywords')
            assert hasattr(scenario, 'thematic_elements')

            # Verify they're not empty
            assert len(scenario.recommended_creatures) > 0
            assert len(scenario.stat_bonuses) > 0
            assert len(scenario.personality_keywords) > 0
            assert len(scenario.thematic_elements) > 0

    def test_trait_synergy_validation(self):
        """Test trait synergy validation system"""
        gaming_backstory = self.lore_system.get_backstory_by_name("Gaming Accident")
        assert gaming_backstory is not None

        # Test perfect match
        perfect_traits = ["strategic", "persistent", "competitive", "analytical"]
        synergy = self.lore_system.validate_trait_synergy(gaming_backstory, perfect_traits)
        assert synergy['synergy_score'] == 1.0
        assert synergy['narrative_bonus'] is True
        assert "Perfect harmony" in synergy['bonus_description']

        # Test partial match
        partial_traits = ["strategic", "friendly"]
        synergy = self.lore_system.validate_trait_synergy(gaming_backstory, partial_traits)
        assert 0 < synergy['synergy_score'] < 1.0

        # Test no match
        no_match_traits = ["peaceful", "artistic"]
        synergy = self.lore_system.validate_trait_synergy(gaming_backstory, no_match_traits)
        assert synergy['synergy_score'] == 0.0
        assert synergy['narrative_bonus'] is False

    def test_occupation_synergy_validation(self):
        """Test occupation synergy validation system"""
        gaming_backstory = self.lore_system.get_backstory_by_name("Gaming Accident")
        assert gaming_backstory is not None

        # Test perfect match
        synergy = self.lore_system.validate_occupation_synergy(gaming_backstory, "programmer")
        assert synergy['is_perfect_match'] is True
        assert synergy['narrative_bonus'] is True

        # Test partial match (using a more realistic partial match)
        synergy = self.lore_system.validate_occupation_synergy(gaming_backstory, "game developer")
        assert synergy['is_partial_match'] is True

        # Test no match
        synergy = self.lore_system.validate_occupation_synergy(gaming_backstory, "chef")
        assert synergy['is_perfect_match'] is False
        assert synergy['is_partial_match'] is False

    def test_ai_context_generation(self):
        """Test AI context generation for backstories"""
        overwork_backstory = self.lore_system.get_backstory_by_name("Overwork Death")
        assert overwork_backstory is not None

        context = self.lore_system.get_backstory_context_for_ai(overwork_backstory)
        assert "BACKSTORY CONTEXT:" in context
        assert "Overwork Death" in context
        assert "methodical" in context  # personality keyword
        assert "work ethic" in context  # thematic element


class TestCharacterEnhancements:
    """Test character class enhancements for backstory integration"""

    def test_character_backstory_fields(self):
        """Test that character has new backstory fields"""
        character = Character()
        assert hasattr(character, 'backstory_name')
        assert hasattr(character, 'backstory_synergy_bonuses')
        assert character.backstory_name == ""
        assert character.backstory_synergy_bonuses == {}

    def test_character_serialization(self):
        """Test that backstory data is properly serialized"""
        character = Character()
        character.backstory_name = "Gaming Accident"
        character.backstory_synergy_bonuses = {
            "trait_synergy": {"synergy_score": 0.8},
            "stat_bonuses": {"attack": 3, "speed": 5}
        }

        # Test to_dict
        data = character.to_dict()
        assert data['backstory_name'] == "Gaming Accident"
        assert data['backstory_synergy_bonuses'] == character.backstory_synergy_bonuses

        # Test from_dict
        new_character = Character.from_dict(data)
        assert new_character.backstory_name == "Gaming Accident"
        assert new_character.backstory_synergy_bonuses == character.backstory_synergy_bonuses


class TestGameEngineIntegration:
    """Test game engine integration with restructured lore system"""

    def setup_method(self):
        """Setup for each test"""
        self.engine = GameEngine()

    @pytest.mark.asyncio
    async def test_enhanced_character_creation_flow(self):
        """Test the enhanced character creation flow with new order"""
        # Mock the Gemini client to avoid API calls
        with patch.object(self.engine.gemini_client, 'generate_character_creation_response',
                         new_callable=AsyncMock) as mock_gemini:
            mock_gemini.return_value = "Mocked response"

            # Start new game (now starts with name)
            await self.engine.start_new_game()
            assert self.engine.creation_stage == "name"

            # Enter name
            await self.engine.process_character_creation("TestName")
            assert self.engine.character.name == "TestName"
            assert self.engine.creation_stage == "traits"

            # Enter traits
            await self.engine.process_character_creation("brave, smart")
            assert self.engine.character.traits == ["brave", "smart"]
            assert self.engine.creation_stage == "occupation"

            # Enter occupation
            await self.engine.process_character_creation("warrior")
            assert self.engine.character.occupation == "warrior"
            assert self.engine.creation_stage == "creature"

            # Select creature
            await self.engine.process_character_creation("1")  # Select first creature
            assert self.engine.character.creature_type != ""
            assert self.engine.creation_stage == "backstory"

            # Select backstory (now comes last)
            await self.engine.process_character_creation("1")  # Select first backstory
            assert self.engine.selected_backstory is not None
            assert self.engine.character.backstory_name == self.engine.selected_backstory.name
            assert "trait_synergy" in self.engine.character.backstory_synergy_bonuses
            assert "occupation_synergy" in self.engine.character.backstory_synergy_bonuses

    @pytest.mark.asyncio
    async def test_stat_bonus_application(self):
        """Test that backstory stat bonuses are properly applied"""
        with patch.object(self.engine.gemini_client, 'generate_character_creation_response',
                         new_callable=AsyncMock) as mock_gemini:
            mock_gemini.return_value = "Mocked response"

            # Complete character creation with new flow
            await self.engine.start_new_game()
            await self.engine.process_character_creation("TestName")
            await self.engine.process_character_creation("brave, smart")
            await self.engine.process_character_creation("warrior")
            await self.engine.process_character_creation("1")  # Select creature

            # Get backstory bonuses before final selection
            first_backstory = self.engine.lore_system.backstory_scenarios[0]
            backstory_bonuses = self.engine.lore_system.get_stat_bonuses(first_backstory)

            # Select backstory (final step)
            await self.engine.process_character_creation("1")  # Select first backstory

            # Verify stat bonuses were applied
            assert "stat_bonuses" in self.engine.character.backstory_synergy_bonuses
            assert self.engine.character.backstory_synergy_bonuses["stat_bonuses"] == backstory_bonuses

            # Verify actual stats include bonuses
            for stat, bonus in backstory_bonuses.items():
                if stat in self.engine.character.stats:
                    # The stat should be base creature stat + bonus
                    assert self.engine.character.stats[stat] >= bonus

    @pytest.mark.asyncio
    async def test_backstory_recommendation_system(self):
        """Test the new backstory recommendation system"""
        with patch.object(self.engine.gemini_client, 'generate_character_creation_response',
                         new_callable=AsyncMock) as mock_gemini:
            mock_gemini.return_value = "Mocked response"

            # Set up character that should match Gaming Accident backstory
            await self.engine.start_new_game()
            await self.engine.process_character_creation("TestGamer")
            await self.engine.process_character_creation("strategic, competitive, analytical")
            await self.engine.process_character_creation("programmer")
            await self.engine.process_character_creation("2")  # Select Spider (recommended for Gaming Accident)

            # Get recommendations
            recommendations = self.engine._get_recommended_backstories()

            # Gaming Accident should be recommended due to:
            # - Traits: strategic, analytical (match personality keywords)
            # - Occupation: programmer (perfect match)
            # - Creature: Spider (in recommended creatures)
            assert "Gaming Accident" in recommendations

            # Test different character profile
            engine2 = GameEngine()
            await engine2.start_new_game()
            await engine2.process_character_creation("TestHero")
            await engine2.process_character_creation("brave, selfless, protective")
            await engine2.process_character_creation("firefighter")
            await engine2.process_character_creation("1")  # Select Slime

            recommendations2 = engine2._get_recommended_backstories()

            # Heroic Sacrifice should be recommended due to trait and occupation matches
            assert "Heroic Sacrifice" in recommendations2


if __name__ == "__main__":
    pytest.main([__file__])
