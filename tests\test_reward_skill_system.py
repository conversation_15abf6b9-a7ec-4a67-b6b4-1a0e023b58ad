"""
Unit tests for the Reward Skill System
"""
import unittest
from unittest.mock import Mock, patch
from game.reward_skill_system import RewardSkillSystem, RewardCategory, RewardCriteria
from game.skill_system import SkillType, SkillRarity
from game.character import Character


class TestRewardSkillSystem(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures"""
        self.reward_system = RewardSkillSystem()
        self.character = Character("TestHero", ["brave"], "warrior")
        self.character.set_creature_type("Slime")

    def test_initialization(self):
        """Test reward system initialization"""
        # Check that criteria are loaded
        self.assertGreater(len(self.reward_system.reward_criteria), 0)
        self.assertGreater(len(self.reward_system.reward_skills), 0)
        
        # Check specific criteria exist
        self.assertIn("First Evolution", self.reward_system.reward_criteria)
        self.assertIn("Monster Hunter", self.reward_system.reward_criteria)
        self.assertIn("Survivor", self.reward_system.reward_criteria)
        
        # Check progress tracking is initialized
        self.assertEqual(self.reward_system.player_progress["combat_victories"], 0)
        self.assertEqual(self.reward_system.player_progress["days_survived"], 0)
        self.assertIsInstance(self.reward_system.player_progress["unique_enemies_defeated"], set)

    def test_story_progression_skills(self):
        """Test story progression reward skills"""
        # Test First Evolution skill
        first_evo_criteria = self.reward_system.reward_criteria["First Evolution"]
        self.assertEqual(first_evo_criteria.category, RewardCategory.STORY_PROGRESSION)
        self.assertEqual(first_evo_criteria.target_value, 1)
        self.assertEqual(first_evo_criteria.tracking_key, "evolution_count")
        
        # Test Survivor skill
        survivor_criteria = self.reward_system.reward_criteria["Survivor"]
        self.assertEqual(survivor_criteria.category, RewardCategory.STORY_PROGRESSION)
        self.assertEqual(survivor_criteria.target_value, 10)
        self.assertEqual(survivor_criteria.tracking_key, "days_survived")

    def test_quest_completion_skills(self):
        """Test quest completion reward skills"""
        # Test Monster Hunter skill
        hunter_criteria = self.reward_system.reward_criteria["Monster Hunter"]
        self.assertEqual(hunter_criteria.category, RewardCategory.QUEST_COMPLETION)
        self.assertEqual(hunter_criteria.target_value, 5)
        self.assertEqual(hunter_criteria.tracking_key, "unique_enemies_defeated")
        
        # Test Explorer skill
        explorer_criteria = self.reward_system.reward_criteria["Explorer"]
        self.assertEqual(explorer_criteria.category, RewardCategory.QUEST_COMPLETION)
        self.assertEqual(explorer_criteria.target_value, 3)
        self.assertEqual(explorer_criteria.tracking_key, "discovered_locations")

    def test_relationship_growth_skills(self):
        """Test relationship growth reward skills"""
        # Test Trusted Ally skill
        ally_criteria = self.reward_system.reward_criteria["Trusted Ally"]
        self.assertEqual(ally_criteria.category, RewardCategory.RELATIONSHIP_GROWTH)
        self.assertEqual(ally_criteria.target_value, 75)
        self.assertEqual(ally_criteria.tracking_key, "max_npc_relationship")

    def test_achievement_based_skills(self):
        """Test achievement-based reward skills"""
        # Test Glutton skill
        glutton_criteria = self.reward_system.reward_criteria["Glutton"]
        self.assertEqual(glutton_criteria.category, RewardCategory.ACHIEVEMENT_BASED)
        self.assertEqual(glutton_criteria.target_value, 50)
        self.assertEqual(glutton_criteria.tracking_key, "unique_foods_eaten")
        
        # Test Hoarder skill
        hoarder_criteria = self.reward_system.reward_criteria["Hoarder"]
        self.assertEqual(hoarder_criteria.category, RewardCategory.ACHIEVEMENT_BASED)
        self.assertEqual(hoarder_criteria.target_value, 100)
        self.assertEqual(hoarder_criteria.tracking_key, "total_items_collected")

    def test_update_progress_counter(self):
        """Test updating progress with counter operations"""
        # Test increment operation
        newly_earned = self.reward_system.update_progress("combat_victories", 5, "increment")
        self.assertEqual(self.reward_system.player_progress["combat_victories"], 5)
        self.assertEqual(len(newly_earned), 0)  # No rewards earned yet
        
        # Test set operation
        newly_earned = self.reward_system.update_progress("combat_victories", 25, "set")
        self.assertEqual(self.reward_system.player_progress["combat_victories"], 25)
        self.assertIn("Champion", newly_earned)  # Should earn Champion skill

    def test_update_progress_collection(self):
        """Test updating progress with collection operations"""
        # Add enemies to defeated set
        self.reward_system.update_progress("unique_enemies_defeated", "Wolf", "add_to_set")
        self.reward_system.update_progress("unique_enemies_defeated", "Spider", "add_to_set")
        self.reward_system.update_progress("unique_enemies_defeated", "Goblin", "add_to_set")
        self.reward_system.update_progress("unique_enemies_defeated", "Rat", "add_to_set")
        
        # Should not earn Monster Hunter yet (need 5)
        self.assertEqual(len(self.reward_system.player_progress["unique_enemies_defeated"]), 4)
        self.assertNotIn("Monster Hunter", self.reward_system.player_progress["earned_rewards"])
        
        # Add fifth enemy - should earn Monster Hunter
        newly_earned = self.reward_system.update_progress("unique_enemies_defeated", "Slime", "add_to_set")
        self.assertIn("Monster Hunter", newly_earned)

    def test_update_progress_max(self):
        """Test updating progress with max operation"""
        # Set initial relationship value
        self.reward_system.update_progress("max_npc_relationship", 50, "max")
        self.assertEqual(self.reward_system.player_progress["max_npc_relationship"], 50)
        
        # Try to set lower value - should not change
        self.reward_system.update_progress("max_npc_relationship", 30, "max")
        self.assertEqual(self.reward_system.player_progress["max_npc_relationship"], 50)
        
        # Set higher value - should earn Trusted Ally
        newly_earned = self.reward_system.update_progress("max_npc_relationship", 80, "max")
        self.assertEqual(self.reward_system.player_progress["max_npc_relationship"], 80)
        self.assertIn("Trusted Ally", newly_earned)

    def test_criteria_checking(self):
        """Test criteria checking logic"""
        # Test counter criteria
        counter_criteria = RewardCriteria(
            RewardCategory.STORY_PROGRESSION,
            "Test Skill",
            "Test description",
            "counter",
            10,
            "test_counter"
        )
        
        self.reward_system.player_progress["test_counter"] = 5
        self.assertFalse(self.reward_system._check_criteria_met(counter_criteria))
        
        self.reward_system.player_progress["test_counter"] = 10
        self.assertTrue(self.reward_system._check_criteria_met(counter_criteria))
        
        # Test collection criteria
        collection_criteria = RewardCriteria(
            RewardCategory.ACHIEVEMENT_BASED,
            "Test Collection",
            "Test description",
            "collection",
            3,
            "test_collection"
        )
        
        self.reward_system.player_progress["test_collection"] = {"item1", "item2"}
        self.assertFalse(self.reward_system._check_criteria_met(collection_criteria))
        
        self.reward_system.player_progress["test_collection"].add("item3")
        self.assertTrue(self.reward_system._check_criteria_met(collection_criteria))

    def test_reward_skill_properties(self):
        """Test reward skill properties"""
        # Test First Evolution skill
        first_evo_skill = self.reward_system.get_reward_skill("First Evolution")
        self.assertIsNotNone(first_evo_skill)
        self.assertEqual(first_evo_skill.skill_type, SkillType.PASSIVE)
        self.assertEqual(first_evo_skill.rarity, SkillRarity.RARE)
        self.assertIn("evolution_speed_boost", first_evo_skill.effects)
        
        # Test Monster Hunter skill
        hunter_skill = self.reward_system.get_reward_skill("Monster Hunter")
        self.assertIsNotNone(hunter_skill)
        self.assertEqual(hunter_skill.skill_type, SkillType.COMBAT)
        self.assertEqual(hunter_skill.rarity, SkillRarity.ADVANCED)
        self.assertIn("new_enemy_crit_bonus", hunter_skill.effects)

    def test_progress_summary(self):
        """Test progress summary generation"""
        # Set some progress
        self.reward_system.update_progress("combat_victories", 15, "set")
        self.reward_system.update_progress("days_survived", 5, "set")
        
        summary = self.reward_system.get_progress_summary()
        
        # Check Champion progress (needs 25 victories)
        champion_progress = summary["Champion"]
        self.assertEqual(champion_progress["status"], "in_progress")
        self.assertEqual(champion_progress["current"], 15)
        self.assertEqual(champion_progress["target"], 25)
        self.assertEqual(champion_progress["progress_percent"], 60.0)
        
        # Check Survivor progress (needs 10 days)
        survivor_progress = summary["Survivor"]
        self.assertEqual(survivor_progress["status"], "in_progress")
        self.assertEqual(survivor_progress["current"], 5)
        self.assertEqual(survivor_progress["target"], 10)
        self.assertEqual(survivor_progress["progress_percent"], 50.0)

    def test_earned_rewards_tracking(self):
        """Test tracking of earned rewards"""
        # Initially no rewards earned
        self.assertEqual(len(self.reward_system.get_earned_rewards()), 0)
        
        # Earn a reward
        self.reward_system.update_progress("evolution_count", 1, "set")
        earned = self.reward_system.get_earned_rewards()
        self.assertIn("First Evolution", earned)
        
        # Check that it's marked as earned
        summary = self.reward_system.get_progress_summary()
        self.assertEqual(summary["First Evolution"]["status"], "earned")

    def test_save_and_load_progress(self):
        """Test saving and loading progress"""
        # Set some progress
        self.reward_system.update_progress("combat_victories", 20, "set")
        self.reward_system.update_progress("unique_enemies_defeated", "Wolf", "add_to_set")
        self.reward_system.update_progress("unique_enemies_defeated", "Spider", "add_to_set")
        
        # Save progress
        save_data = self.reward_system.save_progress()
        
        # Create new system and load
        new_system = RewardSkillSystem()
        new_system.load_progress(save_data)
        
        # Check that progress was loaded correctly
        self.assertEqual(new_system.player_progress["combat_victories"], 20)
        self.assertEqual(len(new_system.player_progress["unique_enemies_defeated"]), 2)
        self.assertIn("Wolf", new_system.player_progress["unique_enemies_defeated"])
        self.assertIn("Spider", new_system.player_progress["unique_enemies_defeated"])

    def test_fusion_combinations_with_rewards(self):
        """Test fusion combinations involving reward skills"""
        fusion_combos = self.reward_system.get_fusion_combinations_with_rewards()
        
        # Check that legendary fusions exist
        self.assertIn("Ultimate Survivor", fusion_combos)
        self.assertIn("Master Explorer", fusion_combos)
        self.assertIn("War Chief", fusion_combos)
        self.assertIn("Legendary Collector", fusion_combos)
        
        # Check Ultimate Survivor fusion
        ultimate_survivor = fusion_combos["Ultimate Survivor"]
        expected_components = ["Survivor", "Veteran Survivor", "Apex Predator"]
        self.assertEqual(ultimate_survivor["components"], expected_components)
        self.assertEqual(ultimate_survivor["fusion_type"], "legendary_reward")

    def test_legendary_reward_skills(self):
        """Test legendary reward skills"""
        legendary_skills = self.reward_system.get_legendary_reward_skills()
        
        # Check that legendary skills exist
        self.assertIn("Ultimate Survivor", legendary_skills)
        self.assertIn("Master Explorer", legendary_skills)
        self.assertIn("War Chief", legendary_skills)
        self.assertIn("Legendary Collector", legendary_skills)
        
        # Check Ultimate Survivor properties
        ultimate_survivor = legendary_skills["Ultimate Survivor"]
        self.assertEqual(ultimate_survivor.skill_type, SkillType.PASSIVE)
        self.assertEqual(ultimate_survivor.rarity, SkillRarity.LEGENDARY)
        self.assertIn("legendary_survival_mastery", ultimate_survivor.effects)
        self.assertEqual(len(ultimate_survivor.fusion_components), 3)


if __name__ == '__main__':
    unittest.main()
