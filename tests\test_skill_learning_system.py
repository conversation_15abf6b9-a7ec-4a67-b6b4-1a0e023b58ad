"""
Tests for the Skill Learning System
"""
import pytest
from game.skill_learning_system import SkillLearningSystem
from game.character import Character

class TestSkillLearningSystem:
    def test_skill_learning_system_initialization(self):
        """Test skill learning system initialization"""
        learning_system = SkillLearningSystem()
        
        assert learning_system.learning_triggers is not None
        assert learning_system.action_counters == {}
        assert learning_system.learning_thresholds is not None
        
        # Check that learnable skills were added to database
        assert "Combat Mastery" in learning_system.skill_system.skill_database
        assert "Stealth" in learning_system.skill_system.skill_database
        assert "Nature Affinity" in learning_system.skill_system.skill_database
    
    def test_action_recording(self):
        """Test recording actions for skill learning"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 1,
            "creature_type": "Spider",
            "current_abilities": ["Web Spin", "Poison Bite"],
            "combat_victories": 0
        }
        
        # Record some actions
        learned_skills = learning_system.record_action("explore_forest", context)
        assert learning_system.action_counters["explore_forest"] == 1
        assert learned_skills == []  # Not enough repetitions yet
        
        # Record more actions to trigger learning
        for _ in range(4):  # Need 5 total for forest exploration
            learned_skills = learning_system.record_action("explore_forest", context)
        
        # Should learn a skill now
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Nature Affinity", "Forest Navigation"]
    
    def test_combat_victory_learning(self):
        """Test learning skills from combat victories"""
        learning_system = SkillLearningSystem()
        
        context = {
            "character_level": 3,
            "creature_type": "Goblin",
            "current_abilities": ["Tool Use", "Cunning"],
            "combat_victories": 5
        }
        
        # Should be able to learn combat skills with enough victories
        learned_skills = learning_system.record_action("combat_victory", context)
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Combat Mastery", "Battle Instinct"]
    
    def test_creature_specific_learning(self):
        """Test creature-specific skill learning"""
        learning_system = SkillLearningSystem()
        
        # Spider should be able to learn from web actions
        spider_context = {
            "character_level": 2,
            "creature_type": "Spider",
            "current_abilities": ["Web Spin", "Poison Bite"],
            "combat_victories": 0
        }
        
        # Record web usage multiple times
        for _ in range(8):  # Need 8 for web mastery
            learning_system.record_action("use_web_spin", spider_context)
        
        learned_skills = learning_system.record_action("use_web_spin", spider_context)
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Web Mastery", "Trap Expertise"]
    
    def test_action_analysis_for_learning(self):
        """Test analyzing user input for skill learning opportunities"""
        learning_system = SkillLearningSystem()
        character = Character("Test", ["brave"], "warrior")
        character.set_creature_type("Spider")
        
        # Test exploration action
        learned_skills = learning_system.analyze_action_for_learning(
            "explore the forest", character, "Mysterious Forest"
        )
        # Should not learn immediately (need repetition)
        assert len(learned_skills) == 0
        
        # Test combat action
        learned_skills = learning_system.analyze_action_for_learning(
            "attack the enemy", character, "Mysterious Forest"
        )
        assert len(learned_skills) == 0  # Need repetition
        
        # Test stealth action
        learned_skills = learning_system.analyze_action_for_learning(
            "sneak quietly", character, "Mysterious Forest"
        )
        assert len(learned_skills) == 0  # Need repetition
    
    def test_skill_learning_hints(self):
        """Test getting skill learning hints"""
        learning_system = SkillLearningSystem()
        character = Character("Test", ["brave"], "warrior")
        character.set_creature_type("Goblin")
        
        # Record some actions to get close to learning
        context = {
            "character_level": 2,
            "creature_type": "Goblin",
            "current_abilities": ["Tool Use", "Cunning"],
            "combat_victories": 2
        }
        
        # Record partial progress
        for _ in range(2):  # Need 5 for forest exploration
            learning_system.record_action("explore_forest", context)
        
        hints = learning_system.get_skill_learning_hints(character, "Mysterious Forest")
        assert len(hints) >= 0  # Should have some hints or none
        
        if hints:
            assert any("explore" in hint.lower() for hint in hints)
    
    def test_learning_conditions(self):
        """Test learning condition checking"""
        learning_system = SkillLearningSystem()
        
        # Test level requirement
        conditions = {"level": 3, "times": 5}
        context = {"character_level": 2}
        assert not learning_system._check_learning_conditions("test", conditions, context)
        
        context = {"character_level": 3}
        assert not learning_system._check_learning_conditions("test", conditions, context)  # Still need times
        
        # Test creature requirement
        conditions = {"creature": ["Spider"], "times": 3}
        context = {"creature_type": "Goblin"}
        assert not learning_system._check_learning_conditions("test", conditions, context)
        
        context = {"creature_type": "Spider"}
        learning_system.action_counters["test"] = 3
        assert learning_system._check_learning_conditions("test", conditions, context)
    
    def test_skill_filtering(self):
        """Test filtering of learnable skills"""
        learning_system = SkillLearningSystem()
        
        context = {
            "current_abilities": ["Web Spin", "Combat Mastery"]
        }
        
        skill_names = ["Combat Mastery", "Stealth", "Web Spin", "Nature Affinity"]
        filtered = learning_system._filter_learnable_skills(skill_names, context)
        
        # Should exclude already known skills
        assert "Combat Mastery" not in filtered
        assert "Web Spin" not in filtered
        assert "Stealth" in filtered
        assert "Nature Affinity" in filtered
    
    def test_serialization(self):
        """Test skill learning system serialization"""
        learning_system = SkillLearningSystem()
        
        # Add some action counters
        learning_system.action_counters = {
            "explore_forest": 3,
            "combat_victory": 2,
            "sneak_action": 1
        }
        
        # Convert to dict
        data = learning_system.to_dict()
        assert "action_counters" in data
        assert data["action_counters"]["explore_forest"] == 3
        
        # Create new system from dict
        new_system = SkillLearningSystem.from_dict(data)
        assert new_system.action_counters["explore_forest"] == 3
        assert new_system.action_counters["combat_victory"] == 2
        assert new_system.action_counters["sneak_action"] == 1
    
    def test_multiple_skill_learning_triggers(self):
        """Test that different action types trigger different skills"""
        learning_system = SkillLearningSystem()
        
        # Test social actions
        context = {
            "character_level": 2,
            "creature_type": "Goblin",
            "current_abilities": ["Tool Use"],
            "combat_victories": 0
        }
        
        # Record enough social actions
        for _ in range(5):
            learning_system.record_action("talk_to_npc", context)
        
        learned_skills = learning_system.record_action("talk_to_npc", context)
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Diplomacy", "Social Insight"]
        
        # Test stealth actions
        for _ in range(4):
            learning_system.record_action("sneak_action", context)
        
        learned_skills = learning_system.record_action("sneak_action", context)
        assert len(learned_skills) > 0
        assert learned_skills[0] in ["Stealth", "Silent Movement"]
