"""
Tests for the Skill Fusion System
"""
import pytest
from game.skill_system import Skill<PERSON>usionSystem, Skill, SkillType, SkillRarity

class TestSkillFusionSystem:
    def test_skill_fusion_system_initialization(self):
        """Test skill fusion system initialization"""
        skill_system = SkillFusionSystem()
        
        assert len(skill_system.fusion_recipes) > 0
        assert len(skill_system.skill_database) > 0
        assert "Temperature Regulation" in skill_system.fusion_recipes
        assert "Venomous Web Trap" in skill_system.fusion_recipes
    
    def test_basic_skill_creation(self):
        """Test basic skill creation and properties"""
        skill = Skill(
            name="Test Skill",
            description="A test skill",
            skill_type=SkillType.COMBAT,
            rarity=SkillRarity.BASIC,
            mp_cost=10,
            power=15,
            effects=["damage"],
            requirements={}
        )
        
        assert skill.name == "Test Skill"
        assert skill.skill_type == SkillType.COMBAT
        assert skill.rarity == SkillRarity.BASIC
        assert skill.mp_cost == 10
        assert "damage" in skill.effects
    
    def test_skill_serialization(self):
        """Test skill to_dict and from_dict methods"""
        original_skill = Skill(
            name="Serialization Test",
            description="Testing serialization",
            skill_type=SkillType.MAGIC,
            rarity=SkillRarity.RARE,
            mp_cost=20,
            power=25,
            effects=["magic", "area"],
            requirements={"level": 5}
        )
        
        skill_dict = original_skill.to_dict()
        restored_skill = Skill.from_dict(skill_dict)
        
        assert restored_skill.name == original_skill.name
        assert restored_skill.skill_type == original_skill.skill_type
        assert restored_skill.rarity == original_skill.rarity
        assert restored_skill.mp_cost == original_skill.mp_cost
        assert restored_skill.effects == original_skill.effects
    
    def test_fusion_possibility_detection(self):
        """Test detection of possible fusions"""
        skill_system = SkillFusionSystem()
        
        # Test with abilities that can fuse
        abilities_with_fusion = ["Heat Resistance", "Cold Resistance"]
        possible_fusions = skill_system.check_fusion_possibilities(abilities_with_fusion)
        
        assert "Temperature Regulation" in possible_fusions
        
        # Test with abilities that cannot fuse
        abilities_without_fusion = ["Heat Resistance", "Web Spin"]
        possible_fusions = skill_system.check_fusion_possibilities(abilities_without_fusion)
        
        assert "Temperature Regulation" not in possible_fusions
    
    def test_successful_fusion(self):
        """Test successful skill fusion"""
        skill_system = SkillFusionSystem()
        
        abilities = ["Heat Resistance", "Cold Resistance"]
        success, message, fused_skill = skill_system.perform_fusion(
            abilities, "Temperature Regulation"
        )
        
        assert success is True
        assert "Successfully fused" in message
        assert fused_skill is not None
        assert fused_skill.name == "Temperature Regulation"
        assert fused_skill.rarity == SkillRarity.FUSED
    
    def test_failed_fusion_missing_components(self):
        """Test fusion failure due to missing components"""
        skill_system = SkillFusionSystem()
        
        abilities = ["Heat Resistance"]  # Missing Cold Resistance
        success, message, fused_skill = skill_system.perform_fusion(
            abilities, "Temperature Regulation"
        )
        
        assert success is False
        assert "Missing components" in message
        assert fused_skill is None
    
    def test_creature_specific_fusion(self):
        """Test creature-specific fusion requirements"""
        skill_system = SkillFusionSystem()
        
        # Test with correct creature type
        abilities = ["Absorb", "Mimic"]
        success, message, fused_skill = skill_system.perform_fusion(
            abilities, "Perfect Copy", "Slime"
        )
        
        assert success is True
        assert fused_skill.name == "Perfect Copy"
        
        # Test with incorrect creature type
        success, message, fused_skill = skill_system.perform_fusion(
            abilities, "Perfect Copy", "Spider"
        )
        
        assert success is False
        assert "requires Slime creature type" in message
    
    def test_fusion_hints(self):
        """Test fusion hint generation"""
        skill_system = SkillFusionSystem()
        
        # Test with one component missing
        abilities = ["Heat Resistance"]
        hints = skill_system.get_fusion_hints(abilities)
        
        assert len(hints) > 0
        assert any("Cold Resistance" in hint for hint in hints)
        assert any("Temperature Regulation" in hint for hint in hints)
    
    def test_skill_database_access(self):
        """Test skill database access methods"""
        skill_system = SkillFusionSystem()
        
        # Test getting specific skill
        absorb_skill = skill_system.get_skill_info("Absorb")
        assert absorb_skill is not None
        assert absorb_skill.name == "Absorb"
        
        # Test getting skills by type
        combat_skills = skill_system.get_skills_by_type(SkillType.COMBAT)
        assert len(combat_skills) > 0
        assert all(skill.skill_type == SkillType.COMBAT for skill in combat_skills)
        
        # Test getting basic skills
        basic_skills = skill_system.get_all_basic_skills()
        assert len(basic_skills) > 0
        assert all(skill.rarity != SkillRarity.FUSED for skill in basic_skills)
    
    def test_complex_fusion_combinations(self):
        """Test multiple fusion combinations"""
        skill_system = SkillFusionSystem()
        
        # Test multiple possible fusions
        abilities = ["Web Spin", "Poison Bite", "Stealth", "Speed Boost"]
        possible_fusions = skill_system.check_fusion_possibilities(abilities)
        
        assert "Venomous Web Trap" in possible_fusions
        assert "Shadow Step" in possible_fusions
        assert len(possible_fusions) == 2
    
    def test_fusion_recipe_validation(self):
        """Test that all fusion recipes are valid"""
        skill_system = SkillFusionSystem()
        
        for fusion_name, recipe in skill_system.fusion_recipes.items():
            # Check that recipe has required fields
            assert "components" in recipe
            assert "result_skill" in recipe
            assert "fusion_type" in recipe
            
            # Check that result skill exists in database
            result_skill = skill_system.get_skill_info(recipe["result_skill"])
            assert result_skill is not None
            
            # Check that components are valid (would exist in a complete database)
            components = recipe["components"]
            assert len(components) >= 2  # Fusion requires at least 2 components
    
    def test_skill_effects_and_requirements(self):
        """Test skill effects and requirements system"""
        skill_system = SkillFusionSystem()
        
        # Test a skill with effects
        venomous_web = skill_system.get_skill_info("Venomous Web Trap")
        assert venomous_web is not None
        assert "poison_trap" in venomous_web.effects
        assert "area_damage" in venomous_web.effects
        assert venomous_web.mp_cost > 0
        
        # Test fusion components tracking
        assert venomous_web.fusion_components == ["Web Spin", "Poison Bite"]
