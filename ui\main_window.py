"""
Main window for the Me? Reincarnated? game
"""
import customtkinter as ctk
import asyncio
import threading
from typing import Any
import config
from game.game_engine import Game<PERSON>ng<PERSON>, GameState

class MainWindow:
    def __init__(self):
        """Initialize the main window"""
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Create main window
        self.root = ctk.CTk()
        self.root.title(config.GAME_TITLE)
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")

        # Bind keyboard shortcuts
        self.root.bind("<Control-n>", lambda _: self.start_new_game())
        self.root.bind("<Control-s>", lambda _: self.save_game())
        self.root.bind("<Control-l>", lambda _: self.load_game())
        self.root.bind("<Control-q>", lambda _: self.root.quit())
        self.root.bind("<F1>", lambda _: self.show_help())
        self.root.bind("<F5>", lambda _: self.quick_save())
        self.root.bind("<F9>", lambda _: self.quick_load())

        # Initialize game engine
        self.game_engine = GameEngine()

        # UI Components
        self.game_frame: Any = None
        self.game_text: Any = None
        self.input_frame: Any = None
        self.input_entry: Any = None
        self.submit_button: Any = None
        self.status_frame: Any = None
        self.status_title: Any = None
        self.stats_frame: Any = None
        self.hp_label: Any = None
        self.hp_progress: Any = None
        self.hp_text: Any = None
        self.mp_label: Any = None
        self.mp_progress: Any = None
        self.mp_text: Any = None
        self.xp_label: Any = None
        self.xp_progress: Any = None
        self.xp_text: Any = None
        self.status_text: Any = None
        self.menu_frame: Any = None
        self.new_game_button: Any = None
        self.save_button: Any = None
        self.load_button: Any = None
        self.setup_ui()

        # Event loop for async operations
        self.loop = None
        self.setup_async_loop()

    def setup_async_loop(self):
        """Setup asyncio event loop for the UI thread"""
        def run_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()

        self.loop_thread = threading.Thread(target=run_loop, daemon=True)
        self.loop_thread.start()

    def setup_ui(self):
        """Setup the user interface"""
        # Configure grid weights
        self.root.grid_columnconfigure(0, weight=3)
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # Main game area (left side)
        self.setup_game_area()

        # Status panel (right side)
        self.setup_status_panel()

        # Show main menu initially
        self.show_main_menu()

    def setup_game_area(self):
        """Setup the main game display area"""
        # Game frame
        self.game_frame = ctk.CTkFrame(self.root)
        self.game_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.game_frame.grid_rowconfigure(0, weight=1)
        self.game_frame.grid_rowconfigure(1, weight=0)
        self.game_frame.grid_columnconfigure(0, weight=1)

        # Game text display
        self.game_text = ctk.CTkTextbox(
            self.game_frame,
            font=(config.FONT_FAMILY, config.FONT_SIZE),
            wrap="word"
        )
        self.game_text.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # Input frame
        self.input_frame = ctk.CTkFrame(self.game_frame)
        self.input_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        self.input_frame.grid_columnconfigure(0, weight=1)

        # Input entry
        self.input_entry = ctk.CTkEntry(
            self.input_frame,
            placeholder_text="Enter your action...",
            font=(config.FONT_FAMILY, config.FONT_SIZE)
        )
        self.input_entry.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        self.input_entry.bind("<Return>", self.on_input_submit)

        # Submit button
        self.submit_button = ctk.CTkButton(
            self.input_frame,
            text="Submit",
            command=self.on_input_submit
        )
        self.submit_button.grid(row=0, column=1, padx=(0, 10), pady=10)

    def setup_status_panel(self):
        """Setup the status panel"""
        # Status frame
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.grid(row=0, column=1, padx=(0, 10), pady=10, sticky="nsew")
        self.status_frame.grid_rowconfigure(1, weight=1)
        self.status_frame.grid_columnconfigure(0, weight=1)

        # Title
        self.status_title = ctk.CTkLabel(
            self.status_frame,
            text="Character Status",
            font=(config.FONT_FAMILY, config.FONT_SIZE + 2, "bold")
        )
        self.status_title.grid(row=0, column=0, padx=10, pady=10)

        # Character stats with progress bars
        self.stats_frame = ctk.CTkFrame(self.status_frame)
        self.stats_frame.grid(row=1, column=0, padx=10, pady=(0, 5), sticky="ew")
        self.stats_frame.grid_columnconfigure(1, weight=1)

        # HP Bar
        self.hp_label = ctk.CTkLabel(self.stats_frame, text="HP:", font=(config.FONT_FAMILY, config.FONT_SIZE - 1))
        self.hp_label.grid(row=0, column=0, padx=(10, 5), pady=2, sticky="w")

        self.hp_progress = ctk.CTkProgressBar(self.stats_frame)
        self.hp_progress.grid(row=0, column=1, padx=(0, 5), pady=2, sticky="ew")
        self.hp_progress.set(1.0)

        self.hp_text = ctk.CTkLabel(self.stats_frame, text="50/50", font=(config.FONT_FAMILY, config.FONT_SIZE - 2))
        self.hp_text.grid(row=0, column=2, padx=(0, 10), pady=2, sticky="e")

        # MP Bar
        self.mp_label = ctk.CTkLabel(self.stats_frame, text="MP:", font=(config.FONT_FAMILY, config.FONT_SIZE - 1))
        self.mp_label.grid(row=1, column=0, padx=(10, 5), pady=2, sticky="w")

        self.mp_progress = ctk.CTkProgressBar(self.stats_frame)
        self.mp_progress.grid(row=1, column=1, padx=(0, 5), pady=2, sticky="ew")
        self.mp_progress.set(1.0)

        self.mp_text = ctk.CTkLabel(self.stats_frame, text="30/30", font=(config.FONT_FAMILY, config.FONT_SIZE - 2))
        self.mp_text.grid(row=1, column=2, padx=(0, 10), pady=2, sticky="e")

        # XP Bar
        self.xp_label = ctk.CTkLabel(self.stats_frame, text="XP:", font=(config.FONT_FAMILY, config.FONT_SIZE - 1))
        self.xp_label.grid(row=2, column=0, padx=(10, 5), pady=2, sticky="w")

        self.xp_progress = ctk.CTkProgressBar(self.stats_frame)
        self.xp_progress.grid(row=2, column=1, padx=(0, 5), pady=2, sticky="ew")
        self.xp_progress.set(0.0)

        self.xp_text = ctk.CTkLabel(self.stats_frame, text="0/100", font=(config.FONT_FAMILY, config.FONT_SIZE - 2))
        self.xp_text.grid(row=2, column=2, padx=(0, 10), pady=2, sticky="e")

        # Detailed status display
        self.status_text = ctk.CTkTextbox(
            self.status_frame,
            font=(config.FONT_FAMILY, config.FONT_SIZE - 1),
            wrap="word",
            height=200
        )
        self.status_text.grid(row=2, column=0, padx=10, pady=(5, 10), sticky="nsew")

        # Menu buttons frame
        self.menu_frame = ctk.CTkFrame(self.status_frame)
        self.menu_frame.grid(row=3, column=0, padx=10, pady=(0, 10), sticky="ew")
        self.menu_frame.grid_columnconfigure(0, weight=1)

        # Menu buttons
        self.new_game_button = ctk.CTkButton(
            self.menu_frame,
            text="New Game",
            command=self.start_new_game
        )
        self.new_game_button.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        self.save_button = ctk.CTkButton(
            self.menu_frame,
            text="Save Game",
            command=self.save_game
        )
        self.save_button.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        self.load_button = ctk.CTkButton(
            self.menu_frame,
            text="Load Game",
            command=self.load_game
        )
        self.load_button.grid(row=2, column=0, padx=10, pady=5, sticky="ew")

    def show_main_menu(self):
        """Display the main menu"""
        menu_text = f"""
Welcome to {config.GAME_TITLE}!

An isekai text adventure where you are reincarnated as a creature in a fantasy world.

• Start a new adventure
• Continue your journey
• Evolve and grow stronger
• Build relationships and kingdoms

Choose an option from the menu on the right to begin!
        """.strip()

        self.display_text(menu_text)
        self.update_status("Ready to begin your new life!")

    def display_text(self, text: str, append: bool = False):
        """Display text in the game area"""
        if append:
            self.game_text.insert("end", f"\n\n{text}")
        else:
            self.game_text.delete("1.0", "end")
            self.game_text.insert("1.0", text)

        # Auto-scroll to bottom
        self.game_text.see("end")

    def update_status(self, status: str):
        """Update the status panel"""
        self.status_text.delete("1.0", "end")
        self.status_text.insert("1.0", status)

    def start_new_game(self):
        """Start a new game"""
        def async_start():
            future = asyncio.run_coroutine_threadsafe(
                self.game_engine.start_new_game(), self.loop
            )
            response = future.result()
            self.root.after(0, lambda: self.display_text(response))
            self.root.after(0, self.update_character_status)

        threading.Thread(target=async_start, daemon=True).start()

    def save_game(self):
        """Save the current game"""
        if self.game_engine.state == GameState.MENU:
            self.display_text("No game to save!", append=True)
            return

        success = self.game_engine.save_game()
        if success:
            self.display_text("Game saved successfully!", append=True)
        else:
            self.display_text("Failed to save game.", append=True)

    def load_game(self):
        """Load a saved game"""
        # For now, try to load autosave
        game_state = self.game_engine.save_system.load_auto_save()
        if game_state:
            success = self.game_engine.load_game_state(game_state)
            if success:
                self.display_text("Game loaded successfully!")
                self.update_character_status()
            else:
                self.display_text("Failed to load game.")
        else:
            self.display_text("No saved game found.")

    def on_input_submit(self, _=None):
        """Handle input submission"""
        user_input = self.input_entry.get().strip()
        if not user_input:
            return

        # Clear input
        self.input_entry.delete(0, "end")

        # Display user input
        self.display_text(f"\n> {user_input}", append=True)

        # Process input based on game state
        def async_process():
            try:
                if self.game_engine.state == GameState.CHARACTER_CREATION:
                    future = asyncio.run_coroutine_threadsafe(
                        self.game_engine.process_character_creation(user_input), self.loop
                    )
                elif self.game_engine.state == GameState.PLAYING:
                    future = asyncio.run_coroutine_threadsafe(
                        self.game_engine.process_game_action(user_input), self.loop
                    )
                else:
                    self.root.after(0, lambda: self.display_text("Game not ready for input.", append=True))
                    return

                response = future.result()
                self.root.after(0, lambda: self.display_text(f"\n{response}", append=True))
                self.root.after(0, self.update_character_status)

            except Exception as e: # Consider catching more specific exceptions
                self.root.after(0, lambda: self.display_text(f"\nError: {e}", append=True))

        threading.Thread(target=async_process, daemon=True).start()

    def update_character_status(self):
        """Update the character status display"""
        character = self.game_engine.character

        if character.name:
            # Update progress bars
            if character.stats.get("max_hp", 0) > 0:
                hp_ratio = character.stats["hp"] / character.stats["max_hp"]
                self.hp_progress.set(hp_ratio)
                self.hp_text.configure(text=f"{character.stats['hp']}/{character.stats['max_hp']}")

                # Color coding for HP
                if hp_ratio > 0.7:
                    self.hp_progress.configure(progress_color="green")
                elif hp_ratio > 0.3:
                    self.hp_progress.configure(progress_color="orange")
                else:
                    self.hp_progress.configure(progress_color="red")

            if character.stats.get("max_mp", 0) > 0:
                mp_ratio = character.stats["mp"] / character.stats["max_mp"]
                self.mp_progress.set(mp_ratio)
                self.mp_text.configure(text=f"{character.stats['mp']}/{character.stats['max_mp']}")

            # XP Progress
            exp_needed = character.level * 100
            if exp_needed > 0:
                xp_ratio = character.experience / exp_needed
                self.xp_progress.set(xp_ratio)
                self.xp_text.configure(text=f"{character.experience}/{exp_needed}")

        # Update detailed status
        status = self.game_engine.get_character_status()
        self.update_status(status)

    def show_help(self):
        """Show help dialog with keyboard shortcuts"""
        help_text = """
Keyboard Shortcuts:
• Ctrl+N: New Game
• Ctrl+S: Save Game
• Ctrl+L: Load Game
• F1: Show Help
• F5: Quick Save
• F9: Quick Load
• Ctrl+Q: Quit Game
• Enter: Submit Action

Game Commands:
• explore, look, search - Investigate your surroundings
• attack [target] - Engage in combat
• talk to [npc] - Interact with characters
• use [skill/item] - Use abilities or items
• move to [location] - Travel to connected areas
• inventory - Check your items
• status - View character information
        """.strip()

        # Create help window
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("Help - Me? Reincarnated?")
        help_window.geometry("500x400")
        help_window.transient(self.root)
        help_window.grab_set()

        help_textbox = ctk.CTkTextbox(help_window, wrap="word")
        help_textbox.pack(fill="both", expand=True, padx=20, pady=20)
        help_textbox.insert("1.0", help_text)
        help_textbox.configure(state="disabled")

        close_button = ctk.CTkButton(help_window, text="Close", command=help_window.destroy)
        close_button.pack(pady=(0, 20))

    def quick_save(self):
        """Perform a quick save"""
        if self.game_engine.state == GameState.MENU:
            self.display_text("No game to save!", append=True)
            return

        success = self.game_engine.save_game("quicksave")
        if success:
            self.display_text("⚡ Quick saved!", append=True)
        else:
            self.display_text("❌ Quick save failed!", append=True)

    def quick_load(self):
        """Perform a quick load"""
        success = self.game_engine.load_game("quicksave")
        if success:
            self.display_text("⚡ Quick loaded!")
            self.update_character_status()
        else:
            self.display_text("❌ No quick save found!")

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        finally:
            if self.loop:
                self.loop.call_soon_threadsafe(self.loop.stop)
